# 书籍列表编辑器API使用指南

## 概述

书籍列表编辑器API (`/content/book/list/editor`) 是专为编辑器界面设计的书籍管理API，提供了丰富的筛选、分页和排序功能，以及完整的书籍配置信息和权限控制字段。

## API基本信息

- **端点**: `/story_v2/api/content/book/list/editor`
- **方法**: GET
- **认证**: 需要有效的API Token
- **版本**: 支持V1和V2

## 导入和基本使用

### 导入必要的类

```python
from gstudio_api import (
    APIEndpoints,
    APIVersion,
    BookListEditorParams,
    BookListEditorResponse,
    BookEditorInfo,
    APIResponse,
    DefaultHeaders
)
```

### 基础查询示例

```python
# 创建基本查询参数
params = BookListEditorParams(
    page_size=50,
    page_no=1
)

# 生成API URL
url = APIEndpoints.get_url(APIEndpoints.Book.LIST_EDITOR)
print(f"API URL: {url}")
# 输出: https://www.gstudios.com.cn/story_v2/api/content/book/list/editor
```

### 高级筛选查询

```python
# 创建带筛选条件的查询参数
advanced_params = BookListEditorParams(
    page_size=20,
    page_no=1,
    name="小说",              # 按书籍名称筛选
    remark="重要",            # 按备注筛选
    finished=False,          # 只显示未完成的书籍
    sort_item="createdTime", # 按创建时间排序
    sort_asc=False,          # 降序排列
    total=100               # 总数（用于分页计算）
)
```

## 查询参数详解

### 必填参数

| 参数名 | 类型 | 说明 | 示例 |
|--------|------|------|------|
| `page_size` | int | 每页显示的记录数 | 50 |
| `page_no` | int | 页码（从1开始） | 1 |

### 可选参数

| 参数名 | 类型 | 说明 | 示例 |
|--------|------|------|------|
| `name` | str | 书籍名称筛选（支持模糊匹配） | "战国明月" |
| `remark` | str | 备注筛选（支持模糊匹配） | "小南瓜画本" |
| `finished` | bool | 完成状态筛选 | true/false |
| `total` | int | 总记录数（用于分页计算） | 100 |
| `sort_item` | str | 排序字段 | "name", "createdTime", "updatedTime" |
| `sort_asc` | bool | 排序方向（true=升序，false=降序） | true/false |

## 响应数据结构

### 标准API响应格式

```python
{
    "code": 1,                    # 响应状态码（1=成功）
    "msg": "成功!",               # 响应消息
    "data": {                     # 响应数据
        "list": [...],            # 书籍列表
        "total": 10               # 总记录数
    }
}
```

### 书籍对象字段详解

每个书籍对象包含以下字段：

```python
{
    "id": 33964,                      # 书籍ID
    "name": "战国明月",               # 书籍名称
    "description": "",                # 书籍描述
    "finished": false,                # 是否完成
    "remark": "小南瓜画本",           # 备注信息
    "auditionRateLimit": 3,           # 试音频率限制
    "auditionShowCv": false,          # 是否显示CV试音
    "communityVisible": true,         # 社区可见性
    "createdTime": 1743654555189,     # 创建时间（Unix时间戳）
    "updatedTime": 1743654555254,     # 更新时间（Unix时间戳）
    "pinned": true,                   # 是否置顶
    "readonly": false,                # 是否只读
    "teamName": "",                   # 团队名称
    "useMasterComp": true,            # 使用主压缩
    "useVoiceComp": true,             # 使用语音压缩
    "useVoiceDenoise": true,          # 使用语音降噪
    "userCanAudit": false,            # 用户可审核
    "userCanDelete": true,            # 用户可删除
    "userCanEdit": true,              # 用户可编辑
    "vadSensitivity": 0               # VAD敏感度
}
```

## 完整使用示例

### 示例1：基础分页查询

```python
import requests
from gstudio_api import APIEndpoints, BookListEditorParams, DefaultHeaders

# 配置
token = "your_api_token_here"
headers = DefaultHeaders.get_headers(token=token)

# 创建查询参数
params = BookListEditorParams(
    page_size=50,
    page_no=1
)

# 构建查询字符串
query_params = {
    "pageSize": params.page_size,
    "pageNo": params.page_no
}

# 发送请求
url = APIEndpoints.get_url(APIEndpoints.Book.LIST_EDITOR)
response = requests.get(url, headers=headers, params=query_params)

# 处理响应
if response.status_code == 200:
    data = response.json()
    if data["code"] == 1:
        books = data["data"]["list"]
        total = data["data"]["total"]
        print(f"找到 {total} 本书籍，当前页显示 {len(books)} 本")
        
        for book in books:
            print(f"- {book['name']} (ID: {book['id']})")
    else:
        print(f"API错误: {data['msg']}")
else:
    print(f"HTTP错误: {response.status_code}")
```

### 示例2：高级筛选查询

```python
# 创建高级筛选参数
advanced_params = BookListEditorParams(
    page_size=20,
    page_no=1,
    name="小说",
    finished=False,
    sort_item="updatedTime",
    sort_asc=False
)

# 构建完整的查询参数
query_params = {
    "pageSize": advanced_params.page_size,
    "pageNo": advanced_params.page_no,
    "name": advanced_params.name or "",
    "finished": str(advanced_params.finished).lower() if advanced_params.finished is not None else "",
    "sortItem": advanced_params.sort_item or "",
    "sortAsc": str(advanced_params.sort_asc).lower() if advanced_params.sort_asc is not None else ""
}

# 发送请求
response = requests.get(url, headers=headers, params=query_params)
```

### 示例3：响应数据处理

```python
from gstudio_api import BookListEditorResponse, BookEditorInfo

# 处理API响应
def process_book_list_response(response_data):
    """处理书籍列表响应数据"""
    
    # 创建响应对象
    book_response = BookListEditorResponse(
        list=response_data["data"]["list"],
        total=response_data["data"]["total"]
    )
    
    # 处理每本书籍
    books = []
    for book_data in book_response.list:
        # 创建书籍信息对象
        book = BookEditorInfo(
            id=book_data["id"],
            name=book_data["name"],
            description=book_data["description"],
            finished=book_data["finished"],
            remark=book_data["remark"],
            audition_rate_limit=book_data["auditionRateLimit"],
            audition_show_cv=book_data["auditionShowCv"],
            community_visible=book_data["communityVisible"],
            created_time=book_data["createdTime"],
            updated_time=book_data["updatedTime"],
            pinned=book_data["pinned"],
            readonly=book_data["readonly"],
            team_name=book_data["teamName"],
            use_master_comp=book_data["useMasterComp"],
            use_voice_comp=book_data["useVoiceComp"],
            use_voice_denoise=book_data["useVoiceDenoise"],
            user_can_audit=book_data["userCanAudit"],
            user_can_delete=book_data["userCanDelete"],
            user_can_edit=book_data["userCanEdit"],
            vad_sensitivity=book_data["vadSensitivity"]
        )
        books.append(book)
    
    return books, book_response.total

# 使用示例
if response.status_code == 200:
    data = response.json()
    if data["code"] == 1:
        books, total = process_book_list_response(data)
        print(f"处理了 {len(books)} 本书籍，总共 {total} 本")
```

## 错误处理

### 常见错误码

| 错误码 | 说明 | 处理方法 |
|--------|------|----------|
| 401 | 未授权（Token无效） | 检查并更新API Token |
| 403 | 禁止访问（权限不足） | 确认账户权限 |
| 404 | 端点不存在 | 检查API路径 |
| 500 | 服务器内部错误 | 稍后重试或联系支持 |

### 错误处理示例

```python
def safe_get_book_list(params, max_retries=3):
    """安全的书籍列表获取，包含重试机制"""
    
    for attempt in range(max_retries):
        try:
            response = requests.get(url, headers=headers, params=query_params, timeout=30)
            
            if response.status_code == 200:
                data = response.json()
                if data["code"] == 1:
                    return data["data"]
                else:
                    print(f"API错误: {data['msg']}")
                    return None
            elif response.status_code == 401:
                print("认证失败，请检查API Token")
                return None
            else:
                print(f"HTTP错误 {response.status_code}，尝试重试...")
                
        except requests.exceptions.RequestException as e:
            print(f"网络错误: {e}")
            
        if attempt < max_retries - 1:
            time.sleep(2 ** attempt)  # 指数退避
    
    print("达到最大重试次数，请求失败")
    return None
```

## 最佳实践

### 1. 分页处理

```python
def get_all_books(name_filter=None, finished_filter=None):
    """获取所有符合条件的书籍"""
    
    all_books = []
    page_no = 1
    page_size = 50
    
    while True:
        params = BookListEditorParams(
            page_size=page_size,
            page_no=page_no,
            name=name_filter,
            finished=finished_filter
        )
        
        # 发送请求
        data = safe_get_book_list(params)
        if not data:
            break
            
        books = data["list"]
        all_books.extend(books)
        
        # 检查是否还有更多页
        if len(books) < page_size:
            break
            
        page_no += 1
    
    return all_books
```

### 2. 缓存机制

```python
import time
from functools import lru_cache

@lru_cache(maxsize=128)
def cached_get_book_list(page_size, page_no, name, finished, cache_time):
    """带缓存的书籍列表获取"""
    # 实际的API调用逻辑
    pass

# 使用缓存（5分钟有效期）
cache_time = int(time.time() // 300)  # 5分钟间隔
books = cached_get_book_list(50, 1, "小说", False, cache_time)
```

### 3. 类型安全

```python
from typing import List, Optional

def filter_editable_books(books: List[BookEditorInfo]) -> List[BookEditorInfo]:
    """筛选可编辑的书籍"""
    return [book for book in books if book.user_can_edit and not book.readonly]

def filter_by_team(books: List[BookEditorInfo], team_name: str) -> List[BookEditorInfo]:
    """按团队筛选书籍"""
    return [book for book in books if book.team_name == team_name]
```

## 总结

书籍列表编辑器API提供了强大的书籍管理功能，支持：

- ✅ 灵活的筛选条件（名称、备注、完成状态）
- ✅ 完整的分页和排序功能
- ✅ 丰富的书籍配置信息
- ✅ 详细的权限控制字段
- ✅ 类型安全的数据结构

通过合理使用这些功能，可以构建强大的书籍管理界面和工作流。
