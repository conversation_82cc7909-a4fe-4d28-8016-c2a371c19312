#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
配置管理模块

提供应用程序的配置管理功能，支持从配置文件加载和保存配置。

作者：Augment Agent
版本：1.0.0
"""

import os
import json
import logging
from typing import Any, Dict, Optional
from pathlib import Path


class AppConfig:
    """应用程序配置管理类"""
    
    def __init__(self, config_file: Optional[str] = None):
        """
        初始化配置管理器
        
        Args:
            config_file: 配置文件路径，如果为None则使用默认路径
        """
        self.logger = logging.getLogger(__name__)
        
        # 确定配置文件路径
        if config_file is None:
            config_dir = Path(__file__).parent
            config_file = config_dir / "config.json"
        
        self.config_file = Path(config_file)
        self._config = {}
        
        # 加载默认配置
        self._load_default_config()
        
        # 尝试加载用户配置
        self.load_config()
    
    def _load_default_config(self):
        """加载默认配置"""
        self._config = {
            # API配置
            "api": {
                "base_url": "https://www.gstudios.com.cn",
                "version": "story_v2",
                "timeout": 60,
                "max_retries": 5,
                "retry_delay": 2.0,
                "request_interval": 0.2,  # 请求间隔时间（秒），防止连接重置
                "token": "",
                "verify_ssl": True,
                "ssl_auto_fix": True
            },
            
            # 文件路径配置
            "paths": {
                "audio_files": "audio_files",
                "logs": "logs",
                "temp": "temp"
            },
            
            # 日志配置
            "logging": {
                "level": "INFO",
                "format": "%(asctime)s - %(name)s - %(levelname)s - %(message)s",
                "file_max_size": 10485760,  # 10MB
                "backup_count": 5
            },
            
            # GUI配置
            "gui": {
                "window_width": 1200,
                "window_height": 800,
                "theme": "default"
            },
            
            # 音频处理配置
            "audio": {
                "default_cv_robot_id": 568,
                "default_speed_factor": 100,
                "default_silence_factor": 100,
                "supported_formats": ["mp3", "wav"],
                "max_file_size": 52428800  # 50MB
            },
            
            # 筛选配置
            "filter": {
                "target_types": ["旁白"],  # 目标段落类型
                "target_cv_type": 0,       # 目标CV类型：0=机器人
                "batch_size": 10           # 批处理大小
            }
        }
    
    def load_config(self) -> bool:
        """
        从文件加载配置
        
        Returns:
            bool: 加载成功返回True，否则返回False
        """
        try:
            if self.config_file.exists():
                with open(self.config_file, 'r', encoding='utf-8') as f:
                    user_config = json.load(f)
                
                # 递归合并配置
                self._merge_config(self._config, user_config)
                self.logger.info(f"配置文件加载成功: {self.config_file}")
                return True
            else:
                self.logger.info("配置文件不存在，使用默认配置")
                # 创建默认配置文件
                self.save_config()
                return False
                
        except Exception as e:
            self.logger.error(f"加载配置文件失败: {e}")
            return False
    
    def save_config(self) -> bool:
        """
        保存配置到文件
        
        Returns:
            bool: 保存成功返回True，否则返回False
        """
        try:
            # 确保配置目录存在
            self.config_file.parent.mkdir(parents=True, exist_ok=True)
            
            with open(self.config_file, 'w', encoding='utf-8') as f:
                json.dump(self._config, f, indent=4, ensure_ascii=False)
            
            self.logger.info(f"配置文件保存成功: {self.config_file}")
            return True
            
        except Exception as e:
            self.logger.error(f"保存配置文件失败: {e}")
            return False
    
    def get(self, key: str, default: Any = None) -> Any:
        """
        获取配置值
        
        Args:
            key: 配置键，支持点号分隔的嵌套键，如 'api.base_url'
            default: 默认值
            
        Returns:
            配置值
        """
        try:
            keys = key.split('.')
            value = self._config
            
            for k in keys:
                value = value[k]
            
            return value
            
        except (KeyError, TypeError):
            return default
    
    def set(self, key: str, value: Any) -> None:
        """
        设置配置值
        
        Args:
            key: 配置键，支持点号分隔的嵌套键
            value: 配置值
        """
        keys = key.split('.')
        config = self._config
        
        # 导航到最后一级的父级
        for k in keys[:-1]:
            if k not in config:
                config[k] = {}
            config = config[k]
        
        # 设置值
        config[keys[-1]] = value
    
    def _merge_config(self, base: Dict, update: Dict) -> None:
        """
        递归合并配置字典
        
        Args:
            base: 基础配置字典
            update: 更新配置字典
        """
        for key, value in update.items():
            if key in base and isinstance(base[key], dict) and isinstance(value, dict):
                self._merge_config(base[key], value)
            else:
                base[key] = value
    
    def get_api_config(self) -> Dict[str, Any]:
        """获取API配置"""
        return self.get('api', {})
    
    def get_paths_config(self) -> Dict[str, str]:
        """获取路径配置"""
        return self.get('paths', {})
    
    def get_logging_config(self) -> Dict[str, Any]:
        """获取日志配置"""
        return self.get('logging', {})
    
    def get_gui_config(self) -> Dict[str, Any]:
        """获取GUI配置"""
        return self.get('gui', {})
    
    def get_audio_config(self) -> Dict[str, Any]:
        """获取音频配置"""
        return self.get('audio', {})
    
    def get_filter_config(self) -> Dict[str, Any]:
        """获取筛选配置"""
        return self.get('filter', {})
    
    def validate_config(self) -> bool:
        """
        验证配置的有效性
        
        Returns:
            bool: 配置有效返回True，否则返回False
        """
        try:
            # 验证必需的配置项
            required_keys = [
                'api.base_url',
                'api.version',
                'paths.audio_files',
                'logging.level'
            ]
            
            for key in required_keys:
                if self.get(key) is None:
                    self.logger.error(f"缺少必需的配置项: {key}")
                    return False
            
            # 验证API URL格式
            base_url = self.get('api.base_url')
            if not base_url.startswith(('http://', 'https://')):
                self.logger.error(f"无效的API URL格式: {base_url}")
                return False
            
            return True

        except Exception as e:
            self.logger.error(f"配置验证失败: {e}")
            return False

    # SSL配置相关方法
    def is_ssl_verification_enabled(self) -> bool:
        """检查是否启用SSL验证"""
        return self.get('api.verify_ssl', True)

    def enable_ssl_verification(self) -> None:
        """启用SSL验证"""
        self.set('api.verify_ssl', True)
        self.save_config()
        self.logger.info("SSL验证已启用")

    def disable_ssl_verification(self) -> None:
        """禁用SSL验证"""
        self.set('api.verify_ssl', False)
        self.save_config()
        self.logger.warning("SSL验证已禁用，仅适用于测试环境")

    def is_ssl_auto_fix_enabled(self) -> bool:
        """检查是否启用SSL自动修复"""
        return self.get('api.ssl_auto_fix', True)

    def set_ssl_auto_fix(self, enabled: bool) -> None:
        """设置SSL自动修复"""
        self.set('api.ssl_auto_fix', enabled)
        self.save_config()
        self.logger.info(f"SSL自动修复已{'启用' if enabled else '禁用'}")

    def get_ssl_config(self) -> Dict[str, Any]:
        """获取SSL配置信息"""
        return {
            'verify_ssl': self.is_ssl_verification_enabled(),
            'ssl_auto_fix': self.is_ssl_auto_fix_enabled(),
            'base_url': self.get('api.base_url', ''),
        }
