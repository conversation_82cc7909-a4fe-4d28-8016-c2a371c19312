#!/usr/bin/env python3
"""
测试音频上传API功能
验证新添加的音频文件上传相关功能
"""

import sys
import os

# 添加simple-gstudio-api目录到Python路径
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'simple-gstudio-api'))

try:
    from gstudio_api import (
        APIEndpoints,
        VoiceUploadParams,
        VoiceUploadResponse,
        APIVersion
    )
    print("✓ 成功导入所有音频上传相关的类")
except ImportError as e:
    print(f"✗ 导入失败: {e}")
    sys.exit(1)

def test_voice_upload_api_endpoint():
    """测试音频上传API端点"""
    print("\n测试音频上传API端点...")
    
    try:
        # 测试音频上传API端点
        voice_upload_url = APIEndpoints.get_url(APIEndpoints.Material.VOICE_UPLOAD)
        expected_upload_url = "https://www.gstudios.com.cn/story_v2/api/material/voice/upload"
        assert voice_upload_url == expected_upload_url
        print(f"✓ 音频上传端点正确: {voice_upload_url}")
        
    except Exception as e:
        print(f"✗ 音频上传API端点测试失败: {e}")
        return False
    
    return True

def test_voice_upload_params():
    """测试音频上传参数类"""
    print("\n测试音频上传参数类...")
    
    try:
        # 测试基本参数创建
        upload_params = VoiceUploadParams(
            file="path/to/audio.mp3",
            cueId=691699346
        )
        assert upload_params.file == "path/to/audio.mp3"
        assert upload_params.cueId == 691699346
        assert upload_params.filename is None
        print(f"✓ 基本参数创建成功: file={upload_params.file}, cueId={upload_params.cueId}")
        
        # 测试完整参数创建（与实际API调用记录一致）
        upload_params_full = VoiceUploadParams(
            file="曹先森MK-III.mp3",
            cueId=691699346,
            filename="曹先森MK-III.mp3"
        )
        assert upload_params_full.file == "曹先森MK-III.mp3"
        assert upload_params_full.cueId == 691699346
        assert upload_params_full.filename == "曹先森MK-III.mp3"
        print(f"✓ 完整参数创建成功（与API调用记录一致）")
        
    except Exception as e:
        print(f"✗ 音频上传参数类测试失败: {e}")
        return False
    
    return True

def test_voice_upload_response():
    """测试音频上传响应类"""
    print("\n测试音频上传响应类...")
    
    try:
        # 测试响应数据类创建（与实际API响应一致）
        response_data = VoiceUploadResponse(
            content="甘泉攻。",
            durationEffectiveMs=950,
            durationTotalMs=1360,
            leftBlankHeadMs=210,
            leftBlankTailMs=200,
            materialId=332540673,
            snrDb=43.383324
        )
        
        assert response_data.content == "甘泉攻。"
        assert response_data.durationEffectiveMs == 950
        assert response_data.durationTotalMs == 1360
        assert response_data.leftBlankHeadMs == 210
        assert response_data.leftBlankTailMs == 200
        assert response_data.materialId == 332540673
        assert response_data.snrDb == 43.383324
        
        print(f"✓ 响应数据类创建成功")
        print(f"  content: {response_data.content}")
        print(f"  durationEffectiveMs: {response_data.durationEffectiveMs}")
        print(f"  durationTotalMs: {response_data.durationTotalMs}")
        print(f"  materialId: {response_data.materialId}")
        print(f"  snrDb: {response_data.snrDb}")
        
    except Exception as e:
        print(f"✗ 音频上传响应类测试失败: {e}")
        return False
    
    return True

def test_api_version_compatibility():
    """测试API版本兼容性"""
    print("\n测试API版本兼容性...")
    
    try:
        # 测试V2版本（默认）
        v2_upload_url = APIEndpoints.get_url(APIEndpoints.Material.VOICE_UPLOAD)
        assert "story_v2" in v2_upload_url
        print(f"✓ V2版本音频上传API正确: {v2_upload_url}")
        
        # 测试V1版本
        v1_upload_url = APIEndpoints.get_url(APIEndpoints.Material.VOICE_UPLOAD, APIVersion.V1)
        assert "story" in v1_upload_url and "story_v2" not in v1_upload_url
        print(f"✓ V1版本音频上传API正确: {v1_upload_url}")
        
    except Exception as e:
        print(f"✗ API版本兼容性测试失败: {e}")
        return False
    
    return True

def test_consistency_with_api_log():
    """测试与API调用记录的一致性"""
    print("\n测试与API调用记录的一致性...")
    
    try:
        # 验证API路径与调用记录一致
        expected_path = "/material/voice/upload"
        actual_path = APIEndpoints.Material.VOICE_UPLOAD
        assert actual_path == expected_path
        print(f"✓ API路径与调用记录一致: {actual_path}")
        
        # 验证参数与调用记录一致
        # 实际调用记录中的参数：file="曹先森MK-III.mp3", cueId=691699346
        upload_params = VoiceUploadParams(
            file="曹先森MK-III.mp3",
            cueId=691699346,
            filename="曹先森MK-III.mp3"
        )
        assert upload_params.cueId == 691699346
        print(f"✓ 参数与调用记录一致: cueId={upload_params.cueId}")
        
        # 验证响应字段与调用记录一致
        # 实际响应中的字段：content, durationEffectiveMs, durationTotalMs, etc.
        response_fields = [
            'content', 'durationEffectiveMs', 'durationTotalMs',
            'leftBlankHeadMs', 'leftBlankTailMs', 'materialId', 'snrDb'
        ]
        
        response_data = VoiceUploadResponse(
            content="甘泉攻。",
            durationEffectiveMs=950,
            durationTotalMs=1360,
            leftBlankHeadMs=210,
            leftBlankTailMs=200,
            materialId=332540673,
            snrDb=43.383324
        )
        
        for field in response_fields:
            assert hasattr(response_data, field)
        
        print(f"✓ 响应字段与调用记录完全一致")
        
    except Exception as e:
        print(f"✗ 与API调用记录一致性测试失败: {e}")
        return False
    
    return True

def test_multipart_form_data_structure():
    """测试multipart/form-data结构理解"""
    print("\n测试multipart/form-data结构理解...")
    
    try:
        # 模拟构建multipart/form-data请求
        upload_params = VoiceUploadParams(
            file="曹先森MK-III.mp3",
            cueId=691699346,
            filename="曹先森MK-III.mp3"
        )
        
        # 模拟requests库的files和data参数
        files_dict = {
            'file': (upload_params.filename or 'audio.mp3', 'binary_data_here', 'audio/mpeg')
        }
        data_dict = {
            'cueId': upload_params.cueId
        }
        
        assert 'file' in files_dict
        assert 'cueId' in data_dict
        assert data_dict['cueId'] == 691699346
        
        print(f"✓ multipart/form-data结构理解正确")
        print(f"  files: {list(files_dict.keys())}")
        print(f"  data: {data_dict}")
        
    except Exception as e:
        print(f"✗ multipart/form-data结构测试失败: {e}")
        return False
    
    return True

def main():
    """主测试函数"""
    print("开始测试音频上传API功能...")
    
    tests = [
        test_voice_upload_api_endpoint,
        test_voice_upload_params,
        test_voice_upload_response,
        test_api_version_compatibility,
        test_consistency_with_api_log,
        test_multipart_form_data_structure
    ]
    
    passed = 0
    total = len(tests)
    
    for test in tests:
        if test():
            passed += 1
        else:
            print(f"✗ 测试失败: {test.__name__}")
    
    print(f"\n测试结果: {passed}/{total} 通过")
    
    if passed == total:
        print("🎉 所有测试通过！音频上传API更新成功。")
        return True
    else:
        print("❌ 部分测试失败，请检查代码。")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
