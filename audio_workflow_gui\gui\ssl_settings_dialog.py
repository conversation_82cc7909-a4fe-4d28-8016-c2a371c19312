#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
SSL设置对话框

提供SSL证书验证设置的图形界面。

作者：Augment Agent
版本：1.0.0
"""

import tkinter as tk
from tkinter import ttk, messagebox
import threading
from typing import Optional, Callable

from utils.logger import LoggerMixin
from utils.ssl_fix import SSLFixer


class SSLSettingsDialog(tk.Toplevel, LoggerMixin):
    """SSL设置对话框"""
    
    def __init__(self, parent, config, on_settings_changed: Optional[Callable] = None):
        """
        初始化SSL设置对话框
        
        Args:
            parent: 父窗口
            config: 应用程序配置对象
            on_settings_changed: 设置更改时的回调函数
        """
        super().__init__(parent)
        LoggerMixin.__init__(self)
        
        self.config = config
        self.on_settings_changed = on_settings_changed
        self.ssl_fixer = SSLFixer()
        
        self.title("SSL设置")
        self.geometry("600x500")
        self.resizable(<PERSON><PERSON><PERSON>, False)
        
        # 设置为模态对话框
        self.transient(parent)
        self.grab_set()
        
        # 居中显示
        self._center_window()
        
        # 创建界面
        self._create_widgets()
        
        # 加载当前设置
        self._load_current_settings()
        
        # 运行SSL诊断
        self._run_ssl_diagnosis()
        
        self.logger.info("SSL设置对话框已打开")
    
    def _center_window(self):
        """将窗口居中显示"""
        self.update_idletasks()
        width = self.winfo_width()
        height = self.winfo_height()
        x = (self.winfo_screenwidth() // 2) - (width // 2)
        y = (self.winfo_screenheight() // 2) - (height // 2)
        self.geometry(f"{width}x{height}+{x}+{y}")
    
    def _create_widgets(self):
        """创建界面组件"""
        # 主框架
        main_frame = ttk.Frame(self, padding="10")
        main_frame.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))
        
        # SSL验证设置
        ssl_frame = ttk.LabelFrame(main_frame, text="SSL证书验证设置", padding="10")
        ssl_frame.grid(row=0, column=0, sticky=(tk.W, tk.E), pady=(0, 10))
        
        # SSL验证开关
        self.ssl_verify_var = tk.BooleanVar()
        self.ssl_verify_check = ttk.Checkbutton(
            ssl_frame,
            text="启用SSL证书验证（推荐）",
            variable=self.ssl_verify_var,
            command=self._on_ssl_verify_changed
        )
        self.ssl_verify_check.grid(row=0, column=0, sticky=tk.W, pady=(0, 5))
        
        # SSL自动修复开关
        self.ssl_auto_fix_var = tk.BooleanVar()
        self.ssl_auto_fix_check = ttk.Checkbutton(
            ssl_frame,
            text="启用SSL问题自动修复",
            variable=self.ssl_auto_fix_var
        )
        self.ssl_auto_fix_check.grid(row=1, column=0, sticky=tk.W, pady=(0, 5))
        
        # 安全警告标签
        self.warning_label = ttk.Label(
            ssl_frame,
            text="⚠ 警告：禁用SSL验证会降低连接安全性，仅适用于测试环境",
            foreground="red"
        )
        self.warning_label.grid(row=2, column=0, sticky=tk.W, pady=(5, 0))
        
        # SSL诊断框架
        diag_frame = ttk.LabelFrame(main_frame, text="SSL连接诊断", padding="10")
        diag_frame.grid(row=1, column=0, sticky=(tk.W, tk.E, tk.N, tk.S), pady=(0, 10))
        
        # 诊断结果显示
        self.diag_text = tk.Text(
            diag_frame,
            height=12,
            width=70,
            wrap=tk.WORD,
            state=tk.DISABLED
        )
        diag_scrollbar = ttk.Scrollbar(diag_frame, orient=tk.VERTICAL, command=self.diag_text.yview)
        self.diag_text.configure(yscrollcommand=diag_scrollbar.set)
        
        self.diag_text.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))
        diag_scrollbar.grid(row=0, column=1, sticky=(tk.N, tk.S))
        
        # 诊断按钮
        diag_button_frame = ttk.Frame(diag_frame)
        diag_button_frame.grid(row=1, column=0, columnspan=2, pady=(10, 0))
        
        self.diagnose_button = ttk.Button(
            diag_button_frame,
            text="重新诊断",
            command=self._run_ssl_diagnosis
        )
        self.diagnose_button.pack(side=tk.LEFT, padx=(0, 10))
        
        self.test_connection_button = ttk.Button(
            diag_button_frame,
            text="测试连接",
            command=self._test_connection
        )
        self.test_connection_button.pack(side=tk.LEFT)
        
        # 按钮框架
        button_frame = ttk.Frame(main_frame)
        button_frame.grid(row=2, column=0, pady=(10, 0))
        
        # 应用按钮
        self.apply_button = ttk.Button(
            button_frame,
            text="应用",
            command=self._apply_settings
        )
        self.apply_button.pack(side=tk.LEFT, padx=(0, 10))
        
        # 确定按钮
        self.ok_button = ttk.Button(
            button_frame,
            text="确定",
            command=self._ok_clicked
        )
        self.ok_button.pack(side=tk.LEFT, padx=(0, 10))
        
        # 取消按钮
        self.cancel_button = ttk.Button(
            button_frame,
            text="取消",
            command=self._cancel_clicked
        )
        self.cancel_button.pack(side=tk.LEFT)
        
        # 配置网格权重
        main_frame.columnconfigure(0, weight=1)
        main_frame.rowconfigure(1, weight=1)
        diag_frame.columnconfigure(0, weight=1)
        diag_frame.rowconfigure(0, weight=1)
    
    def _load_current_settings(self):
        """加载当前设置"""
        self.ssl_verify_var.set(self.config.is_ssl_verification_enabled())
        self.ssl_auto_fix_var.set(self.config.is_ssl_auto_fix_enabled())
        self._update_warning_visibility()
    
    def _on_ssl_verify_changed(self):
        """SSL验证设置改变时的处理"""
        self._update_warning_visibility()
    
    def _update_warning_visibility(self):
        """更新警告标签的可见性"""
        if self.ssl_verify_var.get():
            self.warning_label.grid_remove()
        else:
            self.warning_label.grid()
    
    def _run_ssl_diagnosis(self):
        """运行SSL诊断"""
        self.diagnose_button.config(state=tk.DISABLED)
        self._update_diag_text("正在诊断SSL连接...\n")
        
        def diagnose():
            try:
                base_url = self.config.get('api.base_url', 'https://www.gstudios.com.cn')
                hostname = base_url.replace('https://', '').replace('http://', '').split('/')[0]
                
                diagnosis = self.ssl_fixer.diagnose_ssl_issue(hostname)
                
                # 在主线程中更新UI
                self.after(0, self._display_diagnosis_result, diagnosis)
                
            except Exception as e:
                self.after(0, self._update_diag_text, f"诊断失败: {e}\n")
            finally:
                self.after(0, lambda: self.diagnose_button.config(state=tk.NORMAL))
        
        threading.Thread(target=diagnose, daemon=True).start()
    
    def _display_diagnosis_result(self, diagnosis):
        """显示诊断结果"""
        result_text = f"SSL诊断结果:\n"
        result_text += f"目标主机: {diagnosis['hostname']}:{diagnosis['port']}\n"
        result_text += f"SSL支持: {'是' if diagnosis['ssl_support'] else '否'}\n"
        result_text += f"证书有效: {'是' if diagnosis['certificate_valid'] else '否'}\n"
        result_text += f"CA证书包: {diagnosis.get('ca_bundle_path', '未知')}\n\n"
        
        if diagnosis.get('system_info'):
            system_info = diagnosis['system_info']
            result_text += f"系统信息:\n"
            result_text += f"  平台: {system_info.get('platform', '未知')}\n"
            result_text += f"  SSL版本: {system_info.get('ssl_version', '未知')}\n"
            result_text += f"  Certifi版本: {system_info.get('certifi_version', '未知')}\n\n"
        
        if diagnosis.get('errors'):
            result_text += f"错误信息:\n"
            for error in diagnosis['errors']:
                result_text += f"  • {error}\n"
            result_text += "\n"
        
        if diagnosis.get('suggestions'):
            result_text += f"建议:\n"
            for suggestion in diagnosis['suggestions']:
                result_text += f"  • {suggestion}\n"
        
        self._update_diag_text(result_text)
    
    def _test_connection(self):
        """测试连接"""
        self.test_connection_button.config(state=tk.DISABLED)
        self._update_diag_text("正在测试API连接...\n")
        
        def test():
            try:
                from api.client import GStudioAPIClient
                
                # 使用当前设置创建客户端
                verify_ssl = self.ssl_verify_var.get()
                client = GStudioAPIClient(verify_ssl=verify_ssl, debug_mode=False)
                
                # 设置Token
                token = self.config.get('api.token')
                if token:
                    client.set_token(token)
                    
                    # 测试API调用
                    books = client.search_books("", page_size=1)
                    
                    result = f"✓ API连接测试成功！\n"
                    result += f"SSL验证: {'启用' if verify_ssl else '禁用'}\n"
                    result += f"获取到 {len(books)} 本书籍\n"
                else:
                    result = "⚠ 未设置API Token，无法测试API连接\n"
                
                self.after(0, self._update_diag_text, result)
                
            except Exception as e:
                error_msg = f"✗ API连接测试失败: {e}\n"
                if "SSL" in str(e):
                    error_msg += "建议：尝试禁用SSL验证或更新证书包\n"
                self.after(0, self._update_diag_text, error_msg)
            finally:
                self.after(0, lambda: self.test_connection_button.config(state=tk.NORMAL))
        
        threading.Thread(target=test, daemon=True).start()
    
    def _update_diag_text(self, text):
        """更新诊断文本"""
        self.diag_text.config(state=tk.NORMAL)
        self.diag_text.delete(1.0, tk.END)
        self.diag_text.insert(tk.END, text)
        self.diag_text.config(state=tk.DISABLED)
        self.diag_text.see(tk.END)
    
    def _apply_settings(self):
        """应用设置"""
        try:
            # 保存SSL设置
            if self.ssl_verify_var.get():
                self.config.enable_ssl_verification()
            else:
                # 显示安全警告
                if not messagebox.askyesno(
                    "安全警告",
                    "禁用SSL证书验证会降低连接安全性，可能存在安全风险。\n\n"
                    "这个设置仅适用于测试环境或解决临时的证书问题。\n\n"
                    "确定要禁用SSL验证吗？",
                    icon="warning"
                ):
                    self.ssl_verify_var.set(True)
                    return
                
                self.config.disable_ssl_verification()
            
            # 保存自动修复设置
            self.config.set_ssl_auto_fix(self.ssl_auto_fix_var.get())
            
            # 通知设置已更改
            if self.on_settings_changed:
                self.on_settings_changed()
            
            messagebox.showinfo("设置", "SSL设置已保存")
            self.logger.info("SSL设置已应用")
            
        except Exception as e:
            messagebox.showerror("错误", f"保存设置失败: {e}")
            self.logger.error(f"保存SSL设置失败: {e}")
    
    def _ok_clicked(self):
        """确定按钮点击"""
        self._apply_settings()
        self.destroy()
    
    def _cancel_clicked(self):
        """取消按钮点击"""
        self.destroy()
