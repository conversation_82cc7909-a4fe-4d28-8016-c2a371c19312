#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
重试机制模块

提供指数退避重试策略，用于处理网络请求失败等临时性错误。

作者：Augment Agent
版本：1.0.0
"""

import time
import random
import logging
from typing import Callable, Any, Optional, Type, Tuple
from functools import wraps


class RetryError(Exception):
    """重试失败异常"""
    pass


class ConnectionResetError(Exception):
    """连接重置异常"""
    pass


def exponential_backoff_retry(
    max_retries: int = 3,
    base_delay: float = 1.0,
    max_delay: float = 60.0,
    exponential_base: float = 2.0,
    jitter: bool = True,
    exceptions: Tuple[Type[Exception], ...] = (Exception,),
    logger: Optional[logging.Logger] = None
):
    """
    指数退避重试装饰器
    
    Args:
        max_retries: 最大重试次数
        base_delay: 基础延迟时间（秒）
        max_delay: 最大延迟时间（秒）
        exponential_base: 指数基数
        jitter: 是否添加随机抖动
        exceptions: 需要重试的异常类型元组
        logger: 日志记录器
        
    Returns:
        装饰器函数
    """
    def decorator(func: Callable) -> Callable:
        @wraps(func)
        def wrapper(*args, **kwargs) -> Any:
            _logger = logger or logging.getLogger(func.__module__)
            
            last_exception = None
            
            for attempt in range(max_retries + 1):
                try:
                    return func(*args, **kwargs)
                    
                except exceptions as e:
                    last_exception = e
                    
                    if attempt == max_retries:
                        _logger.error(
                            f"函数 {func.__name__} 重试 {max_retries} 次后仍然失败: {e}"
                        )
                        raise RetryError(
                            f"重试 {max_retries} 次后仍然失败: {e}"
                        ) from e
                    
                    # 计算延迟时间
                    delay = min(
                        base_delay * (exponential_base ** attempt),
                        max_delay
                    )
                    
                    # 添加随机抖动
                    if jitter:
                        delay *= (0.5 + random.random() * 0.5)
                    
                    _logger.warning(
                        f"函数 {func.__name__} 第 {attempt + 1} 次调用失败: {e}，"
                        f"{delay:.2f} 秒后重试"
                    )
                    
                    time.sleep(delay)
                
                except Exception as e:
                    # 不在重试范围内的异常直接抛出
                    _logger.error(f"函数 {func.__name__} 发生不可重试的异常: {e}")
                    raise
            
            # 理论上不会到达这里
            raise last_exception
        
        return wrapper
    return decorator


class RetryManager:
    """重试管理器类"""
    
    def __init__(
        self,
        max_retries: int = 3,
        base_delay: float = 1.0,
        max_delay: float = 60.0,
        exponential_base: float = 2.0,
        jitter: bool = True,
        logger: Optional[logging.Logger] = None
    ):
        """
        初始化重试管理器
        
        Args:
            max_retries: 最大重试次数
            base_delay: 基础延迟时间（秒）
            max_delay: 最大延迟时间（秒）
            exponential_base: 指数基数
            jitter: 是否添加随机抖动
            logger: 日志记录器
        """
        self.max_retries = max_retries
        self.base_delay = base_delay
        self.max_delay = max_delay
        self.exponential_base = exponential_base
        self.jitter = jitter
        self.logger = logger or logging.getLogger(__name__)
    
    def retry(
        self,
        func: Callable,
        *args,
        exceptions: Tuple[Type[Exception], ...] = (Exception,),
        **kwargs
    ) -> Any:
        """
        执行带重试的函数调用
        
        Args:
            func: 要执行的函数
            *args: 函数位置参数
            exceptions: 需要重试的异常类型元组
            **kwargs: 函数关键字参数
            
        Returns:
            函数执行结果
            
        Raises:
            RetryError: 重试失败
        """
        last_exception = None
        
        for attempt in range(self.max_retries + 1):
            try:
                return func(*args, **kwargs)
                
            except exceptions as e:
                last_exception = e
                
                if attempt == self.max_retries:
                    self.logger.error(
                        f"函数 {func.__name__} 重试 {self.max_retries} 次后仍然失败: {e}"
                    )
                    raise RetryError(
                        f"重试 {self.max_retries} 次后仍然失败: {e}"
                    ) from e
                
                # 计算延迟时间
                delay = min(
                    self.base_delay * (self.exponential_base ** attempt),
                    self.max_delay
                )
                
                # 添加随机抖动
                if self.jitter:
                    delay *= (0.5 + random.random() * 0.5)
                
                self.logger.warning(
                    f"函数 {func.__name__} 第 {attempt + 1} 次调用失败: {e}，"
                    f"{delay:.2f} 秒后重试"
                )

                time.sleep(delay)

            except Exception as e:
                # 不在重试范围内的异常直接抛出
                self.logger.error(f"函数 {func.__name__} 发生不可重试的异常: {e}")
                raise

        # 理论上不会到达这里
        raise last_exception


# 常用的重试装饰器实例
network_retry = exponential_backoff_retry(
    max_retries=3,
    base_delay=1.0,
    exceptions=(ConnectionError, TimeoutError)
)

api_retry = exponential_backoff_retry(
    max_retries=5,
    base_delay=0.5,
    max_delay=30.0,
    exceptions=(ConnectionError, TimeoutError, OSError, ConnectionResetError)
)

# 专门用于处理连接重置错误的重试装饰器
connection_reset_retry = exponential_backoff_retry(
    max_retries=3,
    base_delay=1.0,
    max_delay=10.0,
    exceptions=(ConnectionResetError,)
)
