#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
菜单结构重构测试脚本

测试修改后的主窗口菜单结构和功能。

作者：Augment Agent
版本：1.0.0
"""

import sys
import os
import tkinter as tk
import logging

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from config.settings import AppConfig
from gui.main_window import MainWindow


def setup_logging():
    """设置日志"""
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
        handlers=[
            logging.StreamHandler(sys.stdout),
            logging.FileHandler('menu_restructure_test.log', encoding='utf-8')
        ]
    )


def test_menu_structure():
    """测试菜单结构"""
    print("=" * 60)
    print("测试菜单结构")
    print("=" * 60)
    
    try:
        # 创建根窗口（隐藏）
        root = tk.Tk()
        root.withdraw()
        
        # 创建配置对象
        config = AppConfig()
        
        print("1. 创建主窗口...")
        main_window = MainWindow(root, config)
        
        print("   ✓ 主窗口创建成功")
        
        # 检查菜单栏是否存在
        menubar = root['menu']
        if menubar:
            print("   ✓ 菜单栏创建成功")
        else:
            print("   ✗ 菜单栏创建失败")
            return False
        
        print("\n2. 检查菜单结构...")
        
        # 获取菜单项
        menu_count = menubar.index('end')
        if menu_count is not None:
            print(f"   菜单数量: {menu_count + 1}")
            
            # 检查各个菜单
            for i in range(menu_count + 1):
                menu_label = menubar.entrycget(i, 'label')
                print(f"   菜单 {i + 1}: {menu_label}")
        
        # 关闭窗口
        root.destroy()
        
        return True
        
    except Exception as e:
        print(f"   ✗ 菜单结构测试失败: {e}")
        return False


def test_api_token_setting():
    """测试API Token设置功能"""
    print("\n" + "=" * 60)
    print("测试API Token设置功能")
    print("=" * 60)
    
    try:
        # 创建根窗口（隐藏）
        root = tk.Tk()
        root.withdraw()
        
        # 创建配置对象
        config = AppConfig()
        
        print("1. 创建主窗口...")
        main_window = MainWindow(root, config)
        
        print("   ✓ 主窗口创建成功")
        
        print("\n2. 测试API Token设置方法...")
        
        # 检查方法是否存在
        if hasattr(main_window, '_set_api_token'):
            print("   ✓ _set_api_token 方法存在")
        else:
            print("   ✗ _set_api_token 方法不存在")
            return False
        
        # 关闭窗口
        root.destroy()
        
        return True
        
    except Exception as e:
        print(f"   ✗ API Token设置测试失败: {e}")
        return False


def test_api_connection_test():
    """测试API连接测试功能"""
    print("\n" + "=" * 60)
    print("测试API连接测试功能")
    print("=" * 60)
    
    try:
        # 创建根窗口（隐藏）
        root = tk.Tk()
        root.withdraw()
        
        # 创建配置对象
        config = AppConfig()
        
        print("1. 创建主窗口...")
        main_window = MainWindow(root, config)
        
        print("   ✓ 主窗口创建成功")
        
        print("\n2. 测试API连接测试方法...")
        
        # 检查方法是否存在
        if hasattr(main_window, '_test_api_connection'):
            print("   ✓ _test_api_connection 方法存在")
        else:
            print("   ✗ _test_api_connection 方法不存在")
            return False
        
        # 检查辅助方法
        if hasattr(main_window, '_show_api_test_success'):
            print("   ✓ _show_api_test_success 方法存在")
        else:
            print("   ✗ _show_api_test_success 方法不存在")
        
        if hasattr(main_window, '_show_api_test_error'):
            print("   ✓ _show_api_test_error 方法存在")
        else:
            print("   ✗ _show_api_test_error 方法不存在")
        
        # 关闭窗口
        root.destroy()
        
        return True
        
    except Exception as e:
        print(f"   ✗ API连接测试功能测试失败: {e}")
        return False


def test_book_search_changes():
    """测试书籍搜索界面的修改"""
    print("\n" + "=" * 60)
    print("测试书籍搜索界面的修改")
    print("=" * 60)
    
    try:
        # 创建根窗口（隐藏）
        root = tk.Tk()
        root.withdraw()
        
        # 创建配置对象
        config = AppConfig()
        
        print("1. 创建主窗口...")
        main_window = MainWindow(root, config)
        
        print("   ✓ 主窗口创建成功")
        
        print("\n2. 检查书籍搜索界面...")
        
        # 获取书籍搜索框架
        book_search_frame = main_window.book_search_frame
        
        # 检查是否还有测试连接按钮
        if hasattr(book_search_frame, 'test_connection_button'):
            print("   ⚠ 书籍搜索界面仍有test_connection_button属性")
        else:
            print("   ✓ 书籍搜索界面已移除test_connection_button")
        
        # 检查新的提示方法
        if hasattr(book_search_frame, '_show_settings_hint'):
            print("   ✓ _show_settings_hint 方法存在")
        else:
            print("   ✗ _show_settings_hint 方法不存在")
        
        # 关闭窗口
        root.destroy()
        
        return True
        
    except Exception as e:
        print(f"   ✗ 书籍搜索界面修改测试失败: {e}")
        return False


def test_menu_functionality():
    """测试菜单功能"""
    print("\n" + "=" * 60)
    print("测试菜单功能")
    print("=" * 60)
    
    try:
        # 创建根窗口（隐藏）
        root = tk.Tk()
        root.withdraw()
        
        # 创建配置对象
        config = AppConfig()
        
        print("1. 创建主窗口...")
        main_window = MainWindow(root, config)
        
        print("   ✓ 主窗口创建成功")
        
        print("\n2. 测试各项菜单功能...")
        
        # 测试SSL设置
        if hasattr(main_window, '_open_ssl_settings'):
            print("   ✓ SSL设置功能可用")
        else:
            print("   ✗ SSL设置功能不可用")
        
        # 测试网络诊断
        if hasattr(main_window, '_run_network_diagnosis'):
            print("   ✓ 网络诊断功能可用")
        else:
            print("   ✗ 网络诊断功能不可用")
        
        # 测试应用程序设置
        if hasattr(main_window, '_open_app_settings'):
            print("   ✓ 应用程序设置功能可用")
        else:
            print("   ✗ 应用程序设置功能不可用")
        
        # 关闭窗口
        root.destroy()
        
        return True
        
    except Exception as e:
        print(f"   ✗ 菜单功能测试失败: {e}")
        return False


def show_menu_structure_summary():
    """显示菜单结构总结"""
    print("\n" + "=" * 60)
    print("新菜单结构总结")
    print("=" * 60)
    print()
    print("📋 修改后的菜单结构：")
    print()
    print("1. 📁 文件菜单")
    print("   • 导出日志")
    print("   • ──────────")
    print("   • 退出")
    print()
    print("2. ⚙️ 设置菜单")
    print("   • 设置API Token        [从文件菜单移动]")
    print("   • 测试API连接          [从书籍搜索界面移动]")
    print("   • ──────────")
    print("   • SSL设置")
    print("   • 网络诊断")
    print("   • ──────────")
    print("   • 应用程序设置")
    print()
    print("3. 🔧 工具菜单")
    print("   • 清理临时文件")
    print("   • 存储统计")
    print("   • ──────────")
    print("   • 调试模式设置")
    print()
    print("4. ❓ 帮助菜单")
    print("   • 使用说明")
    print("   • 关于")
    print()
    print("🔄 主要变更：")
    print()
    print("✅ 移动功能：")
    print("• API Token设置：文件菜单 → 设置菜单")
    print("• API连接测试：书籍搜索界面 → 设置菜单")
    print()
    print("✅ 逻辑分组：")
    print("• API相关：Token设置 + 连接测试")
    print("• 网络相关：SSL设置 + 网络诊断")
    print("• 应用设置：独立分组")
    print()
    print("✅ 用户体验：")
    print("• 相关功能集中管理")
    print("• 减少界面按钮数量")
    print("• 统一的设置入口")
    print()


def main():
    """主测试函数"""
    print("GStudio 菜单结构重构测试")
    print("=" * 60)
    print("测试修改后的主窗口菜单结构和功能")
    print()
    
    # 设置日志
    setup_logging()
    
    success_count = 0
    total_tests = 5
    
    try:
        # 运行测试
        tests = [
            ("菜单结构", test_menu_structure),
            ("API Token设置功能", test_api_token_setting),
            ("API连接测试功能", test_api_connection_test),
            ("书籍搜索界面修改", test_book_search_changes),
            ("菜单功能", test_menu_functionality)
        ]
        
        for test_name, test_func in tests:
            try:
                if test_func():
                    print(f"✅ {test_name}测试通过")
                    success_count += 1
                else:
                    print(f"❌ {test_name}测试失败")
            except Exception as e:
                print(f"❌ {test_name}测试异常: {e}")
        
        print("\n" + "=" * 60)
        print("测试结果总结")
        print("=" * 60)
        
        print(f"通过测试: {success_count}/{total_tests}")
        
        if success_count == total_tests:
            print("🎉 所有测试通过！菜单结构重构成功！")
            show_menu_structure_summary()
        else:
            print("⚠ 部分测试未通过，请检查失败的测试项目")
        
    except Exception as e:
        print(f"测试过程中发生异常: {e}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    main()
