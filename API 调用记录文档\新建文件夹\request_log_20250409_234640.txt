
==================================================
=== 请求信息 ===
时间: 2025-04-09 23:46:42
方法: GET
URL: https://www.gstudios.com.cn/story_v2/api/content/book/relation/freelancer/by/book
备注标题: 
备注内容: 
Headers: {
  "host": "www.gstudios.com.cn",
  "connection": "keep-alive",
  "sec-ch-ua-platform": "Windows",
  "x-requested-with": "XMLHttpRequest",
  "user-agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********",
  "accept": "application/json, text/plain, */*",
  "sec-ch-ua": "\"Microsoft Edge\";v=\"135\", \"Not-A<PERSON><PERSON>\";v=\"8\", \"Chromium\";v=\"135\"",
  "sec-ch-ua-mobile": "?0",
  "sec-fetch-site": "same-origin",
  "sec-fetch-mode": "cors",
  "sec-fetch-dest": "empty",
  "accept-encoding": "gzip, deflate, br, zstd",
  "accept-language": "zh-CN,zh;q=0.9,en;q=0.8,en-GB;q=0.7,en-US;q=0.6",
  "authorization": "Bearer d22e0aabbc6441dea7c113d07d0a4ed9",
  "referer": "https://www.gstudios.com.cn/"
}
请求数据: {
  "bookId": "33524",
  "freelancerName": "",
  "sortItem": "",
  "sortAsc": ""
}
请求参数: "获取已邀请合作成员列表"

=== 响应信息 ===
状态码: 200
响应内容: {
  "code": 1,
  "data": {
    "list": []
  },
  "msg": "成功!"
}
