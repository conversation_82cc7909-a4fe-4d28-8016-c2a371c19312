# GStudio 音频工作流 GUI 应用程序使用示例

本文档提供了详细的使用示例，帮助您快速上手使用应用程序。

## 快速开始

### 1. 启动应用程序

**Windows用户：**
```bash
# 方法1：双击启动脚本
start.bat

# 方法2：命令行启动
cd audio_workflow_gui
python main.py
```

**Linux/macOS用户：**
```bash
# 方法1：使用启动脚本
./start.sh

# 方法2：命令行启动
cd audio_workflow_gui
python3 main.py
```

### 2. 首次配置

1. **设置API Token**
   - 启动应用程序后，点击菜单栏 "文件" -> "设置API Token"
   - 输入您的GStudio API Token
   - 点击确定保存

2. **验证连接**
   - 工具栏中的"API状态"应显示为"已连接"（绿色）
   - 如果显示"未连接"（红色），请检查Token是否正确

## 完整工作流示例

### 步骤1：书籍搜索

1. **进入书籍搜索页面**
   - 应用程序启动后默认在"1. 书籍搜索"标签页

2. **搜索书籍**
   ```
   示例操作：
   - 在"书籍名称"输入框中输入：修真
   - 选择搜索模式：模糊匹配（默认）
   - 点击"搜索"按钮
   ```

3. **选择书籍**
   - 从搜索结果中选择目标书籍
   - 双击或点击"选择书籍"按钮
   - 应用程序自动切换到下一步

### 步骤2：章节管理

1. **加载章节列表**
   - 点击"加载章节列表"按钮
   - 等待章节数据加载完成

2. **搜索特定章节（可选）**
   ```
   示例操作：
   - 选择"按标题搜索"
   - 输入：第一章
   - 点击"搜索"按钮
   
   或者：
   - 选择"按序号定位"
   - 输入：1
   - 点击"搜索"按钮
   ```

3. **选择章节**
   - 从章节列表中选择要处理的章节
   - 双击或点击"选择章节"按钮

### 步骤3：段落处理

1. **加载段落数据**
   - 点击"加载章节段落"按钮
   - 等待段落数据加载完成

2. **配置筛选条件**
   ```
   默认筛选条件：
   - 段落类型：旁白
   - CV类型：机器人
   
   可根据需要调整筛选条件
   ```

3. **应用筛选**
   - 点击"应用筛选"按钮
   - 查看筛选结果统计信息
   - 点击"预览筛选结果"查看详细内容（可选）

4. **确认选择**
   - 确认筛选结果无误后，点击"确认并继续"

### 步骤4：音频生成

1. **配置TTS参数**
   ```
   推荐配置：
   - CV机器人ID：568（默认）
   - 语速因子：100（正常语速）
   - 静音时长因子：100（正常静音）
   - 批处理大小：10（根据网络情况调整）
   - 并发数量：3（避免过载）
   ```

2. **开始生成音频**
   - 点击"开始生成"按钮
   - 观察进度条和状态信息
   - 等待所有音频生成完成

3. **检查生成结果**
   - 查看生成结果列表
   - 双击任意音频文件进行播放测试
   - 点击"打开文件夹"查看生成的音频文件

4. **继续下一步**
   - 确认生成结果满意后，点击"继续下一步"

### 步骤5：音频上传

1. **配置上传参数**
   ```
   推荐配置：
   - 并发上传数：3（避免服务器过载）
   - 重试次数：3（处理网络异常）
   - 超时时间：60秒
   - 上传模式：覆盖（默认）
   ```

2. **开始上传**
   - 点击"开始上传"按钮
   - 观察上传进度和状态
   - 等待所有文件上传完成

3. **检查上传结果**
   - 查看上传结果统计
   - 如有失败项，可点击"重试失败项"
   - 点击"查看详情"查看具体上传信息

4. **完成工作流**
   - 点击"完成工作流"结束整个处理流程

## 高级功能使用

### 批量处理优化

1. **调整批处理大小**
   - 网络较好时：可设置为20-50
   - 网络一般时：建议10-20
   - 网络较差时：建议5-10

2. **并发控制**
   - 音频生成并发：建议不超过5
   - 音频上传并发：建议不超过3

### 错误处理

1. **网络连接问题**
   ```
   症状：API状态显示"未连接"
   解决方案：
   - 检查网络连接
   - 验证API Token
   - 重启应用程序
   ```

2. **音频生成失败**
   ```
   症状：生成状态显示"失败"
   解决方案：
   - 检查段落内容是否有特殊字符
   - 调整TTS参数
   - 减少并发数量
   ```

3. **上传失败**
   ```
   症状：上传状态显示"失败"
   解决方案：
   - 检查文件是否存在
   - 验证文件格式
   - 使用"重试失败项"功能
   ```

### 文件管理

1. **音频文件位置**
   ```
   生成的音频：audio_files/generated/[书籍ID]/[章节ID]/
   上传的音频：audio_files/uploaded/[书籍ID]/[章节ID]/
   临时文件：audio_files/temp/
   ```

2. **清理临时文件**
   - 菜单栏 -> 工具 -> 清理临时文件

3. **查看存储统计**
   - 菜单栏 -> 工具 -> 存储统计

### 日志和调试

1. **查看日志**
   - 应用程序运行时会在控制台显示日志
   - 详细日志保存在logs目录

2. **导出日志**
   - 菜单栏 -> 文件 -> 导出日志

3. **调试模式**
   - 修改config.json中的logging.level为"DEBUG"

## 常见问题解答

### Q: 应用程序启动失败怎么办？
A: 
1. 确保Python版本为3.7或更高
2. 运行`pip install -r requirements.txt`安装依赖
3. 检查是否有防火墙阻止

### Q: 音频生成速度很慢怎么办？
A:
1. 减少批处理大小
2. 降低并发数量
3. 检查网络连接质量

### Q: 如何批量处理多个章节？
A: 目前版本需要逐个章节处理，后续版本将支持批量章节处理

### Q: 支持哪些音频格式？
A: 目前主要支持MP3格式，其他格式支持有限

### Q: 如何备份配置？
A: 复制config/config.json文件即可备份所有配置

## 技术支持

如遇到问题，请：
1. 查看应用程序日志
2. 检查网络连接
3. 验证API Token有效性
4. 参考本文档的故障排除部分

更多技术细节请参考README.md文档。
