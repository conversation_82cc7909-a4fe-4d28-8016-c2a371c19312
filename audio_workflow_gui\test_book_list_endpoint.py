#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
书籍列表端点专项测试

专门测试书籍列表端点的连接问题。

作者：Augment Agent
版本：1.0.0
"""

import sys
import os
import requests
import time

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from api.models import DefaultHeaders


def test_book_list_endpoint():
    """测试书籍列表端点"""
    print("书籍列表端点专项测试")
    print("=" * 40)
    
    url = "https://www.gstudios.com.cn/story_v2/api/content/book/list"
    headers = DefaultHeaders.get_headers()
    
    print(f"测试URL: {url}")
    print("测试不同的超时设置和重试策略...")
    
    # 测试不同的超时设置
    timeout_settings = [5, 10, 30, 60]
    
    for timeout in timeout_settings:
        print(f"\n--- 超时设置: {timeout}秒 ---")
        
        try:
            start_time = time.time()
            response = requests.get(url, headers=headers, timeout=timeout)
            end_time = time.time()
            
            print(f"✓ 请求成功")
            print(f"  状态码: {response.status_code}")
            print(f"  响应时间: {end_time - start_time:.2f}秒")
            
            if response.status_code == 401:
                print("  ✓ 端点存在，需要认证token")
                return True
            elif response.status_code == 200:
                print("  ✓ 端点可访问")
                return True
            else:
                print(f"  ? 未知状态码: {response.status_code}")
                
        except requests.exceptions.Timeout:
            print(f"✗ 请求超时 (>{timeout}秒)")
            
        except requests.exceptions.ConnectionError as e:
            print(f"✗ 连接错误: {e}")
            
        except requests.exceptions.RequestException as e:
            print(f"✗ 请求异常: {e}")
    
    return False


def test_with_session():
    """使用Session测试"""
    print("\n" + "=" * 40)
    print("使用Session进行测试")
    
    url = "https://www.gstudios.com.cn/story_v2/api/content/book/list"
    headers = DefaultHeaders.get_headers()
    
    session = requests.Session()
    session.headers.update(headers)
    
    try:
        print("发送请求...")
        response = session.get(url, timeout=30)
        
        print(f"✓ 请求成功")
        print(f"  状态码: {response.status_code}")
        
        if response.status_code == 401:
            print("  ✓ 端点存在，需要认证token")
            return True
        elif response.status_code == 200:
            print("  ✓ 端点可访问")
            return True
        else:
            print(f"  ? 未知状态码: {response.status_code}")
            
    except Exception as e:
        print(f"✗ Session请求失败: {e}")
    
    return False


def test_alternative_methods():
    """测试其他HTTP方法"""
    print("\n" + "=" * 40)
    print("测试其他HTTP方法")
    
    url = "https://www.gstudios.com.cn/story_v2/api/content/book/list"
    headers = DefaultHeaders.get_headers()
    
    methods = ['HEAD', 'OPTIONS']
    
    for method in methods:
        print(f"\n--- {method} 方法 ---")
        
        try:
            if method == 'HEAD':
                response = requests.head(url, headers=headers, timeout=30)
            elif method == 'OPTIONS':
                response = requests.options(url, headers=headers, timeout=30)
            
            print(f"✓ {method} 请求成功")
            print(f"  状态码: {response.status_code}")
            
            if response.status_code == 401:
                print("  ✓ 端点存在，需要认证")
            elif response.status_code == 200:
                print("  ✓ 端点可访问")
            elif response.status_code == 405:
                print("  ✓ 端点存在，但不支持此方法")
            else:
                print(f"  ? 状态码: {response.status_code}")
                
        except Exception as e:
            print(f"✗ {method} 请求失败: {e}")


def main():
    """主测试函数"""
    print("GStudio 书籍列表端点诊断")
    print("=" * 50)
    
    # 测试基本连接
    success = test_book_list_endpoint()
    
    if not success:
        # 如果基本测试失败，尝试其他方法
        success = test_with_session()
        
        if not success:
            test_alternative_methods()
    
    print("\n" + "=" * 50)
    
    if success:
        print("✓ 书籍列表端点可访问")
        print("问题可能是:")
        print("1. 需要有效的API Token")
        print("2. 端点响应较慢，需要增加超时时间")
    else:
        print("✗ 书籍列表端点访问困难")
        print("可能的原因:")
        print("1. 网络连接问题")
        print("2. 服务器负载过高")
        print("3. 端点配置问题")
        print("4. 防火墙或代理限制")
        
        print("\n建议:")
        print("1. 检查网络连接")
        print("2. 尝试使用VPN或更换网络")
        print("3. 联系API提供方确认端点状态")
        print("4. 在应用程序中增加超时时间和重试次数")


if __name__ == "__main__":
    main()
    input("\n按回车键退出...")
