#!/usr/bin/env python3
"""
新发现API的使用示例
演示如何使用Material和Chapter相关的新API功能
"""

import sys
import os
import requests
import json
from datetime import datetime

# 添加simple-gstudio-api目录到Python路径
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'simple-gstudio-api'))

from gstudio_api import (
    APIEndpoints,
    VoiceCountChapterCuesParams,
    VoiceListCueParams,
    ChapterCuesEditorParams,
    VoiceDownloadParams,
    DefaultHeaders
)

def build_query_string(params_obj):
    """将参数对象转换为查询字符串"""
    params = {}
    for key, value in params_obj.__dict__.items():
        if value is not None:
            params[key] = value
    return params

def demo_voice_count_chapter_cues(token: str):
    """演示章节内容统计API"""
    print("=== 示例1: 获取章节内容统计 ===")
    
    # 创建参数
    params = VoiceCountChapterCuesParams(chapterId=8607068)
    print(f"参数: chapterId={params.chapterId}")
    
    # 构建URL
    base_url = APIEndpoints.get_url(APIEndpoints.Material.VOICE_COUNT_CHAPTER_CUES)
    query_params = build_query_string(params)
    print(f"请求URL: {base_url}")
    print(f"查询参数: {query_params}")
    
    # 构建完整URL（实际使用时）
    full_url = f"{base_url}?chapterId={params.chapterId}"
    print(f"完整URL: {full_url}")
    
    print("响应示例:")
    print(json.dumps({
        "code": 1,
        "msg": "成功!",
        "data": {
            "list": [
                {
                    "count": 1,
                    "cueId": 691699345,
                    "selectedMaterialId": 305175318
                },
                {
                    "count": 2,
                    "cueId": 691699346,
                    "selectedMaterialId": 305346872
                }
            ]
        }
    }, indent=2, ensure_ascii=False))

def demo_voice_list_cue(token: str):
    """演示章节内容片录音列表API"""
    print("\n=== 示例2: 获取章节内容片录音列表 ===")
    
    # 创建参数
    params = VoiceListCueParams(cueId=691699346, characterId=5530515)
    print(f"参数: cueId={params.cueId}, characterId={params.characterId}")
    
    # 构建URL
    base_url = APIEndpoints.get_url(APIEndpoints.Material.VOICE_LIST_CUE)
    query_params = build_query_string(params)
    print(f"请求URL: {base_url}")
    print(f"查询参数: {query_params}")
    
    # 构建完整URL（实际使用时）
    full_url = f"{base_url}?cueId={params.cueId}&characterId={params.characterId}"
    print(f"完整URL: {full_url}")
    
    print("响应示例:")
    print(json.dumps({
        "code": 1,
        "msg": "成功!",
        "data": {
            "list": [
                {
                    "characterName": "旁白",
                    "createdTime": 1751367655857,
                    "cvName": "曹先森MK-III",
                    "cvType": 0,
                    "durationEffectiveMs": 950,
                    "gainDb": 0,
                    "materialId": 305346872,
                    "needBill": None,
                    "numCharCharged": 0,
                    "robotDurationFactorSilence": None,
                    "robotGenMode": None,
                    "robotSpeedFactor": None,
                    "robotStyle": "",
                    "robotStyleStandard": None,
                    "sourceType": 1
                }
            ],
            "selectedMaterialId": 305346872
        }
    }, indent=2, ensure_ascii=False))

def demo_chapter_cues_editor(token: str):
    """演示章节编辑器内容列表API"""
    print("\n=== 示例3: 获取章节编辑器内容列表 ===")
    
    # 创建参数
    params = ChapterCuesEditorParams(chapterId=8607068)
    print(f"参数: chapterId={params.chapterId}")
    
    # 构建URL
    base_url = APIEndpoints.get_url(APIEndpoints.Chapter.CUES_LIST_EDITOR)
    query_params = build_query_string(params)
    print(f"请求URL: {base_url}")
    print(f"查询参数: {query_params}")
    
    # 构建完整URL（实际使用时）
    full_url = f"{base_url}?chapterId={params.chapterId}"
    print(f"完整URL: {full_url}")
    
    print("响应示例（简化）:")
    print(json.dumps({
        "code": 1,
        "msg": "成功!",
        "data": {
            "characterIds": [],
            "cues": [
                {
                    "action": 0,
                    "chapterTitle": True,
                    "characterId": 5530515,
                    "id": 691699345,
                    "seqNum": 0,
                    "ssml": "第1集，",
                    "text": "第1集，",
                    "type": 2
                }
            ],
            "revision": 6
        }
    }, indent=2, ensure_ascii=False))

def demo_voice_download(token: str):
    """演示录音文件下载API"""
    print("\n=== 示例4: 下载录音文件 ===")
    
    # 创建参数
    params = VoiceDownloadParams(id=305346872)
    print(f"参数: id={params.id} (materialId)")
    
    # 构建URL
    base_url = APIEndpoints.get_url(APIEndpoints.Material.VOICE_DOWNLOAD)
    query_params = build_query_string(params)
    print(f"请求URL: {base_url}")
    print(f"查询参数: {query_params}")
    
    # 构建完整URL（实际使用时）
    full_url = f"{base_url}?id={params.id}"
    print(f"完整URL: {full_url}")
    
    print("响应格式:")
    print("- Content-Type: application/octet-stream")
    print("- Content-Disposition: attachment;filename=录音下载-{timestamp}.mp3")
    print("- 响应体: 二进制MP3音频数据")
    print("- 状态码: 200（成功）")
    print("注意：此API返回二进制音频文件，需要特殊处理")

def demo_actual_request(token: str):
    """演示实际的HTTP请求（如果有有效token）"""
    print("\n=== 示例5: 实际HTTP请求示例 ===")
    
    if token == "your_actual_token_here":
        print("⚠️  请设置实际的认证令牌以进行真实请求")
        print("以下是请求代码示例：")
    else:
        print("使用实际token进行请求...")
    
    print("""
# 示例：获取章节内容统计
import requests

def get_voice_count_chapter_cues(token, chapter_id):
    url = "https://www.gstudios.com.cn/story_v2/api/material/voice/count/chapter/cues"
    headers = {
        "authorization": f"Bearer {token}",
        "accept": "application/json, text/plain, */*",
        "user-agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36"
    }
    params = {"chapterId": chapter_id}
    
    response = requests.get(url, headers=headers, params=params)
    return response.json()

# 示例：下载录音文件
def download_voice_file(token, material_id, output_file):
    url = "https://www.gstudios.com.cn/story_v2/api/material/voice/download"
    headers = {
        "authorization": f"Bearer {token}",
        "user-agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36"
    }
    params = {"id": material_id}
    
    response = requests.get(url, headers=headers, params=params)
    if response.status_code == 200:
        with open(output_file, 'wb') as f:
            f.write(response.content)
        return True
    return False
""")

def main():
    """主函数"""
    print("=== 新发现API使用示例 ===\n")
    
    # 注意：这里需要替换为实际的认证令牌
    token = "your_actual_token_here"
    
    # 演示所有新API的使用
    demo_voice_count_chapter_cues(token)
    demo_voice_list_cue(token)
    demo_chapter_cues_editor(token)
    demo_voice_download(token)
    demo_actual_request(token)
    
    print("\n=== API端点总览 ===")
    print("Material相关API:")
    print(f"  章节内容统计: {APIEndpoints.get_url(APIEndpoints.Material.VOICE_COUNT_CHAPTER_CUES)}")
    print(f"  内容片录音列表: {APIEndpoints.get_url(APIEndpoints.Material.VOICE_LIST_CUE)}")
    print(f"  录音文件下载: {APIEndpoints.get_url(APIEndpoints.Material.VOICE_DOWNLOAD)}")
    
    print("\nChapter相关API:")
    print(f"  编辑器内容列表: {APIEndpoints.get_url(APIEndpoints.Chapter.CUES_LIST_EDITOR)}")
    
    print("\n使用说明:")
    print("1. 所有新API都是GET请求，参数通过查询字符串传递")
    print("2. 需要Bearer Token认证")
    print("3. 录音下载API返回二进制文件，其他API返回JSON")
    print("4. 参数类型都已定义，提供类型安全和IDE支持")
    print("5. 支持V1和V2两个API版本")

if __name__ == "__main__":
    main()
