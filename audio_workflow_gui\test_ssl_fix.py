#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
SSL问题修复测试脚本

测试和修复SSL证书验证问题。

作者：Augment Agent
版本：1.0.0
"""

import sys
import os
import logging

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from api.client import GStudioAPIClient
from utils.ssl_fix import SSLFixer


def setup_logging():
    """设置日志"""
    logging.basicConfig(
        level=logging.DEBUG,
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
        handlers=[
            logging.StreamHandler(sys.stdout),
            logging.FileHandler('ssl_fix_test.log', encoding='utf-8')
        ]
    )


def test_ssl_diagnosis():
    """测试SSL诊断功能"""
    print("=" * 60)
    print("SSL问题诊断测试")
    print("=" * 60)
    
    fixer = SSLFixer()
    
    print("1. 诊断SSL连接问题...")
    diagnosis = fixer.diagnose_ssl_issue()
    
    print(f"   目标主机: {diagnosis['hostname']}:{diagnosis['port']}")
    print(f"   SSL支持: {'是' if diagnosis['ssl_support'] else '否'}")
    print(f"   证书有效: {'是' if diagnosis['certificate_valid'] else '否'}")
    print(f"   CA证书包: {diagnosis['ca_bundle_path']}")
    
    print(f"\n2. 系统信息:")
    system_info = diagnosis['system_info']
    print(f"   平台: {system_info['platform']}")
    print(f"   Python版本: {system_info['python_version']}")
    print(f"   SSL版本: {system_info['ssl_version']}")
    print(f"   Certifi版本: {system_info['certifi_version']}")
    
    if diagnosis['errors']:
        print(f"\n3. 错误信息:")
        for error in diagnosis['errors']:
            print(f"   • {error}")
    
    if diagnosis['suggestions']:
        print(f"\n4. 建议:")
        for suggestion in diagnosis['suggestions']:
            print(f"   • {suggestion}")
    
    return diagnosis


def test_ssl_bypass():
    """测试SSL绕过功能"""
    print("\n" + "=" * 60)
    print("SSL绕过功能测试")
    print("=" * 60)
    
    # 创建启用SSL验证的客户端
    print("1. 测试启用SSL验证的连接...")
    client_with_ssl = GStudioAPIClient(debug_mode=True, verify_ssl=True)
    
    try:
        # 尝试连接
        diagnosis = client_with_ssl.diagnose_ssl_issues()
        print(f"   SSL诊断完成")
        
        # 设置Token并尝试API调用
        token = "3dfb89119562456cb8818120139f6ae1"
        client_with_ssl.set_token(token)
        
        books = client_with_ssl.search_books("", page_size=1)
        print(f"   ✓ SSL验证模式下API调用成功，获取到 {len(books)} 本书籍")
        return True
        
    except Exception as e:
        print(f"   ✗ SSL验证模式下API调用失败: {e}")
        
        # 测试SSL绕过模式
        print("\n2. 测试SSL绕过模式...")
        client_bypass = GStudioAPIClient(debug_mode=True, verify_ssl=False)
        
        try:
            client_bypass.set_token(token)
            books = client_bypass.search_books("", page_size=1)
            print(f"   ✓ SSL绕过模式下API调用成功，获取到 {len(books)} 本书籍")
            print(f"   ⚠ 建议：仅在测试环境中使用SSL绕过模式")
            return True
            
        except Exception as bypass_e:
            print(f"   ✗ SSL绕过模式下API调用也失败: {bypass_e}")
            return False


def test_dynamic_ssl_control():
    """测试动态SSL控制功能"""
    print("\n" + "=" * 60)
    print("动态SSL控制测试")
    print("=" * 60)
    
    # 创建客户端
    client = GStudioAPIClient(debug_mode=True, verify_ssl=True)
    token = "3dfb89119562456cb8818120139f6ae1"
    client.set_token(token)
    
    print("1. 初始状态（SSL验证启用）...")
    print(f"   SSL验证状态: {client.verify_ssl}")
    
    try:
        books = client.search_books("", page_size=1)
        print(f"   ✓ API调用成功，获取到 {len(books)} 本书籍")
        ssl_works = True
    except Exception as e:
        print(f"   ✗ API调用失败: {e}")
        ssl_works = False
    
    if not ssl_works:
        print("\n2. 启用SSL绕过模式...")
        client.enable_ssl_bypass()
        print(f"   SSL验证状态: {client.verify_ssl}")
        
        try:
            books = client.search_books("", page_size=1)
            print(f"   ✓ SSL绕过模式下API调用成功，获取到 {len(books)} 本书籍")
            
            print("\n3. 恢复SSL验证...")
            client.disable_ssl_bypass()
            print(f"   SSL验证状态: {client.verify_ssl}")
            
        except Exception as e:
            print(f"   ✗ SSL绕过模式下API调用失败: {e}")


def test_auto_fix():
    """测试自动修复功能"""
    print("\n" + "=" * 60)
    print("自动修复功能测试")
    print("=" * 60)
    
    fixer = SSLFixer()
    
    print("1. 尝试自动修复常见SSL问题...")
    fix_result = fixer.auto_fix_common_issues()
    
    if fix_result['fixes_applied']:
        print("   应用的修复:")
        for fix in fix_result['fixes_applied']:
            print(f"     • {fix}")
    
    if fix_result['errors']:
        print("   修复过程中的错误:")
        for error in fix_result['errors']:
            print(f"     • {error}")
    
    print(f"   修复成功: {'是' if fix_result['success'] else '否'}")
    
    return fix_result['success']


def provide_user_guidance():
    """提供用户指导"""
    print("\n" + "=" * 60)
    print("SSL问题解决指导")
    print("=" * 60)
    
    print("如果您遇到SSL证书验证失败的问题，请按以下步骤操作：")
    print()
    print("🔧 临时解决方案（仅用于测试）：")
    print("   在创建API客户端时设置 verify_ssl=False")
    print("   例如：client = GStudioAPIClient(verify_ssl=False)")
    print()
    print("🔒 永久解决方案（推荐）：")
    print("   1. 更新证书包：pip install --upgrade certifi")
    print("   2. 检查系统时间是否正确")
    print("   3. 如果在企业网络中，配置代理设置")
    print()
    print("🔍 诊断工具：")
    print("   运行此脚本获取详细的SSL问题诊断信息")
    print("   或在代码中调用 client.diagnose_ssl_issues()")
    print()
    print("⚡ 动态控制：")
    print("   client.enable_ssl_bypass()  # 临时禁用SSL验证")
    print("   client.disable_ssl_bypass() # 恢复SSL验证")


def main():
    """主测试函数"""
    print("GStudio SSL问题修复测试")
    print("=" * 60)
    print("测试SSL证书验证问题的诊断和修复功能")
    print()
    
    # 设置日志
    setup_logging()
    
    try:
        # 运行测试
        diagnosis = test_ssl_diagnosis()
        
        if not diagnosis['certificate_valid']:
            print("\n检测到SSL证书问题，继续测试修复功能...")
            
            bypass_success = test_ssl_bypass()
            test_dynamic_ssl_control()
            
            if bypass_success:
                print("\n✅ SSL绕过模式工作正常，可以作为临时解决方案")
            
            auto_fix_success = test_auto_fix()
            
            if auto_fix_success:
                print("\n✅ 自动修复成功！")
            else:
                print("\n⚠ 自动修复未完全成功，请手动处理")
        else:
            print("\n✅ SSL证书验证正常，无需修复")
        
        provide_user_guidance()
        
        print("\n" + "=" * 60)
        print("测试完成")
        print("=" * 60)
        
    except Exception as e:
        print(f"测试过程中发生异常: {e}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    main()
