#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
新表格列显示测试脚本

测试修改后的书籍搜索界面表格列显示功能。

作者：Augment Agent
版本：1.0.0
"""

import sys
import os
import logging

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from api.client import GStudioAPIClient
from utils.field_utils import (
    get_book_field, normalize_book_data, get_book_display_values,
    format_timestamp, format_finished_status
)


def setup_logging():
    """设置日志"""
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
        handlers=[
            logging.StreamHandler(sys.stdout),
            logging.FileHandler('new_table_columns_test.log', encoding='utf-8')
        ]
    )


def test_time_formatting():
    """测试时间格式化功能"""
    print("=" * 60)
    print("测试时间格式化功能")
    print("=" * 60)
    
    # 测试不同格式的时间戳
    test_timestamps = [
        1743654555189,  # 毫秒时间戳
        1743654555,     # 秒时间戳
        "1743654555189", # 字符串毫秒时间戳
        "1743654555",   # 字符串秒时间戳
        None,           # 空值
        "",             # 空字符串
        "invalid"       # 无效值
    ]
    
    print("时间戳格式化测试:")
    for i, timestamp in enumerate(test_timestamps, 1):
        formatted = format_timestamp(timestamp)
        print(f"  {i}. {timestamp} -> {formatted}")
    
    return True


def test_status_formatting():
    """测试状态格式化功能"""
    print("\n" + "=" * 60)
    print("测试状态格式化功能")
    print("=" * 60)
    
    # 测试不同格式的状态值
    test_statuses = [
        True,           # 布尔值 True
        False,          # 布尔值 False
        "true",         # 字符串 true
        "false",        # 字符串 false
        1,              # 数字 1
        0,              # 数字 0
        "completed",    # 完成状态
        "in_progress",  # 进行中状态
        None,           # 空值
        "unknown"       # 未知值
    ]
    
    print("状态格式化测试:")
    for i, status in enumerate(test_statuses, 1):
        formatted = format_finished_status(status)
        print(f"  {i}. {status} -> {formatted}")
    
    return True


def test_book_display_values():
    """测试书籍显示值获取"""
    print("\n" + "=" * 60)
    print("测试书籍显示值获取")
    print("=" * 60)
    
    # 模拟新API格式的书籍数据
    new_api_book = {
        "id": 39726,
        "name": "我真的不想当皇帝",
        "description": "一部精彩的小说，讲述了一个不想当皇帝的人的故事",
        "finished": False,
        "remark": "小南瓜画本",
        "createdTime": 1743654555189,
        "updatedTime": 1743754555189
    }
    
    # 模拟旧API格式的书籍数据
    old_api_book = {
        "bookId": "12345",
        "bookName": "经典小说",
        "description": "一部经典的文学作品",
        "finished": True,
        "price": 2999,
        "createdTime": 1743654555,
        "updatedTime": 1743754555
    }
    
    print("1. 新API格式书籍数据:")
    print(f"   原始数据: {new_api_book}")
    
    normalized_new = normalize_book_data(new_api_book)
    print(f"   标准化后: {list(normalized_new.keys())}")
    
    display_values = get_book_display_values(normalized_new)
    print(f"   显示值: {display_values}")
    
    print("\n2. 旧API格式书籍数据:")
    print(f"   原始数据: {old_api_book}")
    
    normalized_old = normalize_book_data(old_api_book)
    print(f"   标准化后: {list(normalized_old.keys())}")
    
    display_values_old = get_book_display_values(normalized_old)
    print(f"   显示值: {display_values_old}")
    
    return True


def test_real_api_data():
    """测试真实API数据"""
    print("\n" + "=" * 60)
    print("测试真实API数据")
    print("=" * 60)
    
    try:
        # 创建API客户端（使用SSL绕过模式）
        client = GStudioAPIClient(debug_mode=False, verify_ssl=False)
        
        # 设置Token
        token = "3dfb89119562456cb8818120139f6ae1"
        client.set_token(token)
        
        print("1. 获取真实书籍数据...")
        books = client.search_books("", page_size=3)
        
        if books:
            print(f"   ✓ 获取到 {len(books)} 本书籍")
            
            print("\n2. 分析书籍数据结构:")
            for i, book in enumerate(books, 1):
                print(f"\n   书籍 {i}:")
                print(f"     原始字段: {list(book.keys())}")
                
                # 标准化数据
                normalized = normalize_book_data(book)
                print(f"     标准化后字段: {list(normalized.keys())}")
                
                # 获取显示值
                display_values = get_book_display_values(normalized)
                print(f"     显示值:")
                print(f"       书籍ID: {display_values[0]}")
                print(f"       书籍名称: {display_values[1]}")
                print(f"       描述: {display_values[2]}")
                print(f"       制作状态: {display_values[3]}")
                print(f"       创建时间: {display_values[4]}")
                print(f"       编辑时间: {display_values[5]}")
                
                # 检查原始时间字段
                created_time_raw = get_book_field(book, 'created_time')
                updated_time_raw = get_book_field(book, 'updated_time')
                finished_raw = get_book_field(book, 'finished')
                
                print(f"     原始时间数据:")
                print(f"       createdTime: {created_time_raw} (类型: {type(created_time_raw)})")
                print(f"       updatedTime: {updated_time_raw} (类型: {type(updated_time_raw)})")
                print(f"       finished: {finished_raw} (类型: {type(finished_raw)})")
            
            return True
        else:
            print("   ⚠ 未获取到书籍数据")
            return False
            
    except Exception as e:
        print(f"   ✗ 获取真实API数据失败: {e}")
        return False


def test_table_display_simulation():
    """模拟表格显示测试"""
    print("\n" + "=" * 60)
    print("模拟表格显示测试")
    print("=" * 60)
    
    # 模拟书籍数据
    mock_books = [
        {
            "id": 39726,
            "name": "我真的不想当皇帝",
            "description": "一部精彩的小说",
            "finished": False,
            "createdTime": 1743654555189,
            "updatedTime": 1743754555189
        },
        {
            "id": 37749,
            "name": "官道之色戒（续）",
            "description": "官场小说续集",
            "finished": True,
            "createdTime": 1743554555189,
            "updatedTime": 1743654555189
        },
        {
            "bookId": "12345",
            "bookName": "经典文学作品",
            "description": "一部经典的文学作品，值得反复阅读",
            "finished": True,
            "createdTime": 1743454555,
            "updatedTime": 1743554555
        }
    ]
    
    print("模拟表格显示:")
    print("=" * 120)
    print(f"{'书籍ID':<8} {'书籍名称':<20} {'描述':<25} {'制作状态':<8} {'创建时间':<20} {'编辑时间':<20}")
    print("=" * 120)
    
    for book in mock_books:
        # 标准化数据
        normalized = normalize_book_data(book)
        
        # 获取显示值
        book_id, book_name, description, status, created_time, updated_time = get_book_display_values(normalized)
        
        # 截断过长的文本以适应表格显示
        book_name_display = book_name[:18] + ".." if len(book_name) > 20 else book_name
        description_display = description[:23] + ".." if len(description) > 25 else description
        
        print(f"{book_id:<8} {book_name_display:<20} {description_display:<25} {status:<8} {created_time:<20} {updated_time:<20}")
    
    print("=" * 120)
    
    return True


def main():
    """主测试函数"""
    print("GStudio 新表格列显示测试")
    print("=" * 60)
    print("测试修改后的书籍搜索界面表格列显示功能")
    print()
    
    # 设置日志
    setup_logging()
    
    success_count = 0
    total_tests = 5
    
    try:
        # 运行测试
        tests = [
            ("时间格式化功能", test_time_formatting),
            ("状态格式化功能", test_status_formatting),
            ("书籍显示值获取", test_book_display_values),
            ("真实API数据", test_real_api_data),
            ("表格显示模拟", test_table_display_simulation)
        ]
        
        for test_name, test_func in tests:
            try:
                if test_func():
                    print(f"✅ {test_name}测试通过")
                    success_count += 1
                else:
                    print(f"❌ {test_name}测试失败")
            except Exception as e:
                print(f"❌ {test_name}测试异常: {e}")
        
        print("\n" + "=" * 60)
        print("测试结果总结")
        print("=" * 60)
        
        print(f"通过测试: {success_count}/{total_tests}")
        
        if success_count == total_tests:
            print("🎉 所有测试通过！新表格列显示功能正常！")
            print()
            print("新表格列结构:")
            print("1. ✅ 书籍ID - 显示书籍的唯一标识")
            print("2. ✅ 书籍名称 - 显示书籍的标题")
            print("3. ✅ 描述 - 显示书籍的描述信息")
            print("4. ✅ 制作状态 - 显示'已完成'或'制作中'")
            print("5. ✅ 创建时间 - 格式化显示创建时间")
            print("6. ✅ 编辑时间 - 格式化显示最后编辑时间")
            print()
            print("功能特性:")
            print("• 时间戳自动格式化为 YYYY-MM-DD HH:MM:SS")
            print("• 制作状态中文显示")
            print("• 兼容新旧API数据格式")
            print("• 合理的列宽度分配")
        else:
            print("⚠ 部分测试未通过，请检查失败的测试项目")
        
    except Exception as e:
        print(f"测试过程中发生异常: {e}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    main()
