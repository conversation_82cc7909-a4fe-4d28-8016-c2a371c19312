# GStudio API 项目 .gitignore 文件
# 用于忽略不需要版本控制的文件和目录

# =============================================================================
# Python 相关文件
# =============================================================================

# 字节码文件
__pycache__/
*.py[cod]
*$py.class

# C 扩展
*.so

# 分发 / 打包
.Python
build/
develop-eggs/
dist/
downloads/
eggs/
.eggs/
lib/
lib64/
parts/
sdist/
var/
wheels/
share/python-wheels/
*.egg-info/
.installed.cfg
*.egg
MANIFEST

# PyInstaller
#  通常这些文件是由 python 脚本从模板自动生成的
#  pip install 之前，它们应该被删除
*.manifest
*.spec

# 单元测试 / 覆盖率报告
htmlcov/
.tox/
.nox/
.coverage
.coverage.*
.cache
nosetests.xml
coverage.xml
*.cover
*.py,cover
.hypothesis/
.pytest_cache/
cover/

# Jupyter Notebook
.ipynb_checkpoints

# IPython
profile_default/
ipython_config.py

# pyenv
#   对于使用 pyenv 的库，建议在 README 中包含 .python-version
.python-version

# pipenv
#   根据官方建议，Pipfile.lock 应该被版本控制
#   但是，在协作项目中，建议忽略它
Pipfile.lock

# poetry
#   类似于 Pipfile.lock，通常建议忽略 poetry.lock
#poetry.lock

# pdm
#   类似于 Pipfile.lock，通常建议忽略 pdm.lock
#pdm.lock
__pypackages__/

# Celery
celerybeat-schedule
celerybeat.pid

# SageMath 解析文件
*.sage.py

# =============================================================================
# 虚拟环境
# =============================================================================

# 虚拟环境目录
venv/
env/
ENV/
env.bak/
venv.bak/
.venv/
.env/

# Conda 环境
.conda/

# =============================================================================
# IDE 和编辑器配置文件
# =============================================================================

# Visual Studio Code
.vscode/
*.code-workspace

# PyCharm
.idea/
*.iml
*.ipr
*.iws

# Sublime Text
*.sublime-project
*.sublime-workspace

# Vim
*.swp
*.swo
*~

# Emacs
*~
\#*\#
/.emacs.desktop
/.emacs.desktop.lock
*.elc
auto-save-list
tramp
.\#*

# Atom
.atom/

# =============================================================================
# 操作系统生成的文件
# =============================================================================

# Windows
Thumbs.db
Thumbs.db:encryptable
ehthumbs.db
ehthumbs_vista.db
*.stackdump
[Dd]esktop.ini
$RECYCLE.BIN/
*.cab
*.msi
*.msix
*.msm
*.msp
*.lnk

# macOS
.DS_Store
.AppleDouble
.LSOverride
Icon
._*
.DocumentRevisions-V100
.fseventsd
.Spotlight-V100
.TemporaryItems
.Trashes
.VolumeIcon.icns
.com.apple.timemachine.donotpresent
.AppleDB
.AppleDesktop
Network Trash Folder
Temporary Items
.apdisk

# Linux
*~
.fuse_hidden*
.directory
.Trash-*
.nfs*

# =============================================================================
# 日志文件和临时文件
# =============================================================================

# 日志文件
*.log
logs/
log/

# 临时文件
*.tmp
*.temp
*.bak
*.backup
*.old
*.orig

# 运行时文件
*.pid
*.seed
*.pid.lock

# =============================================================================
# 项目特定文件
# =============================================================================

# API 调用记录文档目录（包含敏感的API调用信息、认证令牌、请求详情等）
# API 调用记录文档/

# 生成的音频文件
*.mp3
*.wav
*.m4a
*.aac
*.ogg
*.flac
tts_audio_*
basic_tts.*
full_tts.*
custom_tts.*

# API 调用记录的敏感信息
*_with_token.txt
*_sensitive.txt

# 配置文件（可能包含敏感信息）
config.ini
config.json
.env
.env.local
.env.*.local
secrets.json
credentials.json

# 测试输出文件
test_output/
test_results/
test_*.mp3

# 文档生成的临时文件
docs/_build/
docs/build/
site/

# =============================================================================
# 数据库文件
# =============================================================================

# SQLite
*.db
*.sqlite
*.sqlite3

# =============================================================================
# 其他工具生成的文件
# =============================================================================

# mypy
.mypy_cache/
.dmypy.json
dmypy.json

# Pyre type checker
.pyre/

# pytype static type analyzer
.pytype/

# Cython debug symbols
cython_debug/

# Rope project settings
.ropeproject

# mkdocs documentation
/site

# Sphinx documentation
docs/_build/

# =============================================================================
# 自定义忽略规则
# =============================================================================

# 个人笔记和草稿
notes.txt
draft/
scratch/
personal/

# 备份文件
backup/
backups/

# 压缩文件
*.zip
*.tar.gz
*.rar
*.7z

# 大型数据文件
*.csv
*.xlsx
*.json
data/
datasets/

# 性能分析文件
*.prof
*.profile

# 内存转储文件
*.dump
*.dmp

# 本地开发用的脚本
local_*.py
dev_*.py
debug_*.py
