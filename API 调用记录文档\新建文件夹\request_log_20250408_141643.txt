
==================================================
=== 请求信息 ===
时间: 2025-04-08 14:18:52
方法: POST
URL: https://www.gstudios.com.cn/story/open/user/login
备注说明: 选择项目组账号登入，POST
Headers: {
  "host": "www.gstudios.com.cn",
  "connection": "keep-alive",
  "sec-ch-ua-platform": "Windows",
  "x-requested-with": "XMLHttpRequest",
  "user-agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********",
  "accept": "application/json, text/plain, */*",
  "sec-ch-ua": "\"Microsoft Edge\";v=\"135\", \"Not-A.<PERSON>\";v=\"8\", \"Chromium\";v=\"135\"",
  "sec-ch-ua-mobile": "?0",
  "sec-fetch-site": "same-origin",
  "sec-fetch-mode": "cors",
  "sec-fetch-dest": "empty",
  "accept-encoding": "gzip, deflate, br, zstd",
  "accept-language": "zh-CN,zh;q=0.9,en;q=0.8,en-GB;q=0.7,en-US;q=0.6",
  "authorization": "Bearer d22e0aabbc6441dea7c113d07d0a4ed9"
}
Cookie: collabOperation=false; isVipClubMember=true; wxToken=46385c92df3a44ef9cf3c784d8bdba2a
请求数据: {
  "id": 1214,
  "token": "46385c92df3a44ef9cf3c784d8bdba2a"
}
=== 响应信息 ===
状态码: 200
响应头: {'server': 'CLOUD ELB 1.0.0', 'date': 'Tue, 08 Apr 2025 06:18:50 GMT', 'content-type': 'application/json;charset=UTF-8', 'transfer-encoding': 'chunked', 'connection': 'close', 'x-powered-by': 'Express', 'vary': 'Accept-Encoding', 'content-encoding': 'gzip'}

=== 响应内容 ===
{
  "code": 1,
  "data": {
    "access_token": "d22e0aabbc6441dea7c113d07d0a4ed9",
    "expires_in": "",
    "refresh_token": "",
    "scope": "",
    "token_type": ""
  },
  "msg": "成功!"
}

