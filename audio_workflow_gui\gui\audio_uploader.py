#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
音频上传模块

实现音频文件批量上传，支持断点续传和详细进度显示。

作者：Augment Agent
版本：1.0.0
"""

import tkinter as tk
from tkinter import ttk, messagebox, filedialog
import threading
import time
from typing import List, Dict, Any, Optional
from pathlib import Path

from utils.logger import LoggerMixin
from api.models import VoiceUploadParams
from utils.table_styles import setup_striped_table, refresh_table_stripes


class AudioUploaderFrame(ttk.Frame, LoggerMixin):
    """音频上传框架"""
    
    def __init__(self, parent, main_window):
        """
        初始化音频上传框架
        
        Args:
            parent: 父窗口
            main_window: 主窗口实例
        """
        super().__init__(parent)
        self.main_window = main_window
        self.generated_audios = []
        self.upload_results = []
        self.upload_cancelled = False
        self.upload_paused = False
        
        self._create_widgets()
        self._setup_layout()
        self._bind_events()
        
        self.logger.info("音频上传模块初始化完成")
    
    def _create_widgets(self):
        """创建界面组件"""
        # 移除音频文件信息区域（信息现在显示在工具栏中）
        
        # 上传配置区域
        config_frame = ttk.LabelFrame(self, text="上传配置", padding=10)

        # 配置选项（单行水平布局）
        config_options_frame = ttk.Frame(config_frame)

        # 并发上传数
        ttk.Label(config_options_frame, text="并发上传数:").pack(side=tk.LEFT, padx=(0, 5))
        self.concurrent_uploads_var = tk.IntVar(value=3)
        self.concurrent_uploads_entry = ttk.Entry(config_options_frame, textvariable=self.concurrent_uploads_var, width=8)
        self.concurrent_uploads_entry.pack(side=tk.LEFT, padx=(0, 15))

        # 重试次数
        ttk.Label(config_options_frame, text="重试次数:").pack(side=tk.LEFT, padx=(0, 5))
        self.retry_count_var = tk.IntVar(value=3)
        self.retry_count_entry = ttk.Entry(config_options_frame, textvariable=self.retry_count_var, width=8)
        self.retry_count_entry.pack(side=tk.LEFT, padx=(0, 15))

        # 超时时间
        ttk.Label(config_options_frame, text="超时时间(秒):").pack(side=tk.LEFT, padx=(0, 5))
        self.timeout_var = tk.IntVar(value=60)
        self.timeout_entry = ttk.Entry(config_options_frame, textvariable=self.timeout_var, width=8)
        self.timeout_entry.pack(side=tk.LEFT, padx=(0, 15))

        # 上传模式
        ttk.Label(config_options_frame, text="上传模式:").pack(side=tk.LEFT, padx=(0, 5))
        self.upload_mode_var = tk.StringVar(value="覆盖")
        upload_mode_combo = ttk.Combobox(
            config_options_frame,
            textvariable=self.upload_mode_var,
            values=["覆盖", "跳过", "重命名"],
            state="readonly",
            width=10
        )
        upload_mode_combo.pack(side=tk.LEFT, padx=(0, 10))
        
        # 上传控制区域
        control_frame = ttk.LabelFrame(self, text="上传控制", padding=10)
        
        # 控制按钮
        control_buttons_frame = ttk.Frame(control_frame)
        
        self.start_upload_button = ttk.Button(
            control_buttons_frame,
            text="开始上传",
            command=self._start_upload,
            state=tk.DISABLED
        )
        self.start_upload_button.pack(side=tk.LEFT, padx=(0, 5))
        
        self.pause_upload_button = ttk.Button(
            control_buttons_frame,
            text="暂停上传",
            command=self._pause_upload,
            state=tk.DISABLED
        )
        self.pause_upload_button.pack(side=tk.LEFT, padx=(0, 5))
        
        self.resume_upload_button = ttk.Button(
            control_buttons_frame,
            text="继续上传",
            command=self._resume_upload,
            state=tk.DISABLED
        )
        self.resume_upload_button.pack(side=tk.LEFT, padx=(0, 5))
        
        self.cancel_upload_button = ttk.Button(
            control_buttons_frame,
            text="取消上传",
            command=self._cancel_upload,
            state=tk.DISABLED
        )
        self.cancel_upload_button.pack(side=tk.LEFT, padx=(0, 10))

        # 将操作按钮移动到取消上传按钮右侧
        self.retry_failed_button = ttk.Button(
            control_buttons_frame,
            text="重试失败项",
            command=self._retry_failed_uploads,
            state=tk.DISABLED
        )
        self.retry_failed_button.pack(side=tk.LEFT, padx=(0, 5))

        self.export_results_button = ttk.Button(
            control_buttons_frame,
            text="导出结果",
            command=self._export_upload_results,
            state=tk.DISABLED
        )
        self.export_results_button.pack(side=tk.LEFT, padx=(0, 5))

        self.view_details_button = ttk.Button(
            control_buttons_frame,
            text="查看详情",
            command=self._view_upload_details,
            state=tk.DISABLED
        )
        self.view_details_button.pack(side=tk.LEFT, padx=(0, 5))

        self.finish_button = ttk.Button(
            control_buttons_frame,
            text="完成工作流",
            command=self._finish_workflow,
            state=tk.DISABLED
        )
        self.finish_button.pack(side=tk.LEFT, padx=(0, 5))
        
        # 进度显示
        progress_frame = ttk.Frame(control_frame)
        
        ttk.Label(progress_frame, text="上传进度:").pack(side=tk.LEFT, padx=(0, 5))
        self.upload_progress_var = tk.DoubleVar()
        self.upload_progress_bar = ttk.Progressbar(
            progress_frame,
            variable=self.upload_progress_var,
            maximum=100,
            length=300
        )
        self.upload_progress_bar.pack(side=tk.LEFT, padx=(0, 5))
        
        self.upload_status_label = ttk.Label(progress_frame, text="就绪")
        self.upload_status_label.pack(side=tk.LEFT, padx=(5, 0))
        
        # 上传结果区域
        results_frame = ttk.LabelFrame(self, text="上传结果", padding=10)
        
        # 创建Treeview显示上传结果
        columns = ("cue_id", "seq_num", "file_name", "status", "upload_time", "material_id", "error_msg")
        self.results_tree = ttk.Treeview(results_frame, columns=columns, show="headings", height=12)
        
        # 设置列标题
        self.results_tree.heading("cue_id", text="段落ID")
        self.results_tree.heading("seq_num", text="序号")
        self.results_tree.heading("file_name", text="文件名")
        self.results_tree.heading("status", text="状态")
        self.results_tree.heading("upload_time", text="上传时间")
        self.results_tree.heading("material_id", text="材料ID")
        self.results_tree.heading("error_msg", text="错误信息")
        
        # 设置列宽
        self.results_tree.column("cue_id", width=80)
        self.results_tree.column("seq_num", width=60)
        self.results_tree.column("file_name", width=150)
        self.results_tree.column("status", width=80)
        self.results_tree.column("upload_time", width=120)
        self.results_tree.column("material_id", width=100)
        self.results_tree.column("error_msg", width=200)
        
        # 添加滚动条
        self.results_scrollbar_y = ttk.Scrollbar(results_frame, orient=tk.VERTICAL, command=self.results_tree.yview)
        self.results_scrollbar_x = ttk.Scrollbar(results_frame, orient=tk.HORIZONTAL, command=self.results_tree.xview)
        self.results_tree.configure(yscrollcommand=self.results_scrollbar_y.set, xscrollcommand=self.results_scrollbar_x.set)

        # 应用条纹化样式美化表格
        setup_striped_table(self.results_tree, auto_update=True)
        
        # 统计信息区域
        stats_frame = ttk.Frame(self)
        
        self.stats_label = ttk.Label(stats_frame, text="统计信息: 未开始")
        self.stats_label.pack(side=tk.LEFT)
        
        # 操作按钮区域（按钮已移动到控制按钮区域）
        operations_frame = ttk.Frame(self)
        
        # 保存组件引用
        self.config_frame = config_frame
        self.config_options_frame = config_options_frame
        self.control_frame = control_frame
        self.control_buttons_frame = control_buttons_frame
        self.progress_frame = progress_frame
        self.results_frame = results_frame
        self.stats_frame = stats_frame
        self.operations_frame = operations_frame
    
    def _setup_layout(self):
        """设置布局"""
        self.config_frame.pack(fill=tk.X, pady=(0, 10))
        self.config_options_frame.pack(fill=tk.X)
        
        self.control_frame.pack(fill=tk.X, pady=(0, 10))
        self.control_buttons_frame.pack(fill=tk.X, pady=(0, 5))
        self.progress_frame.pack(fill=tk.X)
        
        self.results_frame.pack(fill=tk.BOTH, expand=True, pady=(0, 10))
        
        # 结果树形视图布局
        self.results_tree.grid(row=0, column=0, sticky="nsew")
        self.results_scrollbar_y.grid(row=0, column=1, sticky="ns")
        self.results_scrollbar_x.grid(row=1, column=0, sticky="ew")
        
        self.results_frame.grid_rowconfigure(0, weight=1)
        self.results_frame.grid_columnconfigure(0, weight=1)
        
        self.stats_frame.pack(fill=tk.X, pady=(0, 10))
    
    def _bind_events(self):
        """绑定事件"""
        # 标签页激活时更新文件信息
        self.bind("<Visibility>", self._on_visibility_changed)
        
        # 结果树选择事件
        self.results_tree.bind("<<TreeviewSelect>>", self._on_result_select)
    
    def _on_visibility_changed(self, event):
        """标签页可见性改变事件"""
        if event.widget == self:
            self._update_files_info()
    
    def _on_result_select(self, event):
        """结果选择事件处理"""
        selection = self.results_tree.selection()
        if selection:
            self.view_details_button.config(state=tk.NORMAL)
        else:
            self.view_details_button.config(state=tk.DISABLED)
    
    def _update_files_info(self):
        """更新文件信息显示"""
        workflow_state = self.main_window.get_workflow_state()
        generated_audios = workflow_state.get('generated_audios', [])

        # 筛选成功生成的音频
        self.generated_audios = [
            audio for audio in generated_audios
            if audio.get('status') == '成功' and audio.get('file_path')
        ]

        if self.generated_audios:
            # 启用开始上传按钮
            self.start_upload_button.config(state=tk.NORMAL)
        else:
            # 禁用开始上传按钮
            self.start_upload_button.config(state=tk.DISABLED)

    def _start_upload(self):
        """开始上传音频"""
        if not self.generated_audios:
            messagebox.showwarning("提示", "没有可上传的音频文件")
            return

        # 验证配置
        if not self._validate_upload_config():
            return

        # 重置状态
        self.upload_cancelled = False
        self.upload_paused = False
        self.upload_results = []

        # 清空结果列表
        for item in self.results_tree.get_children():
            self.results_tree.delete(item)

        # 更新按钮状态
        self.start_upload_button.config(state=tk.DISABLED)
        self.pause_upload_button.config(state=tk.NORMAL)
        self.cancel_upload_button.config(state=tk.NORMAL)

        # 开始上传
        self.main_window.update_status("开始上传音频...")

        def upload_thread():
            try:
                self._upload_audio_batch()
            except Exception as e:
                self.logger.error(f"音频上传失败: {e}")
                self.after(0, lambda: self._handle_upload_error(str(e)))

        threading.Thread(target=upload_thread, daemon=True).start()

    def _validate_upload_config(self) -> bool:
        """验证上传配置"""
        try:
            concurrent = self.concurrent_uploads_var.get()
            retry_count = self.retry_count_var.get()
            timeout = self.timeout_var.get()

            if not (1 <= concurrent <= 10):
                messagebox.showerror("配置错误", "并发上传数应在1-10之间")
                return False

            if not (0 <= retry_count <= 10):
                messagebox.showerror("配置错误", "重试次数应在0-10之间")
                return False

            if not (10 <= timeout <= 300):
                messagebox.showerror("配置错误", "超时时间应在10-300秒之间")
                return False

            return True

        except tk.TclError:
            messagebox.showerror("配置错误", "请输入有效的数值")
            return False

    def _upload_audio_batch(self):
        """批量上传音频"""
        api_client = self._get_api_client()
        if not api_client:
            return

        total_count = len(self.generated_audios)
        completed_count = 0

        for i, audio_info in enumerate(self.generated_audios):
            if self.upload_cancelled:
                break

            # 检查暂停状态
            while self.upload_paused and not self.upload_cancelled:
                time.sleep(0.1)

            if self.upload_cancelled:
                break

            try:
                # 更新进度
                progress = (i / total_count) * 100
                self.after(0, lambda p=progress, idx=i+1, total=total_count:
                          self._update_upload_progress(p, f"正在上传 {idx}/{total}"))

                paragraph = audio_info['paragraph']
                file_path = Path(audio_info['file_path'])

                # 检查文件是否存在
                if not file_path.exists():
                    raise FileNotFoundError(f"文件不存在: {file_path}")

                # 创建上传参数
                upload_params = VoiceUploadParams(
                    file=str(file_path),
                    cueId=paragraph['cue_id'],
                    filename=file_path.name
                )

                # 执行上传（带重试）
                upload_result = self._upload_with_retry(api_client, upload_params)

                # 记录上传结果
                result = {
                    'audio_info': audio_info,
                    'upload_result': upload_result,
                    'status': '成功',
                    'upload_time': time.strftime("%Y-%m-%d %H:%M:%S"),
                    'material_id': upload_result.data.get('materialId', '') if upload_result else '',
                    'error_msg': ''
                }
                self.upload_results.append(result)

                # 更新UI
                self.after(0, lambda r=result: self._add_upload_result(r))

                completed_count += 1

            except Exception as e:
                self.logger.error(f"上传音频失败 (段落ID: {paragraph['cue_id']}): {e}")

                # 记录失败结果
                result = {
                    'audio_info': audio_info,
                    'upload_result': None,
                    'status': '失败',
                    'upload_time': time.strftime("%Y-%m-%d %H:%M:%S"),
                    'material_id': '',
                    'error_msg': str(e)
                }
                self.upload_results.append(result)

                # 更新UI
                self.after(0, lambda r=result: self._add_upload_result(r))

        # 完成上传
        final_progress = 100 if not self.upload_cancelled else (completed_count / total_count) * 100
        status_text = "上传完成" if not self.upload_cancelled else "上传已取消"

        self.after(0, lambda: self._finish_upload(final_progress, status_text, completed_count, total_count))

    def _upload_with_retry(self, api_client, upload_params):
        """带重试的上传"""
        retry_count = self.retry_count_var.get()

        for attempt in range(retry_count + 1):
            try:
                return api_client.upload_voice(upload_params)
            except Exception as e:
                if attempt == retry_count:
                    raise e

                # 等待后重试
                time.sleep(2 ** attempt)  # 指数退避
                self.logger.warning(f"上传重试 {attempt + 1}/{retry_count}: {e}")

    def _update_upload_progress(self, progress: float, status: str):
        """更新上传进度"""
        self.upload_progress_var.set(progress)
        self.upload_status_label.config(text=status)
        self.main_window.update_progress(progress, status)

    def _add_upload_result(self, result: Dict[str, Any]):
        """添加上传结果到列表"""
        audio_info = result['audio_info']
        paragraph = audio_info['paragraph']
        file_path = Path(audio_info['file_path'])

        # 添加到树形视图
        self.results_tree.insert("", tk.END, values=(
            paragraph['cue_id'],
            paragraph['seq_num'],
            file_path.name,
            result['status'],
            result['upload_time'],
            result['material_id'],
            result['error_msg'][:50] + "..." if len(result['error_msg']) > 50 else result['error_msg']
        ))

        # 刷新表格条纹化效果
        refresh_table_stripes(self.results_tree)

        # 更新统计信息
        self._update_upload_stats()

    def _update_upload_stats(self):
        """更新上传统计信息"""
        total_count = len(self.upload_results)
        success_count = sum(1 for r in self.upload_results if r['status'] == '成功')
        failed_count = total_count - success_count

        stats_text = f"总计: {total_count}, 成功: {success_count}, 失败: {failed_count}"
        if total_count > 0:
            success_rate = (success_count / total_count) * 100
            stats_text += f" (成功率: {success_rate:.1f}%)"

        self.stats_label.config(text=f"统计信息: {stats_text}")

    def _finish_upload(self, progress: float, status: str, completed: int, total: int):
        """完成上传"""
        self._update_upload_progress(progress, status)

        # 更新按钮状态
        self.start_upload_button.config(state=tk.NORMAL)
        self.pause_upload_button.config(state=tk.DISABLED)
        self.resume_upload_button.config(state=tk.DISABLED)
        self.cancel_upload_button.config(state=tk.DISABLED)

        # 启用操作按钮
        if self.upload_results:
            self.export_results_button.config(state=tk.NORMAL)
            self.finish_button.config(state=tk.NORMAL)

            # 如果有失败的上传，启用重试按钮
            failed_count = sum(1 for r in self.upload_results if r['status'] == '失败')
            if failed_count > 0:
                self.retry_failed_button.config(state=tk.NORMAL)

        # 更新工作流状态
        self.main_window.update_workflow_state('upload_results', self.upload_results)

        # 显示完成消息
        success_count = sum(1 for r in self.upload_results if r['status'] == '成功')
        message = f"音频上传完成!\n成功: {success_count}/{total}\n失败: {total - success_count}"

        if success_count < total:
            messagebox.showwarning("上传完成", message)
        else:
            messagebox.showinfo("上传完成", message)

        self.main_window.update_status(f"音频上传完成: {success_count}/{total}")
        self.logger.info(f"音频上传完成: 成功 {success_count}, 失败 {total - success_count}")

    def _pause_upload(self):
        """暂停上传"""
        self.upload_paused = True
        self.pause_upload_button.config(state=tk.DISABLED)
        self.resume_upload_button.config(state=tk.NORMAL)
        self.upload_status_label.config(text="已暂停")
        self.logger.info("用户暂停了音频上传")

    def _resume_upload(self):
        """继续上传"""
        self.upload_paused = False
        self.pause_upload_button.config(state=tk.NORMAL)
        self.resume_upload_button.config(state=tk.DISABLED)
        self.upload_status_label.config(text="继续上传")
        self.logger.info("用户继续了音频上传")

    def _cancel_upload(self):
        """取消上传"""
        if messagebox.askyesno("确认", "确定要取消音频上传吗？"):
            self.upload_cancelled = True
            self.upload_paused = False
            self.logger.info("用户取消了音频上传")

    def _retry_failed_uploads(self):
        """重试失败的上传"""
        failed_results = [r for r in self.upload_results if r['status'] == '失败']

        if not failed_results:
            messagebox.showinfo("提示", "没有失败的上传项")
            return

        if messagebox.askyesno("确认", f"确定要重试 {len(failed_results)} 个失败的上传项吗？"):
            # 重置失败项的状态
            for result in failed_results:
                result['status'] = '待重试'
                result['error_msg'] = ''

            # 重新开始上传这些项目
            self._start_retry_upload(failed_results)

    def _start_retry_upload(self, retry_items: List[Dict[str, Any]]):
        """开始重试上传"""
        # 这里可以实现重试上传的逻辑
        messagebox.showinfo("提示", "重试上传功能待完善")

    def _export_upload_results(self):
        """导出上传结果"""
        if not self.upload_results:
            messagebox.showwarning("提示", "没有上传结果可导出")
            return

        filename = filedialog.asksaveasfilename(
            title="导出上传结果",
            defaultextension=".csv",
            filetypes=[("CSV文件", "*.csv"), ("文本文件", "*.txt"), ("所有文件", "*.*")]
        )

        if filename:
            try:
                import csv

                with open(filename, 'w', newline='', encoding='utf-8') as f:
                    writer = csv.writer(f)

                    # 写入标题行
                    writer.writerow([
                        "段落ID", "序号", "文件名", "状态", "上传时间", "材料ID", "错误信息"
                    ])

                    # 写入数据行
                    for result in self.upload_results:
                        audio_info = result['audio_info']
                        paragraph = audio_info['paragraph']
                        file_path = Path(audio_info['file_path'])

                        writer.writerow([
                            paragraph['cue_id'],
                            paragraph['seq_num'],
                            file_path.name,
                            result['status'],
                            result['upload_time'],
                            result['material_id'],
                            result['error_msg']
                        ])

                messagebox.showinfo("成功", f"上传结果已导出到: {filename}")

            except Exception as e:
                messagebox.showerror("错误", f"导出失败: {e}")

    def _view_upload_details(self):
        """查看上传详情"""
        selection = self.results_tree.selection()
        if not selection:
            messagebox.showwarning("提示", "请先选择一个上传结果")
            return

        item = self.results_tree.item(selection[0])
        cue_id = item['values'][0]

        # 找到对应的上传结果
        result = None
        for r in self.upload_results:
            if r['audio_info']['paragraph']['cue_id'] == cue_id:
                result = r
                break

        if result:
            # 创建详情窗口
            details_window = tk.Toplevel(self)
            details_window.title(f"上传详情 - 段落ID: {cue_id}")
            details_window.geometry("600x400")

            # 创建文本框显示详情
            text_frame = ttk.Frame(details_window)
            text_frame.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)

            details_text = tk.Text(text_frame, wrap=tk.WORD)
            scrollbar = ttk.Scrollbar(text_frame, orient=tk.VERTICAL, command=details_text.yview)
            details_text.configure(yscrollcommand=scrollbar.set)

            details_text.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
            scrollbar.pack(side=tk.RIGHT, fill=tk.Y)

            # 填充详情内容
            audio_info = result['audio_info']
            paragraph = audio_info['paragraph']

            details_content = f"上传详情\n"
            details_content += "=" * 50 + "\n\n"
            details_content += f"段落ID: {paragraph['cue_id']}\n"
            details_content += f"序号: {paragraph['seq_num']}\n"
            details_content += f"内容: {paragraph['text']}\n\n"
            details_content += f"文件路径: {audio_info['file_path']}\n"
            details_content += f"文件大小: {audio_info['file_size']} 字节\n\n"
            details_content += f"上传状态: {result['status']}\n"
            details_content += f"上传时间: {result['upload_time']}\n"
            details_content += f"材料ID: {result['material_id']}\n"

            if result['error_msg']:
                details_content += f"\n错误信息:\n{result['error_msg']}\n"

            if result['upload_result']:
                upload_data = result['upload_result'].data
                details_content += f"\n上传响应数据:\n"
                details_content += f"识别内容: {upload_data.get('content', '')}\n"
                details_content += f"有效时长: {upload_data.get('durationEffectiveMs', 0)} 毫秒\n"
                details_content += f"总时长: {upload_data.get('durationTotalMs', 0)} 毫秒\n"
                details_content += f"信噪比: {upload_data.get('snrDb', 0)} dB\n"

            details_text.insert(1.0, details_content)
            details_text.config(state=tk.DISABLED)

            # 关闭按钮
            close_button = ttk.Button(details_window, text="关闭", command=details_window.destroy)
            close_button.pack(pady=10)

    def _finish_workflow(self):
        """完成工作流"""
        success_count = sum(1 for r in self.upload_results if r['status'] == '成功')
        total_count = len(self.upload_results)

        message = f"音频工作流处理完成!\n\n"
        message += f"处理统计:\n"
        message += f"- 成功上传: {success_count}/{total_count}\n"
        message += f"- 失败上传: {total_count - success_count}\n\n"
        message += f"所有音频文件已处理完毕。"

        messagebox.showinfo("工作流完成", message)
        self.logger.info(f"音频工作流处理完成: 成功 {success_count}/{total_count}")

    def _handle_upload_error(self, error_message: str):
        """处理上传错误"""
        messagebox.showerror("上传失败", f"音频上传时发生错误:\n{error_message}")
        self.main_window.update_status("上传失败")

        # 恢复按钮状态
        self.start_upload_button.config(state=tk.NORMAL)
        self.pause_upload_button.config(state=tk.DISABLED)
        self.resume_upload_button.config(state=tk.DISABLED)
        self.cancel_upload_button.config(state=tk.DISABLED)

    def _get_api_client(self):
        """获取API客户端"""
        # 从书籍搜索模块获取API客户端
        book_search_frame = self.main_window.book_search_frame
        if hasattr(book_search_frame, 'api_client') and book_search_frame.api_client:
            return book_search_frame.api_client

        messagebox.showerror("错误", "API客户端未初始化，请先在书籍搜索页面进行搜索")
        return None
