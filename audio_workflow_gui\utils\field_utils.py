#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
字段兼容性工具模块

提供新旧API格式的字段兼容性访问方法。

作者：Augment Agent
版本：1.0.0
"""

from typing import Dict, Any, Optional
import datetime


def get_book_field(book: Dict[str, Any], field_name: str) -> Any:
    """
    获取书籍字段值，兼容新旧API格式
    
    Args:
        book: 书籍数据字典
        field_name: 字段名称 ('id', 'name', 'description', 'price')
        
    Returns:
        字段值，如果不存在则返回None
    """
    if not book:
        return None
    
    # 字段映射表：标准字段名 -> (新API字段, 旧API字段)
    field_mapping = {
        'id': ('id', 'bookId'),
        'name': ('name', 'bookName'),
        'description': ('description', 'description'),
        'remark': ('remark', 'remark'),
        'price': ('price', 'price'),
        'finished': ('finished', 'finished'),
        'created_time': ('createdTime', 'createdTime'),
        'updated_time': ('updatedTime', 'updatedTime')
    }
    
    if field_name not in field_mapping:
        # 如果不在映射表中，直接返回原字段值
        return book.get(field_name)
    
    new_field, old_field = field_mapping[field_name]
    
    # 优先使用新API字段，如果不存在则使用旧API字段
    return book.get(new_field, book.get(old_field))


def set_book_field(book: Dict[str, Any], field_name: str, value: Any) -> None:
    """
    设置书籍字段值，同时设置新旧API格式的字段
    
    Args:
        book: 书籍数据字典
        field_name: 字段名称
        value: 字段值
    """
    if not book:
        return
    
    field_mapping = {
        'id': ('id', 'bookId'),
        'name': ('name', 'bookName'),
        'description': ('description', 'description'),
        'remark': ('remark', 'remark'),
        'price': ('price', 'price'),
        'finished': ('finished', 'finished'),
        'created_time': ('createdTime', 'createdTime'),
        'updated_time': ('updatedTime', 'updatedTime')
    }
    
    if field_name in field_mapping:
        new_field, old_field = field_mapping[field_name]
        book[new_field] = value
        book[old_field] = value
    else:
        book[field_name] = value


def normalize_book_data(book: Dict[str, Any]) -> Dict[str, Any]:
    """
    标准化书籍数据，确保包含新旧API格式的字段
    
    Args:
        book: 原始书籍数据
        
    Returns:
        标准化后的书籍数据
    """
    if not book:
        return {}
    
    normalized = book.copy()
    
    # 确保ID字段
    book_id = get_book_field(book, 'id')
    if book_id is not None:
        normalized['id'] = book_id
        normalized['bookId'] = str(book_id)
    
    # 确保名称字段
    book_name = get_book_field(book, 'name')
    if book_name is not None:
        normalized['name'] = book_name
        normalized['bookName'] = book_name
    
    # 确保描述字段
    description = get_book_field(book, 'description')
    if description is not None:
        normalized['description'] = description
    
    # 确保备注字段
    remark = get_book_field(book, 'remark')
    if remark is not None:
        normalized['remark'] = remark
    
    # 确保价格字段
    price = get_book_field(book, 'price')
    if price is not None:
        normalized['price'] = price

    # 确保完成状态字段
    finished = get_book_field(book, 'finished')
    if finished is not None:
        normalized['finished'] = finished

    # 确保时间字段
    created_time = get_book_field(book, 'created_time')
    if created_time is not None:
        normalized['createdTime'] = created_time
        normalized['created_time'] = created_time

    updated_time = get_book_field(book, 'updated_time')
    if updated_time is not None:
        normalized['updatedTime'] = updated_time
        normalized['updated_time'] = updated_time

    return normalized


def find_book_by_id(books: list, book_id: Any) -> Optional[Dict[str, Any]]:
    """
    根据ID查找书籍，兼容新旧API格式
    
    Args:
        books: 书籍列表
        book_id: 书籍ID
        
    Returns:
        找到的书籍数据，如果未找到则返回None
    """
    if not books or book_id is None:
        return None
    
    # 转换为字符串进行比较，因为可能存在类型不一致的情况
    target_id = str(book_id)
    
    for book in books:
        # 检查新API格式的ID
        current_id = get_book_field(book, 'id')
        if current_id is not None and str(current_id) == target_id:
            return book
    
    return None


def format_book_display_info(book: Dict[str, Any]) -> str:
    """
    格式化书籍显示信息
    
    Args:
        book: 书籍数据
        
    Returns:
        格式化的显示文本
    """
    if not book:
        return "无书籍信息"
    
    book_id = get_book_field(book, 'id') or '未知'
    book_name = get_book_field(book, 'name') or '未知'
    description = get_book_field(book, 'description') or ''
    price = get_book_field(book, 'price') or 0
    
    info_text = f"书籍ID: {book_id}\n"
    info_text += f"书籍名称: {book_name}\n"
    
    if description:
        info_text += f"描述: {description}\n"
    
    if price:
        price_text = f"{price/100:.2f}元" if isinstance(price, (int, float)) and price > 0 else "免费"
        info_text += f"价格: {price_text}"
    else:
        info_text += "价格: 免费"
    
    return info_text


def format_timestamp(timestamp: Any) -> str:
    """
    格式化时间戳为可读时间

    Args:
        timestamp: 时间戳（毫秒或秒）

    Returns:
        格式化的时间字符串 (YYYY-MM-DD HH:MM:SS)
    """
    if not timestamp:
        return ""

    try:
        # 处理不同类型的时间戳
        if isinstance(timestamp, str):
            # 如果是字符串，尝试转换为数字
            timestamp = int(timestamp)
        elif not isinstance(timestamp, (int, float)):
            return ""

        # 判断是毫秒还是秒时间戳
        if timestamp > 1e10:  # 毫秒时间戳
            timestamp = timestamp / 1000

        # 转换为datetime对象
        dt = datetime.datetime.fromtimestamp(timestamp)

        # 格式化为字符串
        return dt.strftime("%Y-%m-%d %H:%M:%S")

    except (ValueError, OSError, OverflowError):
        return ""


def format_finished_status(finished: Any) -> str:
    """
    格式化制作状态

    Args:
        finished: 完成状态（布尔值或字符串）

    Returns:
        格式化的状态字符串
    """
    if finished is None:
        return "未知"

    # 处理布尔值
    if isinstance(finished, bool):
        return "已完成" if finished else "制作中"

    # 处理字符串
    if isinstance(finished, str):
        finished_lower = finished.lower()
        if finished_lower in ['true', '1', 'yes', 'completed', 'finished']:
            return "已完成"
        elif finished_lower in ['false', '0', 'no', 'in_progress', 'working']:
            return "制作中"

    # 处理数字
    if isinstance(finished, (int, float)):
        return "已完成" if finished else "制作中"

    return "未知"


def get_book_display_values(book: Dict[str, Any]) -> tuple:
    """
    获取书籍在列表中的显示值（新版本）

    Args:
        book: 书籍数据

    Returns:
        (book_id, book_name, description, status, created_time, updated_time) 元组
    """
    if not book:
        return ('', '', '', '', '', '')

    book_id = get_book_field(book, 'id') or ''
    book_name = get_book_field(book, 'name') or ''
    description = get_book_field(book, 'description') or get_book_field(book, 'remark') or ''
    finished = get_book_field(book, 'finished')
    created_time = get_book_field(book, 'created_time')
    updated_time = get_book_field(book, 'updated_time')

    # 格式化制作状态
    status = format_finished_status(finished)

    # 格式化时间
    created_time_str = format_timestamp(created_time)
    updated_time_str = format_timestamp(updated_time)

    # 截断过长的描述
    if description and len(description) > 40:
        description = description[:40] + "..."

    return (str(book_id), book_name, description, status, created_time_str, updated_time_str)


def get_book_display_values_legacy(book: Dict[str, Any]) -> tuple:
    """
    获取书籍在列表中的显示值（旧版本，保持兼容性）

    Args:
        book: 书籍数据

    Returns:
        (book_id, book_name, price_text, description) 元组
    """
    if not book:
        return ('', '', '', '')

    book_id = get_book_field(book, 'id') or ''
    book_name = get_book_field(book, 'name') or ''
    description = get_book_field(book, 'description') or get_book_field(book, 'remark') or ''
    price = get_book_field(book, 'price') or 0

    # 格式化价格
    if isinstance(price, (int, float)) and price > 0:
        price_text = f"{price/100:.2f}元"
    else:
        price_text = "免费"

    # 截断过长的描述
    if description and len(description) > 50:
        description = description[:50] + "..."

    return (str(book_id), book_name, price_text, description)
