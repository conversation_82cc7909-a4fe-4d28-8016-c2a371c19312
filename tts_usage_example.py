#!/usr/bin/env python3
"""
TTS API 使用示例
演示如何使用新添加的TTS试听功能
"""

import sys
import os
import requests
import json
from datetime import datetime

# 添加simple-gstudio-api目录到Python路径
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'simple-gstudio-api'))

from gstudio_api import (
    APIEndpoints,
    TTSTrialParams,
    GenMode,
    DefaultHeaders
)

def generate_tts_audio(token: str, params: TTSTrialParams, output_file: str = None) -> bool:
    """
    生成TTS试听音频
    
    Args:
        token: 认证令牌
        params: TTS参数
        output_file: 输出文件路径（可选）
    
    Returns:
        bool: 是否成功
    """
    try:
        # 构建请求URL
        url = APIEndpoints.get_url(APIEndpoints.Record.TTS_TRIAL)
        print(f"请求URL: {url}")
        
        # 构建请求头
        headers = DefaultHeaders.get_headers(token=token)
        print(f"请求头: {json.dumps(headers, indent=2, ensure_ascii=False)}")
        
        # 构建请求数据
        # 将dataclass转换为字典
        request_data = {
            "bookId": params.bookId,
            "cvRobotId": params.cvRobotId,
            "ssml": params.ssml,
            "genMode": params.genMode.value,
            "speedFactor": params.speedFactor,
            "durationFactorSilence": params.durationFactorSilence,
            "seed": params.seed
        }
        print(f"请求数据: {json.dumps(request_data, indent=2, ensure_ascii=False)}")
        
        # 发送请求
        print("\n发送TTS请求...")
        response = requests.post(url, headers=headers, json=request_data)
        
        print(f"响应状态码: {response.status_code}")
        print(f"响应头: {dict(response.headers)}")
        
        if response.status_code == 200:
            # 检查响应内容类型
            content_type = response.headers.get('content-type', '')
            print(f"Content-Type: {content_type}")
            
            if 'audio' in content_type.lower() or len(response.content) > 1000:
                # 这是音频数据
                audio_data = response.content
                print(f"✓ 成功获取音频数据，大小: {len(audio_data)} 字节")
                
                # 保存音频文件
                if output_file is None:
                    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
                    output_file = f"tts_audio_{timestamp}.mp3"
                
                with open(output_file, 'wb') as f:
                    f.write(audio_data)
                
                print(f"✓ 音频文件已保存: {output_file}")
                return True
            else:
                # 可能是JSON错误响应
                try:
                    error_data = response.json()
                    print(f"✗ API返回错误: {error_data}")
                except:
                    print(f"✗ 未知响应格式: {response.text[:200]}...")
                return False
        else:
            print(f"✗ 请求失败，状态码: {response.status_code}")
            try:
                error_data = response.json()
                print(f"错误信息: {error_data}")
            except:
                print(f"错误响应: {response.text}")
            return False
            
    except Exception as e:
        print(f"✗ 请求异常: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """主函数 - 演示TTS API使用"""
    print("=== TTS API 使用示例 ===\n")
    
    # 注意：这里需要替换为实际的认证令牌
    token = "3dfb89119562456cb8818120139f6ae1"
    
    if token == "your_actual_token_here":
        print("⚠️  请在代码中设置实际的认证令牌")
        print("   token = \"your_actual_token_here\"")
        print("\n以下是模拟演示（不会实际发送请求）：\n")
        demo_mode = True
    else:
        demo_mode = False
    
    # 示例1: 基本TTS参数
    print("示例1: 基本TTS参数")
    basic_params = TTSTrialParams(
        bookId="39726",
        cvRobotId=568,
        ssml="这是一个测试文本。"
    )
    print(f"  bookId: {basic_params.bookId}")
    print(f"  cvRobotId: {basic_params.cvRobotId}")
    print(f"  ssml: {basic_params.ssml}")
    print(f"  genMode: {basic_params.genMode} ({basic_params.genMode.value})")
    print(f"  speedFactor: {basic_params.speedFactor}")
    print(f"  durationFactorSilence: {basic_params.durationFactorSilence}")
    print(f"  seed: {basic_params.seed}")
    
    if not demo_mode:
        success = generate_tts_audio(token, basic_params, "basic_tts.mp3")
        print(f"结果: {'成功' if success else '失败'}")
    
    print("\n" + "="*50 + "\n")
    
    # 示例2: 完整TTS参数（与实际API调用记录一致）
    print("示例2: 完整TTS参数（与API调用记录一致）")
    full_params = TTSTrialParams(
        bookId="39726",
        cvRobotId=568,
        ssml="在一道斥责声中，林止陌迷迷糊糊的抬起头，一道模糊的窈窕身影，慢慢的在他眼前变得清晰起来。",
        genMode=GenMode.DEFAULT,
        speedFactor=100,
        durationFactorSilence=100,
        seed=None
    )
    print(f"  bookId: {full_params.bookId}")
    print(f"  cvRobotId: {full_params.cvRobotId}")
    print(f"  ssml: {full_params.ssml}")
    print(f"  genMode: {full_params.genMode}")
    print(f"  speedFactor: {full_params.speedFactor}")
    print(f"  durationFactorSilence: {full_params.durationFactorSilence}")
    print(f"  seed: {full_params.seed}")
    
    if not demo_mode:
        success = generate_tts_audio(token, full_params, "full_tts.mp3")
        print(f"结果: {'成功' if success else '失败'}")
    
    print("\n" + "="*50 + "\n")
    
    # 示例3: 自定义语速的TTS参数
    print("示例3: 自定义语速的TTS参数")
    custom_params = TTSTrialParams(
        bookId="39726",
        cvRobotId=568,
        ssml="这是一个语速较快的测试。",
        genMode=GenMode.DEFAULT,
        speedFactor=120,  # 加快语速
        durationFactorSilence=80,  # 减少静音时长
        seed=12345  # 固定随机种子
    )
    print(f"  bookId: {custom_params.bookId}")
    print(f"  cvRobotId: {custom_params.cvRobotId}")
    print(f"  ssml: {custom_params.ssml}")
    print(f"  genMode: {custom_params.genMode}")
    print(f"  speedFactor: {custom_params.speedFactor} (加快语速)")
    print(f"  durationFactorSilence: {custom_params.durationFactorSilence} (减少静音)")
    print(f"  seed: {custom_params.seed} (固定种子)")
    
    if not demo_mode:
        success = generate_tts_audio(token, custom_params, "custom_tts.mp3")
        print(f"结果: {'成功' if success else '失败'}")
    
    print("\n" + "="*50 + "\n")
    
    # 显示API端点信息
    print("API端点信息:")
    tts_url = APIEndpoints.get_url(APIEndpoints.Record.TTS_TRIAL)
    print(f"  TTS试听: {tts_url}")
    
    cv_info_url = APIEndpoints.get_url(APIEndpoints.Record.CHAPTER_CV_INFO)
    print(f"  章节CV信息: {cv_info_url}")
    
    cv_cancel_url = APIEndpoints.get_url(APIEndpoints.Record.CHAPTER_CV_CANCEL)
    print(f"  章节CV取消: {cv_cancel_url}")
    
    print("\n使用说明:")
    print("1. 设置正确的认证令牌")
    print("2. 根据需要调整TTS参数")
    print("3. 调用generate_tts_audio函数")
    print("4. 生成的音频文件为MP3格式")
    print("5. 注意：此API返回二进制数据，不是JSON响应")

if __name__ == "__main__":
    main()
