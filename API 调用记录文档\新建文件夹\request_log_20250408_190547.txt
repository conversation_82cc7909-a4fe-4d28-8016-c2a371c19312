
==================================================
=== 请求信息 ===
时间: 2025-04-08 19:09:13
方法: POST
URL: https://www.gstudios.com.cn/story_v2/api/content/book/relation/freelancer/invite
备注标题: 邀请新合作成员
备注内容: 
Headers: {
  "host": "www.gstudios.com.cn",
  "connection": "keep-alive",
  "sec-ch-ua-platform": "\"Windows\"",
  "x-requested-with": "XMLHttpRequest",
  "user-agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********",
  "accept": "application/json, text/plain, */*",
  "sec-ch-ua": "\"Microsoft Edge\";v=\"135\", \"Not-A.Brand\";v=\"8\", \"Chromium\";v=\"135\"",
  "sec-ch-ua-mobile": "?0",
  "sec-fetch-site": "same-origin",
  "sec-fetch-mode": "cors",
  "sec-fetch-dest": "empty",
  "accept-encoding": "gzip, deflate, br, zstd",
  "accept-language": "zh-CN,zh;q=0.9,en;q=0.8,en-GB;q=0.7,en-US;q=0.6",
  "authorization": "Bearer d22e0aabbc6441dea7c113d07d0a4ed9",
  "content-length": "61",
  "content-type": "application/json",
  "origin": "https://www.gstudios.com.cn",
  "cookie": "collabOperation=false; isVipClubMember=true; playerId=1214; wxToken=46385c92df3a44ef9cf3c784d8bdba2a; user_type=0; user_role=3; user_name=A%E9%A3%9E%E6%89%AC; access_token=d22e0aabbc6441dea7c113d07d0a4ed9; group_name=%E4%BA%9A%E8%A7%86%E6%9C%89%E5%A3%B0; cid=null; gcid=67516"
}
Cookie: collabOperation=false; isVipClubMember=true; playerId=1214; wxToken=46385c92df3a44ef9cf3c784d8bdba2a; user_type=0; user_role=3; user_name=A%E9%A3%9E%E6%89%AC; access_token=d22e0aabbc6441dea7c113d07d0a4ed9; group_name=%E4%BA%9A%E8%A7%86%E6%9C%89%E5%A3%B0; cid=null; gcid=67516
请求数据: {
  "relationType": 3,
  "monthly": false,
  "price": 0,
  "bookId": "33524"
}
=== 响应信息 ===
状态码: 200
响应头: {'server': 'CLOUD ELB 1.0.0', 'date': 'Tue, 08 Apr 2025 11:09:10 GMT', 'content-type': 'application/json;charset=UTF-8', 'transfer-encoding': 'chunked', 'connection': 'close', 'x-powered-by': 'Express', 'vary': 'Accept-Encoding', 'content-encoding': 'gzip'}

=== 响应内容 ===
{
  "code": 1,
  "data": {
    "bookName": "知乎合集-森森专辑",
    "price": 0,
    "redeemCode": "7mqzEwJK",
    "relationType": 3
  },
  "msg": "成功!"
}


==================================================
=== 请求信息 ===
时间: 2025-04-08 19:10:32
方法: GET
URL: https://www.gstudios.com.cn/story_v2/api/content/book/relation/freelancer/by/book?bookId=33524&freelancerName=&sortItem=&sortAsc=
备注标题: 获取已邀请合作成员列表
备注内容: 
Headers: {
  "host": "www.gstudios.com.cn",
  "connection": "keep-alive",
  "sec-ch-ua-platform": "Windows",
  "x-requested-with": "XMLHttpRequest",
  "user-agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********",
  "accept": "application/json, text/plain, */*",
  "sec-ch-ua": "\"Microsoft Edge\";v=\"135\", \"Not-A.Brand\";v=\"8\", \"Chromium\";v=\"135\"",
  "sec-ch-ua-mobile": "?0",
  "sec-fetch-site": "same-origin",
  "sec-fetch-mode": "cors",
  "sec-fetch-dest": "empty",
  "accept-encoding": "gzip, deflate, br, zstd",
  "accept-language": "zh-CN,zh;q=0.9,en;q=0.8,en-GB;q=0.7,en-US;q=0.6",
  "authorization": "Bearer d22e0aabbc6441dea7c113d07d0a4ed9",
  "referer": "https://www.gstudios.com.cn/"
}
Cookie: collabOperation=false; isVipClubMember=true; playerId=1214; wxToken=46385c92df3a44ef9cf3c784d8bdba2a; user_type=0; user_role=3; user_name=A%E9%A3%9E%E6%89%AC; access_token=d22e0aabbc6441dea7c113d07d0a4ed9; group_name=%E4%BA%9A%E8%A7%86%E6%9C%89%E5%A3%B0; cid=null; gcid=67516
=== 响应信息 ===
状态码: 200
响应头: {'server': 'CLOUD ELB 1.0.0', 'date': 'Tue, 08 Apr 2025 11:10:29 GMT', 'content-type': 'application/json;charset=UTF-8', 'transfer-encoding': 'chunked', 'connection': 'close', 'x-powered-by': 'Express', 'vary': 'Accept-Encoding', 'content-encoding': 'gzip'}

=== 响应内容 ===
{
  "code": 1,
  "data": {
    "list": [
      {
        "monthly": false,
        "playerAvatar": "",
        "playerDesc": "",
        "playerName": "罗兰之猗猗",
        "price": 500,
        "priceProposal": null,
        "proposalConfirmedTime": null,
        "proposalState": 0,
        "redeemCode": "AQKJOJkB",
        "redeemCreatedTime": 1743582984337,
        "redeemId": 186351,
        "redeemState": 1,
        "redeemSubjectId": 30317,
        "redeemSubjectName": "罗兰之猗猗",
        "relationEnabled": 1,
        "relationId": 206157,
        "relationType": 0
      },
      {
        "monthly": false,
        "playerAvatar": "",
        "playerDesc": "",
        "playerName": "",
        "price": 30000,
        "priceProposal": null,
        "proposalConfirmedTime": null,
        "proposalState": null,
        "redeemCode": "HbHd4x83",
        "redeemCreatedTime": 1744105185477,
        "redeemId": 188854,
        "redeemState": 0,
        "redeemSubjectId": 157,
        "redeemSubjectName": "金阅微",
        "relationEnabled": null,
        "relationId": null,
        "relationType": 3
      },
      {
        "monthly": false,
        "playerAvatar": "",
        "playerDesc": "",
        "playerName": "",
        "price": 25000,
        "priceProposal": null,
        "proposalConfirmedTime": null,
        "proposalState": null,
        "redeemCode": "Q79d5YLo",
        "redeemCreatedTime": 1744105185338,
        "redeemId": 188853,
        "redeemState": 0,
        "redeemSubjectId": 88,
        "redeemSubjectName": "曹先森1",
        "relationEnabled": null,
        "relationId": null,
        "relationType": 3
      },
      {
        "monthly": false,
        "playerAvatar": "",
        "playerDesc": "",
        "playerName": "",
        "price": 20000,
        "priceProposal": null,
        "proposalConfirmedTime": null,
        "proposalState": null,
        "redeemCode": "AwK8x6Iq",
        "redeemCreatedTime": 1744105456073,
        "redeemId": 188864,
        "redeemState": 0,
        "redeemSubjectId": 148,
        "redeemSubjectName": "银耳",
        "relationEnabled": null,
        "relationId": null,
        "relationType": 3
      },
      {
        "monthly": false,
        "playerAvatar": "",
        "playerDesc": "",
        "playerName": "",
        "price": 15000,
        "priceProposal": null,
        "proposalConfirmedTime": null,
        "proposalState": null,
        "redeemCode": "T3o911TL",
        "redeemCreatedTime": 1744105185622,
        "redeemId": 188855,
        "redeemState": 0,
        "redeemSubjectId": 139,
        "redeemSubjectName": "会跳舞的铁匠",
        "relationEnabled": null,
        "relationId": null,
        "relationType": 3
      },
      {
        "monthly": false,
        "playerAvatar": "",
        "playerDesc": "",
        "playerName": "",
        "price": 0,
        "priceProposal": null,
        "proposalConfirmedTime": null,
        "proposalState": null,
        "redeemCode": "SGO8E2hf",
        "redeemCreatedTime": 1744110290114,
        "redeemId": 188893,
        "redeemState": 0,
        "redeemSubjectId": null,
        "redeemSubjectName": "",
        "relationEnabled": null,
        "relationId": null,
        "relationType": 3
      },
      {
        "monthly": false,
        "playerAvatar": "",
        "playerDesc": "",
        "playerName": "",
        "price": 0,
        "priceProposal": null,
        "proposalConfirmedTime": null,
        "proposalState": null,
        "redeemCode": "7mqzEwJK",
        "redeemCreatedTime": 1744110550554,
        "redeemId": 188894,
        "redeemState": 0,
        "redeemSubjectId": null,
        "redeemSubjectName": "",
        "relationEnabled": null,
        "relationId": null,
        "relationType": 3
      }
    ]
  },
  "msg": "成功!"
}


==================================================
=== 请求信息 ===
时间: 2025-04-08 19:10:41
方法: GET
URL: https://www.gstudios.com.cn/story_v2/api/content/book/relation/freelancer/by/book?bookId=33524&freelancerName=&sortItem=&sortAsc=
备注标题: 获取已邀请合作成员列表
备注内容: 
Headers: {
  "host": "www.gstudios.com.cn",
  "connection": "keep-alive",
  "sec-ch-ua-platform": "Windows",
  "x-requested-with": "XMLHttpRequest",
  "user-agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********",
  "accept": "application/json, text/plain, */*",
  "sec-ch-ua": "\"Microsoft Edge\";v=\"135\", \"Not-A.Brand\";v=\"8\", \"Chromium\";v=\"135\"",
  "sec-ch-ua-mobile": "?0",
  "sec-fetch-site": "same-origin",
  "sec-fetch-mode": "cors",
  "sec-fetch-dest": "empty",
  "accept-encoding": "gzip, deflate, br, zstd",
  "accept-language": "zh-CN,zh;q=0.9,en;q=0.8,en-GB;q=0.7,en-US;q=0.6",
  "authorization": "Bearer d22e0aabbc6441dea7c113d07d0a4ed9",
  "referer": "https://www.gstudios.com.cn/"
}
Cookie: collabOperation=false; isVipClubMember=true; playerId=1214; wxToken=46385c92df3a44ef9cf3c784d8bdba2a; user_type=0; user_role=3; user_name=A%E9%A3%9E%E6%89%AC; access_token=d22e0aabbc6441dea7c113d07d0a4ed9; group_name=%E4%BA%9A%E8%A7%86%E6%9C%89%E5%A3%B0; cid=null; gcid=67516
=== 响应信息 ===
状态码: 200
响应头: {'server': 'CLOUD ELB 1.0.0', 'date': 'Tue, 08 Apr 2025 11:10:38 GMT', 'content-type': 'application/json;charset=UTF-8', 'transfer-encoding': 'chunked', 'connection': 'close', 'x-powered-by': 'Express', 'vary': 'Accept-Encoding', 'content-encoding': 'gzip'}

=== 响应内容 ===
{
  "code": 1,
  "data": {
    "list": [
      {
        "monthly": false,
        "playerAvatar": "",
        "playerDesc": "",
        "playerName": "罗兰之猗猗",
        "price": 500,
        "priceProposal": null,
        "proposalConfirmedTime": null,
        "proposalState": 0,
        "redeemCode": "AQKJOJkB",
        "redeemCreatedTime": 1743582984337,
        "redeemId": 186351,
        "redeemState": 1,
        "redeemSubjectId": 30317,
        "redeemSubjectName": "罗兰之猗猗",
        "relationEnabled": 1,
        "relationId": 206157,
        "relationType": 0
      },
      {
        "monthly": false,
        "playerAvatar": "",
        "playerDesc": "",
        "playerName": "",
        "price": 30000,
        "priceProposal": null,
        "proposalConfirmedTime": null,
        "proposalState": null,
        "redeemCode": "HbHd4x83",
        "redeemCreatedTime": 1744105185477,
        "redeemId": 188854,
        "redeemState": 0,
        "redeemSubjectId": 157,
        "redeemSubjectName": "金阅微",
        "relationEnabled": null,
        "relationId": null,
        "relationType": 3
      },
      {
        "monthly": false,
        "playerAvatar": "",
        "playerDesc": "",
        "playerName": "",
        "price": 25000,
        "priceProposal": null,
        "proposalConfirmedTime": null,
        "proposalState": null,
        "redeemCode": "Q79d5YLo",
        "redeemCreatedTime": 1744105185338,
        "redeemId": 188853,
        "redeemState": 0,
        "redeemSubjectId": 88,
        "redeemSubjectName": "曹先森1",
        "relationEnabled": null,
        "relationId": null,
        "relationType": 3
      },
      {
        "monthly": false,
        "playerAvatar": "",
        "playerDesc": "",
        "playerName": "",
        "price": 20000,
        "priceProposal": null,
        "proposalConfirmedTime": null,
        "proposalState": null,
        "redeemCode": "AwK8x6Iq",
        "redeemCreatedTime": 1744105456073,
        "redeemId": 188864,
        "redeemState": 0,
        "redeemSubjectId": 148,
        "redeemSubjectName": "银耳",
        "relationEnabled": null,
        "relationId": null,
        "relationType": 3
      },
      {
        "monthly": false,
        "playerAvatar": "",
        "playerDesc": "",
        "playerName": "",
        "price": 15000,
        "priceProposal": null,
        "proposalConfirmedTime": null,
        "proposalState": null,
        "redeemCode": "T3o911TL",
        "redeemCreatedTime": 1744105185622,
        "redeemId": 188855,
        "redeemState": 0,
        "redeemSubjectId": 139,
        "redeemSubjectName": "会跳舞的铁匠",
        "relationEnabled": null,
        "relationId": null,
        "relationType": 3
      },
      {
        "monthly": false,
        "playerAvatar": "",
        "playerDesc": "",
        "playerName": "",
        "price": 0,
        "priceProposal": null,
        "proposalConfirmedTime": null,
        "proposalState": null,
        "redeemCode": "SGO8E2hf",
        "redeemCreatedTime": 1744110290114,
        "redeemId": 188893,
        "redeemState": 0,
        "redeemSubjectId": null,
        "redeemSubjectName": "",
        "relationEnabled": null,
        "relationId": null,
        "relationType": 3
      },
      {
        "monthly": false,
        "playerAvatar": "",
        "playerDesc": "",
        "playerName": "",
        "price": 0,
        "priceProposal": null,
        "proposalConfirmedTime": null,
        "proposalState": null,
        "redeemCode": "7mqzEwJK",
        "redeemCreatedTime": 1744110550554,
        "redeemId": 188894,
        "redeemState": 0,
        "redeemSubjectId": null,
        "redeemSubjectName": "",
        "relationEnabled": null,
        "relationId": null,
        "relationType": 3
      }
    ]
  },
  "msg": "成功!"
}


==================================================
=== 请求信息 ===
时间: 2025-04-08 19:17:52
方法: POST
URL: https://www.gstudios.com.cn/story_v2/api/content/book/relation/freelancer/invite/cancel
备注标题: 删除合作成员
备注内容: 
Headers: {
  "host": "www.gstudios.com.cn",
  "connection": "keep-alive",
  "sec-ch-ua-platform": "Windows",
  "x-requested-with": "XMLHttpRequest",
  "user-agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********",
  "accept": "application/json, text/plain, */*",
  "sec-ch-ua": "\"Microsoft Edge\";v=\"135\", \"Not-A.Brand\";v=\"8\", \"Chromium\";v=\"135\"",
  "sec-ch-ua-mobile": "?0",
  "sec-fetch-site": "same-origin",
  "sec-fetch-mode": "cors",
  "sec-fetch-dest": "empty",
  "accept-encoding": "gzip, deflate, br, zstd",
  "accept-language": "zh-CN,zh;q=0.9,en;q=0.8,en-GB;q=0.7,en-US;q=0.6",
  "authorization": "Bearer d22e0aabbc6441dea7c113d07d0a4ed9",
  "referer": "https://www.gstudios.com.cn/"
}
Cookie: collabOperation=false; isVipClubMember=true; playerId=1214; wxToken=46385c92df3a44ef9cf3c784d8bdba2a; user_type=0; user_role=3; user_name=A%E9%A3%9E%E6%89%AC; access_token=d22e0aabbc6441dea7c113d07d0a4ed9; group_name=%E4%BA%9A%E8%A7%86%E6%9C%89%E5%A3%B0; cid=null; gcid=67516
请求数据: {
  "redeemId": 188894
}
=== 响应信息 ===
状态码: 200
响应头: {'server': 'CLOUD ELB 1.0.0', 'date': 'Tue, 08 Apr 2025 11:17:49 GMT', 'content-type': 'application/json;charset=UTF-8', 'transfer-encoding': 'chunked', 'connection': 'close', 'x-powered-by': 'Express', 'vary': 'Accept-Encoding', 'content-encoding': 'gzip'}

=== 响应内容 ===
{
  "code": 1,
  "data": null,
  "msg": "成功!"
}

