#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
书籍列表编辑器端点测试脚本

测试新的书籍列表编辑器API端点功能。

作者：Augment Agent
版本：1.0.0
"""

import sys
import os
import json
import logging

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from api.client import GStudioAPIClient
from api.models import APIVersion, BookListEditorParams


def setup_logging():
    """设置日志"""
    logging.basicConfig(
        level=logging.DEBUG,
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
        handlers=[
            logging.StreamHandler(sys.stdout),
            logging.FileHandler('book_list_editor_test.log', encoding='utf-8')
        ]
    )


def test_book_list_editor_endpoint():
    """测试书籍列表编辑器端点"""
    print("=" * 60)
    print("测试书籍列表编辑器端点")
    print("=" * 60)
    
    # 创建调试模式的API客户端
    client = GStudioAPIClient(
        debug_mode=True,
        timeout=30,
        max_retries=3
    )
    
    # 设置测试token（如果有的话）
    test_token = input("请输入API Token（可选，直接回车跳过）: ").strip()
    if test_token:
        client.set_token(test_token)
    
    print("\n1. 测试基本的书籍列表获取...")
    try:
        # 测试基本参数
        params = BookListEditorParams(
            page_size=10,
            page_no=1,
            sort_item="bookName",
            sort_asc=True
        )
        
        response = client.get_book_list_editor(params)
        
        print(f"✓ API调用成功")
        print(f"  响应码: {response.code}")
        print(f"  响应消息: {response.msg}")
        
        if response.data:
            if isinstance(response.data, dict):
                book_list = response.data.get('list', [])
                total = response.data.get('total', 0)
                print(f"  获取到 {len(book_list)} 本书籍，总计 {total} 本")
                
                if book_list:
                    print(f"  第一本书籍信息:")
                    first_book = book_list[0]
                    for key, value in first_book.items():
                        print(f"    {key}: {value}")
            else:
                print(f"  响应数据类型: {type(response.data)}")
                print(f"  响应数据: {response.data}")
        else:
            print("  无响应数据")
            
    except Exception as e:
        print(f"✗ API调用失败: {e}")
    
    print("\n2. 测试带搜索关键词的书籍列表...")
    try:
        search_query = input("请输入搜索关键词（可选，直接回车跳过）: ").strip()
        
        params = BookListEditorParams(
            page_size=10,
            page_no=1,
            name=search_query if search_query else None,
            sort_item="bookName",
            sort_asc=True
        )
        
        response = client.get_book_list_editor(params)
        
        print(f"✓ 搜索API调用成功")
        print(f"  搜索关键词: '{search_query}'")
        print(f"  响应码: {response.code}")
        
        if response.data and isinstance(response.data, dict):
            book_list = response.data.get('list', [])
            total = response.data.get('total', 0)
            print(f"  搜索到 {len(book_list)} 本书籍，总计 {total} 本")
            
            for i, book in enumerate(book_list[:3], 1):  # 只显示前3本
                book_name = book.get('name', '未知')
                book_id = book.get('id', '未知')
                print(f"    {i}. {book_name} (ID: {book_id})")
        
    except Exception as e:
        print(f"✗ 搜索API调用失败: {e}")
    
    print("\n3. 测试便捷搜索方法...")
    try:
        search_query = "测试" if not test_token else input("请输入搜索关键词进行测试: ").strip()
        
        books = client.search_books(query=search_query, page_size=5)
        
        print(f"✓ 便捷搜索方法调用成功")
        print(f"  搜索关键词: '{search_query}'")
        print(f"  获取到 {len(books)} 本书籍")
        
        for i, book in enumerate(books, 1):
            book_name = book.get('name', book.get('bookName', '未知'))
            book_id = book.get('id', book.get('bookId', '未知'))
            description = book.get('description', '无描述')
            print(f"    {i}. {book_name} (ID: {book_id})")
            if description:
                print(f"       描述: {description[:50]}...")
        
    except Exception as e:
        print(f"✗ 便捷搜索方法调用失败: {e}")


def test_api_endpoint_comparison():
    """比较新旧API端点"""
    print("\n" + "=" * 60)
    print("比较新旧API端点")
    print("=" * 60)
    
    client = GStudioAPIClient(debug_mode=True)
    
    print("\n1. 测试旧端点 (/content/book/list)...")
    try:
        response = client.get_book_list()
        print(f"✓ 旧端点调用成功")
        print(f"  响应码: {response.code}")
        print(f"  响应消息: {response.msg}")
        
        if response.data:
            if isinstance(response.data, dict):
                book_list = response.data.get('list', [])
                print(f"  获取到 {len(book_list)} 本书籍")
            elif isinstance(response.data, list):
                print(f"  获取到 {len(response.data)} 本书籍")
        
    except Exception as e:
        print(f"✗ 旧端点调用失败: {e}")
    
    print("\n2. 测试新端点 (/content/book/list/editor)...")
    try:
        params = BookListEditorParams(
            page_size=10,
            page_no=1
        )
        
        response = client.get_book_list_editor(params)
        print(f"✓ 新端点调用成功")
        print(f"  响应码: {response.code}")
        print(f"  响应消息: {response.msg}")
        
        if response.data and isinstance(response.data, dict):
            book_list = response.data.get('list', [])
            total = response.data.get('total', 0)
            print(f"  获取到 {len(book_list)} 本书籍，总计 {total} 本")
        
    except Exception as e:
        print(f"✗ 新端点调用失败: {e}")


def main():
    """主测试函数"""
    print("GStudio 书籍列表编辑器端点测试")
    print("=" * 60)
    print("此测试将验证:")
    print("• 新的书籍列表编辑器API端点")
    print("• 搜索功能和参数处理")
    print("• 响应数据结构和字段")
    print("• 新旧端点的差异")
    print()
    
    # 设置日志
    setup_logging()
    
    try:
        # 运行测试
        test_book_list_editor_endpoint()
        test_api_endpoint_comparison()
        
        print("\n" + "=" * 60)
        print("测试完成！")
        print("=" * 60)
        print("请查看:")
        print("1. 控制台输出的测试结果")
        print("2. book_list_editor_test.log 文件中的详细日志")
        print("3. 新旧端点的响应差异")
        
    except Exception as e:
        print(f"测试过程中发生异常: {e}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    main()
    input("\n按回车键退出...")
