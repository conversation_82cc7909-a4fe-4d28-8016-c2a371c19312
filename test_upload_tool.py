#!/usr/bin/env python3
"""
测试音频上传工具的基本功能
验证参数处理、文件验证等功能（不进行实际上传）
"""

import sys
import os
import tempfile
from pathlib import Path

# 添加simple-gstudio-api目录到Python路径
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'simple-gstudio-api'))

# 导入上传工具的函数
try:
    from upload_audio_to_gstudio import validate_audio_file, API_TOKEN, CUE_ID
    from gstudio_api import APIEndpoints, VoiceUploadParams
except ImportError as e:
    print(f"❌ 导入失败: {e}")
    sys.exit(1)

def test_api_configuration():
    """测试API配置"""
    print("测试API配置...")
    
    # 验证API令牌
    assert API_TOKEN == "3dfb89119562456cb8818120139f6ae1"
    print(f"✓ API令牌正确: {API_TOKEN[:10]}...")
    
    # 验证CUE_ID
    assert CUE_ID == 691699346
    print(f"✓ CUE_ID正确: {CUE_ID}")
    
    # 验证API端点
    upload_url = APIEndpoints.get_url(APIEndpoints.Material.VOICE_UPLOAD)
    expected_url = "https://www.gstudios.com.cn/story_v2/api/material/voice/upload"
    assert upload_url == expected_url
    print(f"✓ API端点正确: {upload_url}")

def test_voice_upload_params():
    """测试VoiceUploadParams创建"""
    print("\n测试VoiceUploadParams创建...")
    
    params = VoiceUploadParams(
        file="test.mp3",
        cueId=CUE_ID,
        filename="test.mp3"
    )
    
    assert params.file == "test.mp3"
    assert params.cueId == CUE_ID
    assert params.filename == "test.mp3"
    print(f"✓ VoiceUploadParams创建成功")

def test_file_validation():
    """测试文件验证功能"""
    print("\n测试文件验证功能...")
    
    # 创建临时MP3文件用于测试
    with tempfile.NamedTemporaryFile(suffix='.mp3', delete=False) as temp_file:
        temp_file.write(b"fake mp3 content for testing")
        temp_mp3_path = temp_file.name
    
    try:
        # 测试有效文件
        validated_path = validate_audio_file(temp_mp3_path)
        assert validated_path.exists()
        print(f"✓ 有效文件验证通过: {validated_path.name}")
        
        # 测试文件不存在
        try:
            validate_audio_file("nonexistent.mp3")
            assert False, "应该抛出FileNotFoundError"
        except FileNotFoundError:
            print("✓ 文件不存在检测正常")
        
        # 测试不支持的格式
        with tempfile.NamedTemporaryFile(suffix='.txt', delete=False) as temp_txt:
            temp_txt.write(b"text file")
            temp_txt_path = temp_txt.name
        
        try:
            validate_audio_file(temp_txt_path)
            assert False, "应该抛出ValueError"
        except ValueError as e:
            print(f"✓ 文件格式检测正常: {e}")
        finally:
            os.unlink(temp_txt_path)
            
    finally:
        # 清理临时文件
        os.unlink(temp_mp3_path)

def test_multipart_structure():
    """测试multipart/form-data结构理解"""
    print("\n测试multipart/form-data结构...")
    
    params = VoiceUploadParams(
        file="test.mp3",
        cueId=CUE_ID,
        filename="test.mp3"
    )
    
    # 模拟requests的files和data结构
    files_dict = {
        'file': (params.filename, b'fake_audio_data', 'audio/mpeg')
    }
    data_dict = {
        'cueId': params.cueId
    }
    
    assert 'file' in files_dict
    assert 'cueId' in data_dict
    assert data_dict['cueId'] == CUE_ID
    assert files_dict['file'][0] == params.filename
    assert files_dict['file'][2] == 'audio/mpeg'
    
    print("✓ multipart/form-data结构正确")

def test_error_handling():
    """测试错误处理"""
    print("\n测试错误处理...")
    
    # 测试目录而非文件
    try:
        validate_audio_file(".")
        assert False, "应该抛出ValueError"
    except ValueError as e:
        print(f"✓ 目录检测正常: {e}")
    
    # 测试空路径
    try:
        validate_audio_file("")
        assert False, "应该抛出异常"
    except (FileNotFoundError, ValueError):
        print("✓ 空路径检测正常")

def create_sample_mp3():
    """创建一个示例MP3文件用于测试"""
    print("\n创建示例MP3文件...")
    
    sample_file = "sample_audio.mp3"
    
    # 创建一个假的MP3文件（仅用于测试文件处理逻辑）
    with open(sample_file, 'wb') as f:
        # 写入MP3文件头（简化版本）
        f.write(b'\xff\xfb\x90\x00')  # MP3 header
        f.write(b'Sample audio content for testing' * 100)  # 填充一些数据
    
    print(f"✓ 示例文件已创建: {sample_file}")
    print(f"   文件大小: {os.path.getsize(sample_file)} 字节")
    print(f"   可以使用以下命令测试上传工具:")
    print(f"   python upload_audio_to_gstudio.py {sample_file}")
    
    return sample_file

def main():
    """主测试函数"""
    print("🧪 测试音频上传工具")
    print("=" * 50)
    
    try:
        test_api_configuration()
        test_voice_upload_params()
        test_file_validation()
        test_multipart_structure()
        test_error_handling()
        
        print("\n" + "=" * 50)
        print("✅ 所有测试通过！")
        
        # 创建示例文件
        sample_file = create_sample_mp3()
        
        print(f"\n📝 使用说明:")
        print(f"1. 上传工具已准备就绪")
        print(f"2. 使用真实的API令牌: {API_TOKEN[:10]}...")
        print(f"3. 使用真实的CUE_ID: {CUE_ID}")
        print(f"4. 支持MP3格式音频文件")
        print(f"5. 示例命令: python upload_audio_to_gstudio.py {sample_file}")
        
        print(f"\n⚠️  注意事项:")
        print(f"- 确保音频文件是有效的MP3格式")
        print(f"- 文件大小建议不超过50MB")
        print(f"- 需要稳定的网络连接")
        print(f"- 上传后会自动进行语音识别和音频分析")
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        sys.exit(1)

if __name__ == "__main__":
    main()
