#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
GUI书籍搜索功能测试脚本

测试GUI中的书籍搜索功能是否能正确处理新的API端点。

作者：Augment Agent
版本：1.0.0
"""

import sys
import os
import json
import logging
import tkinter as tk
from unittest.mock import Mock, patch

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from gui.book_search import BookSearchFrame
from gui.main_window import MainWindow
from api.client import GStudioAPIClient
from api.models import APIResponse


def setup_logging():
    """设置日志"""
    logging.basicConfig(
        level=logging.DEBUG,
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
        handlers=[
            logging.StreamHandler(sys.stdout),
            logging.FileHandler('gui_book_search_test.log', encoding='utf-8')
        ]
    )


def create_mock_book_data():
    """创建模拟的书籍数据"""
    # 新API格式的书籍数据
    new_format_books = [
        {
            "id": 33964,
            "name": "战国明月",
            "description": "一部精彩的历史小说",
            "finished": False,
            "remark": "小南瓜画本",
            "auditionRateLimit": 3,
            "auditionShowCv": False,
            "communityVisible": True,
            "createdTime": 1743654555189
        },
        {
            "id": 33965,
            "name": "测试书籍",
            "description": "这是一本测试用的书籍",
            "finished": True,
            "remark": "测试备注",
            "auditionRateLimit": 5,
            "auditionShowCv": True,
            "communityVisible": False,
            "createdTime": 1743654555190
        }
    ]
    
    # 旧API格式的书籍数据
    old_format_books = [
        {
            "bookId": "12345",
            "bookName": "经典小说",
            "description": "一部经典的文学作品",
            "price": 2999,  # 以分为单位
            "author": "著名作家"
        },
        {
            "bookId": "12346",
            "bookName": "现代小说",
            "description": "现代文学的代表作",
            "price": 0,  # 免费
            "author": "现代作家"
        }
    ]
    
    return new_format_books, old_format_books


def test_book_search_with_mock_data():
    """使用模拟数据测试书籍搜索功能"""
    print("=" * 60)
    print("测试GUI书籍搜索功能（模拟数据）")
    print("=" * 60)
    
    new_books, old_books = create_mock_book_data()
    
    # 创建主窗口
    root = tk.Tk()
    root.withdraw()  # 隐藏主窗口
    
    try:
        # 创建主窗口实例
        main_window = MainWindow(root)
        
        # 创建书籍搜索框架
        book_search_frame = BookSearchFrame(root, main_window)
        
        print("\n1. 测试新API格式数据处理...")
        
        # 模拟API客户端
        mock_client = Mock(spec=GStudioAPIClient)
        mock_client.debug_mode = True
        mock_client.search_books.return_value = new_books
        
        # 设置模拟的API客户端
        book_search_frame.api_client = mock_client
        
        # 测试更新搜索结果
        book_search_frame._update_search_results(new_books)
        
        # 检查结果
        items = book_search_frame.results_tree.get_children()
        print(f"  ✓ 显示了 {len(items)} 本书籍")
        
        for i, item in enumerate(items):
            values = book_search_frame.results_tree.item(item, 'values')
            print(f"    {i+1}. ID: {values[0]}, 名称: {values[1]}, 价格: {values[2]}")
            print(f"       描述: {values[3]}")
        
        print("\n2. 测试旧API格式数据处理...")
        
        # 测试旧格式数据
        book_search_frame._update_search_results(old_books)
        
        # 检查结果
        items = book_search_frame.results_tree.get_children()
        print(f"  ✓ 显示了 {len(items)} 本书籍")
        
        for i, item in enumerate(items):
            values = book_search_frame.results_tree.item(item, 'values')
            print(f"    {i+1}. ID: {values[0]}, 名称: {values[1]}, 价格: {values[2]}")
            print(f"       描述: {values[3]}")
        
        print("\n3. 测试混合格式数据处理...")
        
        # 测试混合格式
        mixed_books = new_books + old_books
        book_search_frame._update_search_results(mixed_books)
        
        # 检查结果
        items = book_search_frame.results_tree.get_children()
        print(f"  ✓ 显示了 {len(items)} 本书籍")
        
        print("\n4. 测试搜索功能...")
        
        # 模拟搜索
        book_search_frame.search_var.set("测试")
        
        # 模拟搜索结果
        search_results = [book for book in mixed_books 
                         if "测试" in book.get('name', book.get('bookName', ''))]
        
        book_search_frame._update_search_results(search_results)
        
        items = book_search_frame.results_tree.get_children()
        print(f"  ✓ 搜索到 {len(items)} 本相关书籍")
        
        print("\n✓ GUI书籍搜索功能测试完成")
        
    except Exception as e:
        print(f"✗ 测试失败: {e}")
        import traceback
        traceback.print_exc()
    
    finally:
        root.destroy()


def test_api_integration():
    """测试API集成"""
    print("\n" + "=" * 60)
    print("测试API集成")
    print("=" * 60)
    
    try:
        # 创建API客户端
        client = GStudioAPIClient(debug_mode=True)
        
        print("\n1. 测试新端点URL生成...")
        from api.models import APIEndpoints
        
        new_url = APIEndpoints.get_url(APIEndpoints.Book.LIST_EDITOR)
        old_url = APIEndpoints.get_url(APIEndpoints.Book.LIST)
        
        print(f"  新端点URL: {new_url}")
        print(f"  旧端点URL: {old_url}")
        print("  ✓ URL生成正确")
        
        print("\n2. 测试搜索方法调用...")
        
        # 测试搜索方法（会返回空列表，因为没有认证）
        books = client.search_books("测试", page_size=5)
        print(f"  ✓ 搜索方法调用成功，返回 {len(books)} 本书籍")
        
        print("\n3. 测试参数构建...")
        
        from api.models import BookListEditorParams
        
        params = BookListEditorParams(
            page_size=10,
            page_no=1,
            name="测试书籍",
            sort_item="bookName",
            sort_asc=True
        )
        
        print(f"  ✓ 参数构建成功: page_size={params.page_size}, name={params.name}")
        
    except Exception as e:
        print(f"✗ API集成测试失败: {e}")
        import traceback
        traceback.print_exc()


def test_field_compatibility():
    """测试字段兼容性"""
    print("\n" + "=" * 60)
    print("测试字段兼容性")
    print("=" * 60)
    
    # 测试数据
    test_cases = [
        # 新格式
        {"id": 1, "name": "新格式书籍", "description": "新格式描述"},
        # 旧格式
        {"bookId": "2", "bookName": "旧格式书籍", "description": "旧格式描述", "price": 2999},
        # 混合格式
        {"id": 3, "bookName": "混合格式", "remark": "备注信息"},
        # 缺失字段
        {"id": 4},
        # 空字段
        {"id": 5, "name": "", "description": None}
    ]
    
    print("\n测试字段提取逻辑:")
    
    for i, book in enumerate(test_cases, 1):
        print(f"\n{i}. 测试数据: {book}")
        
        # 模拟字段提取逻辑
        book_id = book.get('id', book.get('bookId', ''))
        book_name = book.get('name', book.get('bookName', ''))
        description = book.get('description', book.get('remark', ''))
        price = book.get('price', 0)
        
        print(f"   提取结果:")
        print(f"     ID: '{book_id}'")
        print(f"     名称: '{book_name}'")
        print(f"     描述: '{description}'")
        print(f"     价格: {price}")
        
        # 验证提取结果
        if book_id and book_name:
            print(f"   ✓ 字段提取成功")
        else:
            print(f"   ⚠ 字段提取不完整")


def main():
    """主测试函数"""
    print("GStudio GUI书籍搜索功能测试")
    print("=" * 60)
    print("此测试将验证:")
    print("• GUI中的书籍搜索功能")
    print("• 新旧API格式的兼容性")
    print("• 字段映射和数据处理")
    print("• API集成功能")
    print()
    
    # 设置日志
    setup_logging()
    
    try:
        # 运行测试
        test_field_compatibility()
        test_api_integration()
        test_book_search_with_mock_data()
        
        print("\n" + "=" * 60)
        print("测试完成！")
        print("=" * 60)
        print("请查看:")
        print("1. 控制台输出的测试结果")
        print("2. gui_book_search_test.log 文件中的详细日志")
        print("3. 字段兼容性测试结果")
        
    except Exception as e:
        print(f"测试过程中发生异常: {e}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    main()
