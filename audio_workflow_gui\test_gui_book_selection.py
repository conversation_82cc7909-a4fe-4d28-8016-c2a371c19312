#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
GUI书籍选择功能实际测试

使用真实的API数据测试修复后的书籍选择功能。

作者：Augment Agent
版本：1.0.0
"""

import sys
import os
import tkinter as tk
import logging

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from api.client import GStudioAPIClient
from gui.main_window import MainWindow
from config.settings import AppConfig


def setup_logging():
    """设置日志"""
    logging.basicConfig(
        level=logging.DEBUG,
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
        handlers=[
            logging.StreamHandler(sys.stdout),
            logging.FileHandler('gui_book_selection_test.log', encoding='utf-8')
        ]
    )


def test_with_real_api():
    """使用真实API测试"""
    print("=" * 60)
    print("使用真实API测试书籍选择功能")
    print("=" * 60)
    
    # 设置Token
    token = "3dfb89119562456cb8818120139f6ae1"
    
    # 创建API客户端
    client = GStudioAPIClient(
        debug_mode=True,
        request_interval=0.2  # 使用推荐的生产环境值
    )
    client.set_token(token)
    
    print("1. 获取真实书籍数据...")
    try:
        books = client.search_books("", page_size=5)
        print(f"   ✓ 获取到 {len(books)} 本书籍")
        
        if books:
            # 显示前3本书的信息
            from utils.field_utils import get_book_field, normalize_book_data, get_book_display_values
            
            print("\n2. 书籍数据分析:")
            for i, book in enumerate(books[:3], 1):
                print(f"\n   书籍 {i}:")
                print(f"     原始字段: {list(book.keys())}")
                
                # 标准化数据
                normalized = normalize_book_data(book)
                print(f"     标准化后字段: {list(normalized.keys())}")
                
                # 显示值
                values = get_book_display_values(normalized)
                print(f"     显示值: ID={values[0]}, 名称={values[1]}, 价格={values[2]}")
                print(f"     描述: {values[3]}")
        
        return books
        
    except Exception as e:
        print(f"   ✗ 获取书籍数据失败: {e}")
        return []


def test_gui_integration():
    """测试GUI集成"""
    print("\n" + "=" * 60)
    print("测试GUI集成")
    print("=" * 60)
    
    try:
        # 创建配置
        config = AppConfig()
        
        # 创建主窗口
        root = tk.Tk()
        root.title("GStudio 书籍选择功能测试")
        root.geometry("800x600")
        
        # 创建主窗口实例
        main_window = MainWindow(root, config)
        
        print("1. GUI界面创建成功")
        
        # 设置API Token
        token = "3dfb89119562456cb8818120139f6ae1"
        config.set('api.token', token)
        print("2. API Token设置成功")
        
        # 获取书籍搜索框架
        book_search_frame = main_window.book_search_frame
        
        if book_search_frame:
            print("3. 找到书籍搜索框架")

            # 触发API客户端创建
            book_search_frame._check_api_client()

            # 检查API客户端
            if hasattr(book_search_frame, 'api_client') and book_search_frame.api_client:
                print("4. 书籍搜索框架API客户端已设置")
                
                # 测试搜索功能
                print("5. 开始测试搜索功能...")
                
                def test_search():
                    try:
                        # 模拟搜索
                        books = book_search_frame.api_client.search_books("", page_size=3)
                        print(f"   ✓ 搜索成功，获取到 {len(books)} 本书籍")
                        
                        if books:
                            # 更新搜索结果
                            book_search_frame._update_search_results(books)
                            print(f"   ✓ 搜索结果已更新到界面")
                            
                            # 检查树形视图
                            items = book_search_frame.results_tree.get_children()
                            print(f"   ✓ 树形视图显示 {len(items)} 行数据")
                            
                            if items:
                                # 模拟选择第一本书
                                first_item = items[0]
                                book_search_frame.results_tree.selection_set(first_item)
                                book_search_frame.results_tree.focus(first_item)

                                # 创建模拟事件并触发选择事件
                                import tkinter as tk
                                mock_event = type('MockEvent', (), {'widget': book_search_frame.results_tree})()
                                book_search_frame._on_tree_select(mock_event)
                                print(f"   ✓ 模拟选择第一本书")
                                
                                # 检查选择按钮状态
                                button_state = book_search_frame.select_button['state']
                                selection = book_search_frame.results_tree.selection()
                                print(f"   调试信息: 按钮状态={button_state}, 选中项={selection}")

                                if button_state == 'normal':
                                    print(f"   ✓ 选择按钮已启用")

                                    # 模拟点击选择按钮
                                    book_search_frame._select_book()
                                    print(f"   ✓ 模拟点击选择按钮")

                                    # 检查工作流状态
                                    workflow_state = main_window.get_workflow_state()
                                    selected_book = workflow_state.get('selected_book')

                                    if selected_book:
                                        from utils.field_utils import get_book_field
                                        book_name = get_book_field(selected_book, 'name')
                                        book_id = get_book_field(selected_book, 'id')
                                        print(f"   ✅ 书籍选择成功！")
                                        print(f"       选中书籍: {book_name} (ID: {book_id})")

                                        # 检查章节管理界面
                                        chapter_frame = main_window.chapter_manager_frame

                                        if chapter_frame:
                                            print(f"   ✓ 找到章节管理框架")

                                            # 更新章节管理界面
                                            chapter_frame._update_book_info()

                                            # 检查加载按钮状态
                                            if chapter_frame.load_chapters_button['state'] == 'normal':
                                                print(f"   ✅ 章节加载按钮已启用！")
                                                print(f"   🎉 书籍选择功能完全正常！")
                                            else:
                                                print(f"   ❌ 章节加载按钮未启用")
                                        else:
                                            print(f"   ⚠ 未找到章节管理框架")
                                    else:
                                        print(f"   ❌ 工作流状态中未找到选中的书籍")
                                elif button_state in ['normal', 'active']:
                                    print(f"   ✓ 选择按钮已启用（状态: {button_state}）")

                                    # 继续执行选择操作...
                                    book_search_frame._select_book()
                                    print(f"   ✓ 模拟点击选择按钮")

                                    # 检查工作流状态
                                    workflow_state = main_window.get_workflow_state()
                                    selected_book = workflow_state.get('selected_book')

                                    if selected_book:
                                        from utils.field_utils import get_book_field
                                        book_name = get_book_field(selected_book, 'name')
                                        book_id = get_book_field(selected_book, 'id')
                                        print(f"   ✅ 书籍选择成功！")
                                        print(f"       选中书籍: {book_name} (ID: {book_id})")

                                        # 检查章节管理界面
                                        chapter_frame = main_window.chapter_manager_frame

                                        if chapter_frame:
                                            print(f"   ✓ 找到章节管理框架")

                                            # 更新章节管理界面
                                            chapter_frame._update_book_info()

                                            # 检查加载按钮状态
                                            chapter_button_state = chapter_frame.load_chapters_button['state']
                                            print(f"   章节加载按钮状态: {chapter_button_state}")

                                            if chapter_button_state in ['normal', 'active']:
                                                print(f"   ✅ 章节加载按钮已启用！")
                                                print(f"   🎉 书籍选择功能完全正常！")
                                            else:
                                                print(f"   ❌ 章节加载按钮未启用")
                                        else:
                                            print(f"   ⚠ 未找到章节管理框架")
                                    else:
                                        print(f"   ❌ 工作流状态中未找到选中的书籍")
                                else:
                                    print(f"   ❌ 选择按钮未启用，状态: {button_state}")
                            else:
                                print(f"   ⚠ 树形视图无数据")
                        else:
                            print(f"   ⚠ 搜索结果为空")
                            
                    except Exception as e:
                        print(f"   ❌ 搜索测试失败: {e}")
                        import traceback
                        traceback.print_exc()
                
                # 延迟执行测试，确保界面完全加载
                root.after(1000, test_search)
                
                # 显示窗口并运行一小段时间
                print("6. 显示测试窗口...")
                root.after(5000, root.quit)  # 5秒后自动关闭
                root.mainloop()
                
            else:
                print("4. ❌ 书籍搜索框架API客户端未设置")
        else:
            print("3. ❌ 未找到书籍搜索框架")
        
        root.destroy()
        
    except Exception as e:
        print(f"GUI集成测试失败: {e}")
        import traceback
        traceback.print_exc()


def main():
    """主测试函数"""
    print("GStudio GUI书籍选择功能实际测试")
    print("=" * 60)
    print("使用真实API数据测试修复后的功能")
    print()
    
    # 设置日志
    setup_logging()
    
    try:
        # 测试真实API
        books = test_with_real_api()
        
        if books:
            # 测试GUI集成
            test_gui_integration()
        else:
            print("⚠ 无法获取真实书籍数据，跳过GUI测试")
        
        print("\n" + "=" * 60)
        print("测试完成")
        print("=" * 60)
        
    except Exception as e:
        print(f"测试过程中发生异常: {e}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    main()
