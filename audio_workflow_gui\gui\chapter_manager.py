#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
章节管理模块

实现章节列表获取、显示和选择功能，支持按标题搜索和序号定位。

作者：Augment Agent
版本：1.0.0
"""

import tkinter as tk
from tkinter import ttk, messagebox
import threading
from datetime import datetime
from typing import List, Dict, Any, Optional

from utils.logger import LoggerMixin
from utils.field_utils import get_book_field, format_book_display_info
from utils.table_styles import setup_striped_table, refresh_table_stripes


class ChapterManagerFrame(ttk.Frame, LoggerMixin):
    """章节管理框架"""
    
    def __init__(self, parent, main_window):
        """
        初始化章节管理框架
        
        Args:
            parent: 父窗口
            main_window: 主窗口实例
        """
        super().__init__(parent)
        self.main_window = main_window
        self.chapters_data = []
        
        self._create_widgets()
        self._create_context_menu()
        self._setup_layout()
        self._bind_events()

        self.logger.info("章节管理模块初始化完成")

    def _create_context_menu(self):
        """创建右键上下文菜单"""
        self.context_menu = tk.Menu(self, tearoff=0)
        self.context_menu.add_command(label="查看详情", command=self._view_chapter_detail)
    
    def _create_widgets(self):
        """创建界面组件"""
        # 移除书籍信息区域（信息现在显示在工具栏中）
        
        # 章节搜索区域
        search_frame = ttk.LabelFrame(self, text="章节搜索", padding=10)
        
        # 搜索输入框架
        search_input_frame = ttk.Frame(search_frame)

        # 搜索方式选择（移动到搜索内容标签前面）
        self.search_type_var = tk.StringVar(value="title")
        ttk.Radiobutton(
            search_input_frame,
            text="按标题搜索",
            variable=self.search_type_var,
            value="title"
        ).pack(side=tk.LEFT, padx=(0, 10))

        ttk.Radiobutton(
            search_input_frame,
            text="按序号定位",
            variable=self.search_type_var,
            value="seq_num"
        ).pack(side=tk.LEFT, padx=(0, 15))

        # 搜索内容标签和输入框
        ttk.Label(search_input_frame, text="搜索内容:").pack(side=tk.LEFT, padx=(0, 5))
        self.search_var = tk.StringVar()
        self.search_entry = ttk.Entry(search_input_frame, textvariable=self.search_var, width=30)
        self.search_entry.pack(side=tk.LEFT, padx=(0, 5))
        
        self.search_button = ttk.Button(search_input_frame, text="搜索", command=self._search_chapters)
        self.search_button.pack(side=tk.LEFT, padx=(5, 0))

        self.clear_search_button = ttk.Button(search_input_frame, text="清除", command=self._clear_search)
        self.clear_search_button.pack(side=tk.LEFT, padx=(5, 0))

        # 加载章节列表按钮（移动到清除按钮右侧）
        self.load_chapters_button = ttk.Button(
            search_input_frame,
            text="加载章节列表",
            command=self._load_chapters
        )
        self.load_chapters_button.pack(side=tk.LEFT, padx=(10, 0))
        
        # 章节列表区域
        chapters_frame = ttk.LabelFrame(self, text="章节列表", padding=10)
        
        # 创建Treeview显示章节列表（移除章节ID列）
        columns = ("seq_num", "chapter_name", "word_count", "edition_state", "recording_state", "audition_state", "edit_time")
        self.chapters_tree = ttk.Treeview(chapters_frame, columns=columns, show="headings", height=15)

        # 设置列标题
        self.chapters_tree.heading("seq_num", text="序号")
        self.chapters_tree.heading("chapter_name", text="章节名称")
        self.chapters_tree.heading("word_count", text="章节字数")
        self.chapters_tree.heading("edition_state", text="编辑状态")
        self.chapters_tree.heading("recording_state", text="录音状态")
        self.chapters_tree.heading("audition_state", text="审听状态")
        self.chapters_tree.heading("edit_time", text="编辑时间")

        # 设置列宽（重新调整以优化显示效果）
        self.chapters_tree.column("seq_num", width=70)  # 增加序号列宽
        self.chapters_tree.column("chapter_name", width=280)  # 增加章节名称列宽
        self.chapters_tree.column("word_count", width=100)  # 增加字数列宽
        self.chapters_tree.column("edition_state", width=100)  # 增加编辑状态列宽
        self.chapters_tree.column("recording_state", width=100)  # 增加录音状态列宽
        self.chapters_tree.column("audition_state", width=100)  # 增加审听状态列宽
        self.chapters_tree.column("edit_time", width=180)  # 增加编辑时间列宽
        
        # 添加滚动条
        self.scrollbar_y = ttk.Scrollbar(chapters_frame, orient=tk.VERTICAL, command=self.chapters_tree.yview)
        self.scrollbar_x = ttk.Scrollbar(chapters_frame, orient=tk.HORIZONTAL, command=self.chapters_tree.xview)
        self.chapters_tree.configure(yscrollcommand=self.scrollbar_y.set, xscrollcommand=self.scrollbar_x.set)

        # 应用条纹化样式美化表格
        setup_striped_table(self.chapters_tree, auto_update=True)
        
        # 移除操作按钮区域和选中章节信息显示区域（功能将通过右键菜单和工具栏提供）
        
        # 保存组件引用
        self.search_frame = search_frame
        self.search_input_frame = search_input_frame
        self.chapters_frame = chapters_frame
    
    def _setup_layout(self):
        """设置布局"""
        self.search_frame.pack(fill=tk.X, pady=(0, 10))
        self.search_input_frame.pack(fill=tk.X)
        
        self.chapters_frame.pack(fill=tk.BOTH, expand=True, pady=(0, 10))
        
        # 章节树形视图布局
        self.chapters_tree.grid(row=0, column=0, sticky="nsew")
        self.scrollbar_y.grid(row=0, column=1, sticky="ns")
        self.scrollbar_x.grid(row=1, column=0, sticky="ew")
        
        self.chapters_frame.grid_rowconfigure(0, weight=1)
        self.chapters_frame.grid_columnconfigure(0, weight=1)
    
    def _bind_events(self):
        """绑定事件"""
        # 搜索框回车事件
        self.search_entry.bind("<Return>", lambda e: self._search_chapters())
        
        # 树形视图选择事件
        self.chapters_tree.bind("<<TreeviewSelect>>", self._on_tree_select)
        
        # 双击选择章节
        self.chapters_tree.bind("<Double-1>", lambda e: self._select_chapter())

        # 右键菜单
        self.chapters_tree.bind("<Button-3>", self._show_context_menu)

        # 标签页激活时更新书籍信息
        self.bind("<Visibility>", self._on_visibility_changed)
    
    def _on_visibility_changed(self, event):
        """标签页可见性改变事件"""
        if event.widget == self:
            self._update_book_info()
    
    def _on_tree_select(self, event):
        """树形视图选择事件处理"""
        # 按钮已移除，此方法保留以备将来扩展
        pass

    def _show_context_menu(self, event):
        """显示右键上下文菜单"""
        # 获取点击位置的项目
        item = self.chapters_tree.identify_row(event.y)
        if item:
            # 选中该项目
            self.chapters_tree.selection_set(item)
            # 显示右键菜单
            try:
                self.context_menu.tk_popup(event.x_root, event.y_root)
            finally:
                self.context_menu.grab_release()
    
    def _update_book_info(self):
        """更新书籍信息显示"""
        workflow_state = self.main_window.get_workflow_state()
        selected_book = workflow_state.get('selected_book')

        if selected_book:
            # 启用加载按钮
            self.load_chapters_button.config(state=tk.NORMAL)

            book_name = get_book_field(selected_book, 'name')
            self.logger.debug(f"章节管理界面显示书籍: {book_name}")
        else:
            # 禁用加载按钮
            self.load_chapters_button.config(state=tk.DISABLED)

            self.logger.debug("章节管理界面：未选择书籍")
    
    def _load_chapters(self):
        """加载章节列表"""
        workflow_state = self.main_window.get_workflow_state()
        selected_book = workflow_state.get('selected_book')

        if not selected_book:
            messagebox.showwarning("提示", "请先选择书籍")
            return

        # 使用兼容的字段访问方法
        book_id = get_book_field(selected_book, 'id')
        if not book_id:
            messagebox.showerror("错误", "书籍ID无效")
            return
        
        self.main_window.update_status("正在加载章节列表...")
        self.load_chapters_button.config(state=tk.DISABLED)
        
        def load_thread():
            try:
                # 获取API客户端
                api_client = self._get_api_client()
                if not api_client:
                    return
                
                # 获取所有章节
                chapters = api_client.get_all_chapters(book_id)
                
                # 在主线程中更新UI
                self.after(0, lambda: self._update_chapters_list(chapters))
                
            except Exception as e:
                self.logger.error(f"加载章节列表失败: {e}")
                self.after(0, lambda: self._handle_load_error(str(e)))
        
        threading.Thread(target=load_thread, daemon=True).start()
    
    def _update_chapters_list(self, chapters: List[Dict[str, Any]]):
        """更新章节列表"""
        # 清空现有结果
        for item in self.chapters_tree.get_children():
            self.chapters_tree.delete(item)
        
        # 添加新结果
        self.chapters_data = chapters
        for chapter in chapters:
            seq_num = chapter.get('chapterSeqNum', 0)
            chapter_id = chapter.get('chapterId', '')
            chapter_name = chapter.get('chapterName', '')
            word_count = chapter.get('numChar', 0)
            edition_state = self._format_state(chapter.get('editionState', 0), "edition")
            recording_state = self._format_state(chapter.get('executionState', 0), "recording")
            audition_state = self._format_state(chapter.get('auditionState', 0), "audition")
            edit_time = self._format_time(chapter.get('updatedTime', 0))

            self.chapters_tree.insert("", tk.END, values=(
                seq_num, chapter_name, word_count, edition_state, recording_state, audition_state, edit_time
            ))
        
        # 更新状态
        count = len(chapters)
        self.main_window.update_status(f"加载了 {count} 个章节")

        # 刷新表格条纹化效果
        refresh_table_stripes(self.chapters_tree)

        # 恢复按钮状态
        self.load_chapters_button.config(state=tk.NORMAL)

        self.logger.info(f"章节列表加载完成，共 {count} 个章节")
    
    def _handle_load_error(self, error_message: str):
        """处理加载错误"""
        messagebox.showerror("加载失败", f"加载章节列表时发生错误:\n{error_message}")
        self.main_window.update_status("加载失败")
        
        # 恢复按钮状态
        self.load_chapters_button.config(state=tk.NORMAL)
    
    def _search_chapters(self):
        """搜索章节"""
        if not self.chapters_data:
            messagebox.showwarning("提示", "请先加载章节列表")
            return
        
        query = self.search_var.get().strip()
        if not query:
            self._clear_search()
            return
        
        search_type = self.search_type_var.get()
        
        # 清空现有显示
        for item in self.chapters_tree.get_children():
            self.chapters_tree.delete(item)
        
        # 筛选章节
        filtered_chapters = []
        
        if search_type == "title":
            # 按标题搜索
            query_lower = query.lower()
            filtered_chapters = [
                chapter for chapter in self.chapters_data
                if query_lower in chapter.get('chapterName', '').lower()
            ]
        elif search_type == "seq_num":
            # 按序号定位
            try:
                target_seq = int(query)
                filtered_chapters = [
                    chapter for chapter in self.chapters_data
                    if chapter.get('chapterSeqNum', 0) == target_seq
                ]
            except ValueError:
                messagebox.showerror("错误", "序号必须是数字")
                return
        
        # 显示筛选结果
        for chapter in filtered_chapters:
            seq_num = chapter.get('chapterSeqNum', 0)
            chapter_id = chapter.get('chapterId', '')
            chapter_name = chapter.get('chapterName', '')
            word_count = chapter.get('numChar', 0)
            edition_state = self._format_state(chapter.get('editionState', 0), "edition")
            recording_state = self._format_state(chapter.get('executionState', 0), "recording")
            audition_state = self._format_state(chapter.get('auditionState', 0), "audition")
            edit_time = self._format_time(chapter.get('updatedTime', 0))

            self.chapters_tree.insert("", tk.END, values=(
                seq_num, chapter_name, word_count, edition_state, recording_state, audition_state, edit_time
            ))
        
        count = len(filtered_chapters)
        self.main_window.update_status(f"找到 {count} 个匹配的章节")

        # 刷新表格条纹化效果
        refresh_table_stripes(self.chapters_tree)
    
    def _clear_search(self):
        """清除搜索"""
        self.search_var.set("")
        self._update_chapters_list(self.chapters_data)
    
    def _select_chapter(self):
        """选择章节"""
        selection = self.chapters_tree.selection()
        if not selection:
            messagebox.showwarning("提示", "请先选择一个章节")
            return

        # 获取选中项的索引
        selected_item = selection[0]
        try:
            # 获取选中项在树形视图中的索引
            all_items = self.chapters_tree.get_children()
            selected_index = all_items.index(selected_item)

            # 通过索引从chapters_data获取完整的章节信息
            if 0 <= selected_index < len(self.chapters_data):
                selected_chapter = self.chapters_data[selected_index]

                # 更新工作流状态
                self.main_window.update_workflow_state('selected_chapter', selected_chapter)

                # 切换到下一步
                self.main_window.next_step()

                self.logger.info(f"已选择章节: {selected_chapter.get('chapterName')}")
            else:
                messagebox.showerror("错误", "选中的章节索引超出范围")
                self.logger.error(f"章节索引超出范围: {selected_index}")

        except (ValueError, IndexError) as e:
            messagebox.showerror("错误", "获取选中章节信息失败")
            self.logger.error(f"获取选中章节信息失败: {e}")
    
    def _view_chapter_detail(self):
        """查看章节详情"""
        selection = self.chapters_tree.selection()
        if not selection:
            return

        # 获取选中项的索引
        selected_item = selection[0]
        try:
            # 获取选中项在树形视图中的索引
            all_items = self.chapters_tree.get_children()
            selected_index = all_items.index(selected_item)

            # 通过索引从chapters_data获取完整的章节信息
            if 0 <= selected_index < len(self.chapters_data):
                selected_chapter = self.chapters_data[selected_index]
                chapter_id = selected_chapter.get('chapterId', '')
                chapter_name = selected_chapter.get('chapterName', '')

                # 这里可以实现查看章节详情的功能
                messagebox.showinfo("章节详情", f"章节ID: {chapter_id}\n章节名称: {chapter_name}\n\n详情功能待实现...")
            else:
                messagebox.showerror("错误", "选中的章节索引超出范围")
                self.logger.error(f"查看详情时章节索引超出范围: {selected_index}")

        except (ValueError, IndexError) as e:
            messagebox.showerror("错误", "获取选中章节信息失败")
            self.logger.error(f"查看详情时获取章节信息失败: {e}")
    
    # _display_selected_chapter 方法已移除，章节信息现在显示在工具栏中
    
    def _format_state(self, state: int, state_type: str) -> str:
        """格式化状态显示"""
        if state_type == "edition":
            # 编辑状态: 0=未发布, 1=编辑中, 2=已提交, 3=已取消
            states = {
                0: "未发布",
                1: "编辑中",
                2: "已提交",
                3: "已取消"
            }
        elif state_type == "recording":
            # 录音状态: 0=未发布, 1=(录音中), 2=(返音中), 3=(已完成), 4=录音中, 5=返音中, 6=已完成
            states = {
                0: "未发布",
                1: "(录音中)",     # 旧状态，保留兼容性
                2: "(返音中)",     # 旧状态，保留兼容性
                3: "(已完成)",     # 旧状态，保留兼容性
                4: "录音中",
                5: "返音中",
                6: "已完成"
            }
        elif state_type == "audition":
            # 审听状态: 0=未发布, 1=发布中, 2=待审听, 3=已提交, 4=已取消, 5=异常
            states = {
                0: "未发布",
                1: "发布中",
                2: "待审听",
                3: "已提交",
                4: "已取消",
                5: "异常"
            }
        else:
            return str(state)

        return states.get(state, f"未知({state})")

    def _format_time(self, timestamp: int) -> str:
        """格式化时间戳显示"""
        if not timestamp:
            return "未知"

        try:
            # 时间戳是毫秒，需要转换为秒
            dt = datetime.fromtimestamp(timestamp / 1000)
            return dt.strftime("%Y-%m-%d %H:%M")
        except (ValueError, OSError):
            return "无效时间"
    
    def _get_api_client(self):
        """获取API客户端"""
        # 从书籍搜索模块获取API客户端
        book_search_frame = self.main_window.book_search_frame
        if hasattr(book_search_frame, 'api_client') and book_search_frame.api_client:
            return book_search_frame.api_client
        
        messagebox.showerror("错误", "API客户端未初始化，请先在书籍搜索页面进行搜索")
        return None
