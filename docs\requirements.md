# GStudio API支持库需求文档

## 1. 项目概述

### 1.1 项目目标
设计并实现一个轻量级的GStudio API支持库，提供API端点定义和基础数据结构，不引入任何第三方依赖，仅使用Python标准库。

### 1.2 设计原则
- 极简原则：保持代码简单，易于理解和维护
- 零依赖：仅使用Python标准库
- 可扩展：便于后续功能扩展
- 类型安全：使用类型注解确保类型安全

## 2. 功能需求

### 2.1 API版本控制（这个版本控制可以采用不同的方式，如URL前缀、版本参数等））
- 支持story和story_v2两个版本
- 提供版本枚举定义
- 支持URL版本前缀管理

### 2.2 API端点定义
- 提供所有API端点的静态定义
- 按功能模块分类管理端点
- 支持URL参数格式化
- 提供URL生成工具方法

#### 2.2.1 必需的端点模块
- 认证相关端点
- 书籍管理端点
  - 书籍列表查询 (`/content/book/list`)
  - 书籍列表编辑器 (`/content/book/list/editor`) - 新增
  - 书籍详情获取 (`/content/book/{book_id}`)
  - 书籍合作成员列表 (`/content/book/partner/list`)
- 章节管理端点
- 团队协作端点
- 根据提供的API调用记录文档，定义端点。

### 2.3 数据结构定义
- 使用dataclass定义标准数据结构
- 提供基础响应结构
- 提供分页参数结构
- 提供业务实体结构

#### 2.3.1 核心数据结构
- API响应结构
- 分页参数结构
- 书籍信息结构
  - 基础书籍信息 (`BookInfo`)
  - 书籍编辑器信息 (`BookEditorInfo`) - 新增，包含完整的20+字段
  - 书籍列表编辑器响应 (`BookListEditorResponse`) - 新增
  - 书籍列表编辑器查询参数 (`BookListEditorParams`) - 新增
- 章节信息结构
- 用户信息结构
- 根据提供的API调用记录文档，定义数据结构。

### 2.4 常量定义
- API基础URL
- 默认请求头
- 错误码定义
- 其他公共常量

## 3. 技术要求

### 3.1 开发环境
- Python 3.7+
- 仅使用Python标准库

### 3.2 代码规范
- 遵循PEP 8编码规范
- 使用类型注解
- 提供完整的文档字符串

### 3.3 文件结构
- 单文件实现
- 清晰的模块组织
- 合理的代码分层

## 4. 使用示例

### 4.1 基础使用
```python
from gstudio_api import APIEndpoints, APIVersion

# 获取API URL
book_list_url = APIEndpoints.get_url(APIEndpoints.Book.LIST)

# 获取书籍列表编辑器API URL（新增）
book_editor_url = APIEndpoints.get_url(APIEndpoints.Book.LIST_EDITOR)

# 获取特定版本的API URL
v1_book_list_url = APIEndpoints.get_url(
    APIEndpoints.Book.LIST,
    version=APIVersion.V1
)
```

### 4.2 数据结构使用
```python
from gstudio_api import (
    BookInfo,
    BookListEditorParams,
    BookEditorInfo,
    APIResponse
)

# 创建书籍信息
book = BookInfo(
    book_id="12345",
    book_name="测试书籍",
    description="这是一本测试书籍"
)

# 创建书籍列表编辑器查询参数（新增）
editor_params = BookListEditorParams(
    page_size=50,
    page_no=1,
    name="小说",
    finished=False,
    sort_item="updatedTime",
    sort_asc=False
)

# 创建API响应
response = APIResponse(
    code=1,
    msg="success",
    data=book
)
```

## 5. 交付标准

### 5.1 代码文件
- `__init__.py`：主模块文件
- `README.md`：使用说明文档
- `requirements.md`：需求文档

### 5.2 文档要求
- 完整的模块文档
- 详细的使用示例
- 清晰的API说明

### 5.3 代码质量
- 代码注释完整
- 命名规范清晰
- 结构层次分明

## 6. 后续扩展

### 6.1 可选扩展方向
- 请求客户端封装
- 响应数据验证
- 错误处理机制
- 日志记录功能

注：这些扩展功能不在当前版本范围内，但设计时应考虑后续扩展的可能性。