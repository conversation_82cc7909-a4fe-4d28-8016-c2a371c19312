# GStudio 音频工作流 GUI 应用程序

这是一个完整的GUI应用程序，用于实现自动化的书籍音频处理工作流。

## 功能特性

### 核心功能
1. **书籍搜索与选择** - 支持模糊匹配和精确匹配的书籍搜索
2. **书籍编辑器管理** - 支持编辑器界面的书籍列表管理，包含完整的书籍配置和权限信息
3. **章节管理** - 章节列表获取、显示和选择，支持按标题搜索和序号定位
4. **段落处理** - 智能筛选"旁白"且朗读者为"机器人"的段落
5. **音频生成** - 批量TTS音频生成，支持本地下载存储和预览播放
6. **音频上传** - 批量音频文件上传，支持断点续传和详细进度显示

### 技术特性
- **完善的错误处理** - 异常捕获和用户友好的错误提示
- **详细的日志系统** - 包含时间戳、操作类型、执行状态的完整日志
- **指数退避重试** - 网络请求的可靠性保障
- **配置管理** - 支持API配置、文件路径等参数的可配置化
- **进度显示** - 实时显示操作进展和状态指示
- **操作历史** - 支持结果导出和操作记录

## 安装说明

### 系统要求
- Python 3.7 或更高版本
- Windows/macOS/Linux 操作系统
- 网络连接（用于API调用）

### 安装步骤

1. **克隆或下载项目**
   ```bash
   # 如果使用git
   git clone <repository_url>
   cd audio_workflow_gui
   
   # 或者直接下载并解压到目标目录
   ```

2. **安装依赖**
   ```bash
   pip install -r requirements.txt
   ```

3. **配置应用程序**
   - 编辑 `config/config.json` 文件
   - 设置API相关配置（如果需要）

## 使用说明

### 启动应用程序
```bash
python main.py
```

### 基本工作流

#### 1. 设置API Token
- 启动应用程序后，点击菜单 "文件" -> "设置API Token"
- 输入有效的API Token

#### 2. 书籍搜索
- 在"书籍搜索"标签页中输入书籍名称
- 选择搜索模式（模糊匹配或精确匹配）
- 点击"搜索"按钮
- 从结果列表中选择目标书籍

#### 3. 章节管理
- 在"章节管理"标签页中点击"加载章节列表"
- 可以按标题搜索或按序号定位特定章节
- 选择要处理的章节

#### 4. 段落处理
- 在"段落处理"标签页中点击"加载章节段落"
- 配置筛选条件（默认为"旁白"+"机器人"）
- 点击"应用筛选"查看筛选结果
- 确认选择并继续

#### 5. 音频生成
- 在"音频生成"标签页中配置TTS参数
- 点击"开始生成"批量生成音频文件
- 可以预览播放生成的音频
- 确认生成结果并继续

#### 6. 音频上传
- 在"音频上传"标签页中配置上传参数
- 点击"开始上传"批量上传音频文件
- 监控上传进度和结果
- 完成整个工作流

### 高级功能

#### 配置管理
- 所有配置保存在 `config/config.json` 文件中
- 支持API配置、文件路径、日志级别等参数调整
- 配置文件会在首次运行时自动创建

#### 文件管理
- 生成的音频文件保存在 `audio_files/generated/` 目录
- 上传的音频文件移动到 `audio_files/uploaded/` 目录
- 临时文件保存在 `audio_files/temp/` 目录

#### 日志系统
- 应用程序日志保存在 `logs/` 目录
- 支持日志轮转，避免文件过大
- 可通过菜单导出日志文件

## 配置说明

### API配置
```json
{
  "api": {
    "base_url": "https://www.gstudios.com.cn",
    "version": "story_v2",
    "timeout": 30,
    "max_retries": 3,
    "retry_delay": 1.0,
    "token": "your_api_token_here"
  }
}
```

### 音频配置
```json
{
  "audio": {
    "default_cv_robot_id": 568,
    "default_speed_factor": 100,
    "default_silence_factor": 100,
    "supported_formats": ["mp3", "wav"],
    "max_file_size": 52428800
  }
}
```

### 筛选配置
```json
{
  "filter": {
    "target_types": ["旁白"],
    "target_cv_type": 0,
    "batch_size": 10
  }
}
```

## 故障排除

### 常见问题

1. **API连接失败**
   - 检查网络连接
   - 验证API Token是否正确
   - 确认API服务器是否可访问
   - 运行`python test_api_endpoints.py`测试API端点可访问性

2. **音频生成失败**
   - 检查TTS配置参数
   - 验证段落内容是否有效
   - 查看错误日志获取详细信息

3. **文件上传失败**
   - 检查文件是否存在
   - 验证文件格式是否支持
   - 确认网络连接稳定

4. **界面无响应**
   - 等待当前操作完成
   - 检查是否有错误对话框弹出
   - 重启应用程序

### 日志查看
- 应用程序运行时的详细日志保存在控制台和日志文件中
- 可通过菜单 "文件" -> "导出日志" 获取日志文件
- 日志包含时间戳、模块名称、日志级别和详细信息

## 开发说明

### 项目结构
```
audio_workflow_gui/
├── main.py                 # 主程序入口
├── config/                 # 配置管理
├── api/                    # API客户端
├── gui/                    # GUI界面模块
├── utils/                  # 工具模块
├── tests/                  # 测试用例
├── audio_files/           # 音频文件存储
├── requirements.txt       # 依赖列表
└── README.md             # 使用文档
```

### 扩展开发
- 所有模块都有清晰的接口定义
- 支持添加新的音频处理功能
- 可以扩展支持更多的API端点
- GUI界面采用模块化设计，易于扩展

## 版本信息

- **版本**: 1.1.0
- **作者**: Augment Agent
- **Python版本**: 3.7+
- **GUI框架**: tkinter

### 更新历史

#### v1.1.0 (2025-07-23)
- 新增书籍列表编辑器API支持 (`/content/book/list/editor`)
- 添加完整的书籍编辑器信息数据结构 (`BookEditorInfo`)
- 支持书籍筛选功能（按名称、备注、完成状态等）
- 增强分页和排序功能
- 基于实际API调用记录优化数据结构

#### v1.0.0 (2025-04-08)
- 初始版本发布
- 基础音频工作流功能
- GUI界面和API集成

## 许可证

本项目仅供学习和研究使用。

## 支持

如有问题或建议，请查看日志文件或联系开发者。
