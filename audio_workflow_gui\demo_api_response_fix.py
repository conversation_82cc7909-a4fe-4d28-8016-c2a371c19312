#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
API响应处理修复演示脚本

演示修复后的API响应处理和书籍搜索功能。

作者：Augment Agent
版本：1.0.0
"""

import sys
import os
import tkinter as tk
import logging

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from config.settings import AppConfig
from gui.main_window import MainWindow


def setup_logging():
    """设置日志"""
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
        handlers=[
            logging.StreamHandler(sys.stdout),
            logging.FileHandler('api_fix_demo.log', encoding='utf-8')
        ]
    )


def demo_api_response_fix():
    """演示API响应处理修复"""
    print("=" * 60)
    print("GStudio API响应处理修复演示")
    print("=" * 60)
    print()
    print("此演示将展示修复后的API响应处理和书籍搜索功能：")
    print()
    print("🐛 修复的问题：")
    print("• API响应码判断错误（200 vs 1）")
    print("• 成功响应被误判为失败")
    print("• 书籍列表显示为0本")
    print()
    print("✅ 修复后的效果：")
    print("• 正确识别API成功响应（code=1）")
    print("• 书籍列表正常显示")
    print("• 状态筛选功能正常工作")
    print("• 详细的调试日志")
    print()
    
    try:
        # 创建主窗口
        root = tk.Tk()
        root.title("GStudio 音频工作流 - API响应修复演示")
        
        # 创建配置对象
        config = AppConfig()
        
        # 创建主窗口实例
        main_window = MainWindow(root, config)
        
        print("✅ 应用程序已启动")
        print()
        print("📋 演示说明：")
        print()
        print("1️⃣ 书籍搜索功能测试：")
        print("   • 点击'书籍搜索'标签页")
        print("   • 点击'刷新全部'按钮")
        print("   • 观察是否能正常显示书籍列表")
        print("   • 检查状态栏是否显示正确的书籍数量")
        print()
        print("2️⃣ 状态筛选功能测试：")
        print("   • 尝试选择'制作中'筛选")
        print("   • 尝试选择'已完成'筛选")
        print("   • 观察筛选结果是否正确")
        print("   • 检查表格中的'制作状态'列")
        print()
        print("3️⃣ 搜索功能测试：")
        print("   • 输入书籍名称进行搜索")
        print("   • 结合状态筛选进行搜索")
        print("   • 测试精确匹配功能")
        print()
        print("4️⃣ 日志观察：")
        print("   • 观察控制台日志输出")
        print("   • 应该看到'搜索完成，找到 X 本书籍'")
        print("   • 不应该再看到'API返回失败: 成功!'")
        print()
        
        # 显示当前配置
        ssl_status = "启用" if config.is_ssl_verification_enabled() else "禁用"
        token_status = "已设置" if config.get('api.token') else "未设置"
        
        print(f"📋 当前配置：")
        print(f"• SSL验证：{ssl_status}")
        print(f"• API Token：{token_status}")
        print(f"• API地址：{config.get('api.base_url')}")
        print()
        
        print("🚀 开始演示...")
        print("请在应用程序中测试修复后的书籍搜索功能")
        print()
        
        # 运行应用程序
        root.mainloop()
        
        print("演示结束")
        
    except Exception as e:
        print(f"演示过程中发生错误: {e}")
        import traceback
        traceback.print_exc()


def show_fix_details():
    """显示修复详情"""
    print("\n" + "=" * 60)
    print("API响应处理修复详情")
    print("=" * 60)
    print()
    print("🔍 问题分析：")
    print()
    print("从您提供的日志可以看出：")
    print("```")
    print("2025-07-24 14:24:54,680 - GStudioAPIClient - INFO - 请求完成: GET ... - 200 (0.054s)")
    print("2025-07-24 14:24:54,680 - BookSearchFrame - WARNING - API返回失败: 成功!")
    print("2025-07-24 14:24:54,682 - BookSearchFrame - INFO - 搜索完成，找到 0 本书籍")
    print("```")
    print()
    print("问题分析：")
    print("1. HTTP请求成功（状态码200）")
    print("2. 但应用程序判断为'API返回失败'")
    print("3. 错误消息是'成功!'（很矛盾）")
    print("4. 最终找到0本书籍")
    print()
    print("🔧 根本原因：")
    print()
    print("在 _search_books_with_status() 方法中：")
    print("```python")
    print("# 错误的判断逻辑")
    print("if response.code == 200 and response.data:")
    print("    return response.data.get('list', [])")
    print("```")
    print()
    print("但实际上：")
    print("• HTTP状态码是200（成功）")
    print("• API业务状态码是1（ErrorCodes.SUCCESS）")
    print("• 我们应该判断 response.code == 1")
    print()
    print("✅ 修复方案：")
    print()
    print("1. 修正响应码判断：")
    print("```python")
    print("# 修复后的判断逻辑")
    print("if response.code == 1 and response.data:  # ErrorCodes.SUCCESS = 1")
    print("    book_list = response.data.get('list', [])")
    print("    self.logger.debug(f'获取到书籍列表，数量: {len(book_list)}')")
    print("    return book_list")
    print("```")
    print()
    print("2. 增强调试日志：")
    print("```python")
    print("# 添加详细的调试信息")
    print("self.logger.debug(f'API响应: code={response.code}, msg={response.msg}, data_type={type(response.data)}')")
    print("```")
    print()
    print("3. 改进错误处理：")
    print("```python")
    print("# 更详细的错误日志")
    print("self.logger.warning(f'API返回失败: code={response.code}, msg={response.msg}')")
    print("```")
    print()
    print("🎯 修复效果：")
    print()
    print("修复前的日志：")
    print("```")
    print("BookSearchFrame - WARNING - API返回失败: 成功!")
    print("BookSearchFrame - INFO - 搜索完成，找到 0 本书籍")
    print("```")
    print()
    print("修复后的日志：")
    print("```")
    print("BookSearchFrame - DEBUG - API响应: code=1, msg=成功!, data_type=<class 'dict'>")
    print("BookSearchFrame - DEBUG - 获取到书籍列表，数量: 2")
    print("BookSearchFrame - INFO - 搜索完成，找到 2 本书籍")
    print("```")
    print()
    print("📊 测试验证：")
    print()
    print("通过测试脚本验证：")
    print("• ✅ ErrorCodes.SUCCESS = 1")
    print("• ✅ API响应码正确 (code=1)")
    print("• ✅ 获取到书籍列表，数量: 2")
    print("• ✅ 书籍字段完整，包含finished、createdTime等")
    print()


def main():
    """主函数"""
    print("GStudio API响应处理修复演示")
    print("=" * 60)
    
    # 设置日志
    setup_logging()
    
    try:
        # 显示修复详情
        show_fix_details()
        
        # 询问是否开始演示
        response = input("是否要启动GUI演示？(y/n): ").lower().strip()
        
        if response in ['y', 'yes', '是', '1']:
            demo_api_response_fix()
        else:
            print("演示已取消")
        
    except KeyboardInterrupt:
        print("\n演示被用户中断")
    except Exception as e:
        print(f"演示过程中发生异常: {e}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    main()
