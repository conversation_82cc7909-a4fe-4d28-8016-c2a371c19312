# GStudio 音频工作流 GUI - 调试功能使用指南

## 概述

本指南详细介绍了GStudio音频工作流GUI应用程序中增强的调试功能，帮助您快速定位和解决API连接问题。

## 新增调试功能

### 1. 详细的HTTP请求/响应日志

#### 功能特性
- **完整请求信息**：URL、方法、参数、请求头、请求体
- **API Token安全显示**：只显示前8位和后4位字符
- **网络连接诊断**：DNS解析、TCP连接、SSL验证状态
- **响应详情**：状态码、响应头、响应体（前200字符）、响应时间

#### 示例输出
```
2025-07-23 19:43:16,724 - GStudioAPIClient - DEBUG - === HTTP请求详细信息 ===
2025-07-23 19:43:16,724 - GStudioAPIClient - DEBUG - URL: https://www.gstudios.com.cn/story_v2/api/content/book/list
2025-07-23 19:43:16,724 - GStudioAPIClient - DEBUG - 方法: GET
2025-07-23 19:43:16,725 - GStudioAPIClient - DEBUG - 超时设置: 10秒
2025-07-23 19:43:16,725 - GStudioAPIClient - DEBUG - 请求头: {
  "authorization": "Bearer test_tok...7890"
}
2025-07-23 19:43:18,573 - GStudioAPIClient - DEBUG - 网络连接诊断: {
  "dns_resolution": true,
  "tcp_connection": true,
  "ssl_verification": true,
  "ip_address": "**************"
}
```

### 2. 网络连接诊断

#### 功能特性
- **DNS解析测试**：验证域名是否能正确解析到IP地址
- **TCP连接测试**：验证是否能建立TCP连接
- **SSL证书验证**：验证HTTPS连接的SSL证书
- **IP地址显示**：显示解析到的实际IP地址

#### 使用方法
```python
# 在代码中调用
client = GStudioAPIClient(debug_mode=True)
result = client._test_network_connectivity("https://www.gstudios.com.cn")
```

### 3. API连接测试功能

#### 功能特性
- **多端点测试**：同时测试书籍列表、章节列表、用户门户、TTS试听等端点
- **响应时间统计**：测量每个端点的响应时间
- **状态分析**：自动分析端点状态并提供建议
- **详细报告**：生成完整的连接测试报告

#### GUI使用方法
1. 在书籍搜索页面点击"测试API连接"按钮
2. 查看弹出的详细测试结果窗口
3. 根据建议进行相应的配置调整

### 4. 增强的错误处理

#### 功能特性
- **详细错误堆栈**：显示完整的调用堆栈信息
- **配置上下文**：记录出错时的配置参数
- **重试详情**：记录每次重试的详细过程
- **用户友好提示**：在GUI中显示具体的解决建议

#### 错误信息示例
```
2025-07-23 19:43:20,864 - GStudioAPIClient - DEBUG - === 错误详细信息 ===
2025-07-23 19:43:20,864 - GStudioAPIClient - DEBUG - 异常类型: HTTPError
2025-07-23 19:43:20,864 - GStudioAPIClient - DEBUG - 异常消息: 401 Client Error: Unauthorized
2025-07-23 19:43:20,872 - GStudioAPIClient - DEBUG - 当前配置: {
  "base_url": "https://www.gstudios.com.cn",
  "version": "story_v2",
  "timeout": 10,
  "has_token": true
}
```

## 启用调试模式

### 方法1：通过GUI界面

1. **打开调试设置**：
   - 启动应用程序
   - 点击菜单栏：工具 → 调试模式设置

2. **配置日志级别**：
   - 选择"DEBUG - 调试模式"
   - 点击"应用设置"
   - 重启应用程序

3. **验证调试模式**：
   - 重启后，应用程序将输出详细的调试信息
   - 在书籍搜索页面点击"测试API连接"查看详细诊断

### 方法2：通过配置文件

编辑 `config/config.json` 文件：
```json
{
  "logging": {
    "level": "DEBUG",
    "file_enabled": true,
    "console_enabled": true
  }
}
```

### 方法3：通过环境变量

设置环境变量：
```bash
# Windows
set GSTUDIO_DEBUG=1

# Linux/macOS
export GSTUDIO_DEBUG=1
```

## 调试功能使用场景

### 场景1：API连接失败

**问题症状**：
```
HTTP请求失败: 404 Client Error: Not Found for url: https://www.gstudios.com.cn/story_v2/api/content/book/list
```

**调试步骤**：
1. 启用调试模式
2. 点击"测试API连接"按钮
3. 查看网络连接诊断结果
4. 检查API端点状态

**可能的解决方案**：
- 网络连接问题：检查DNS、TCP、SSL状态
- API Token问题：验证Token是否正确设置
- 超时问题：增加超时时间设置
- 服务器问题：联系API提供方

### 场景2：请求超时

**问题症状**：
```
requests.exceptions.Timeout: HTTPSConnectionPool(host='www.gstudios.com.cn', port=443): Read timed out.
```

**调试步骤**：
1. 查看响应时间统计
2. 检查网络连接质量
3. 调整超时配置

**解决方案**：
```json
{
  "api": {
    "timeout": 120,
    "max_retries": 5,
    "retry_delay": 3.0
  }
}
```

### 场景3：认证错误

**问题症状**：
```
401 Client Error: Unauthorized
```

**调试步骤**：
1. 检查Token是否正确设置
2. 验证Token格式和有效性
3. 查看API响应中的错误信息

**解决方案**：
- 重新设置正确的API Token
- 联系API提供方验证Token状态

## 日志文件管理

### 日志文件位置
- **应用程序日志**：`logs/app.log`
- **调试测试日志**：`debug_test.log`
- **控制台输出**：实时显示在终端

### 日志级别说明
- **DEBUG**：最详细的调试信息
- **INFO**：一般信息和状态更新
- **WARNING**：警告信息和重试通知
- **ERROR**：错误信息和异常详情

### 日志文件轮转
- 自动轮转：当日志文件超过10MB时
- 保留文件：最多保留5个历史文件
- 压缩存储：历史文件自动压缩

## 性能影响说明

### 调试模式的影响
- **网络开销**：每次请求前会进行连接诊断（约2-3秒）
- **日志开销**：详细日志会增加磁盘I/O
- **内存开销**：保存更多的调试信息

### 建议使用方式
- **开发/测试环境**：始终启用调试模式
- **生产环境**：只在需要诊断问题时启用
- **临时诊断**：通过GUI快速切换调试模式

## 故障排除工具

### 1. 连接测试脚本
```bash
cd audio_workflow_gui
python test_debug_features.py
```

### 2. API端点测试
```bash
cd audio_workflow_gui
python test_api_endpoints.py
```

### 3. 网络诊断
```bash
cd audio_workflow_gui
python debug_api.py
```

## 常见问题解答

### Q: 调试模式下应用程序变慢了？
A: 这是正常的，调试模式会进行额外的网络诊断和详细日志记录。生产使用时建议关闭调试模式。

### Q: 如何查看完整的API请求和响应？
A: 启用DEBUG日志级别，所有HTTP请求和响应的详细信息都会记录在日志中。

### Q: Token信息会被完整记录吗？
A: 不会，为了安全考虑，Token只会显示前8位和后4位字符。

### Q: 如何导出调试日志？
A: 通过菜单"文件 → 导出日志"可以导出完整的日志文件。

### Q: 调试信息太多，如何筛选？
A: 可以通过日志级别控制输出详细程度，或使用文本编辑器的搜索功能筛选特定信息。

## 技术支持

如果调试功能无法解决您的问题，请：

1. **收集信息**：
   - 启用调试模式
   - 重现问题
   - 导出完整日志

2. **提供详情**：
   - 错误信息截图
   - 完整的日志文件
   - 网络环境描述
   - 操作系统信息

3. **联系支持**：
   - 提交问题报告
   - 附上调试日志
   - 描述重现步骤

通过这些增强的调试功能，您应该能够快速定位和解决大部分API连接问题。
