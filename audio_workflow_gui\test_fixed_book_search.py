#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试修复后的书籍搜索功能

验证参数绑定问题是否已解决。

作者：Augment Agent
版本：1.0.0
"""

import sys
import os
import logging

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from api.client import GStudioAPIClient
from api.models import BookListEditorParams


def setup_logging():
    """设置日志"""
    logging.basicConfig(
        level=logging.DEBUG,
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
        handlers=[
            logging.StreamHandler(sys.stdout),
            logging.FileHandler('fixed_book_search_test.log', encoding='utf-8')
        ]
    )


def test_fixed_parameters():
    """测试修复后的参数处理"""
    print("=" * 60)
    print("测试修复后的参数处理")
    print("=" * 60)
    
    # 创建调试模式的API客户端
    client = GStudioAPIClient(debug_mode=True)
    
    # 设置测试token（如果有的话）
    test_token = input("请输入API Token进行测试（可选，直接回车跳过）: ").strip()
    if test_token:
        client.set_token(test_token)
        print("✓ API Token已设置")
    else:
        print("⚠ 跳过Token设置，将测试无认证情况")
    
    print("\n1. 测试最小参数集...")
    try:
        params = BookListEditorParams(
            page_size=10,
            page_no=1
        )
        
        response = client.get_book_list_editor(params)
        
        print(f"✓ API调用成功")
        print(f"  响应码: {response.code}")
        print(f"  响应消息: {response.msg}")
        
        if response.code == 1 and response.data:
            book_list = response.data.get('list', [])
            total = response.data.get('total', 0)
            print(f"  ✓ 成功获取 {len(book_list)} 本书籍，总计 {total} 本")
            
            if book_list:
                first_book = book_list[0]
                print(f"  第一本书籍字段: {list(first_book.keys())}")
                print(f"  书籍示例: ID={first_book.get('id')}, 名称={first_book.get('name')}")
        else:
            print(f"  API响应: {response.msg}")
            
    except Exception as e:
        print(f"✗ API调用失败: {e}")
    
    print("\n2. 测试带搜索参数...")
    try:
        params = BookListEditorParams(
            page_size=5,
            page_no=1,
            name="测试",
            finished=False,
            sort_item="bookName",
            sort_asc=True
        )
        
        response = client.get_book_list_editor(params)
        
        print(f"✓ 搜索API调用成功")
        print(f"  响应码: {response.code}")
        print(f"  响应消息: {response.msg}")
        
        if response.code == 1 and response.data:
            book_list = response.data.get('list', [])
            total = response.data.get('total', 0)
            print(f"  ✓ 搜索到 {len(book_list)} 本书籍，总计 {total} 本")
            
            for i, book in enumerate(book_list[:3], 1):
                book_name = book.get('name', '未知')
                book_id = book.get('id', '未知')
                print(f"    {i}. {book_name} (ID: {book_id})")
        else:
            print(f"  API响应: {response.msg}")
            
    except Exception as e:
        print(f"✗ 搜索API调用失败: {e}")
    
    print("\n3. 测试便捷搜索方法...")
    try:
        books = client.search_books("小说", page_size=5)
        
        print(f"✓ 便捷搜索方法调用成功")
        print(f"  获取到 {len(books)} 本书籍")
        
        for i, book in enumerate(books, 1):
            book_name = book.get('name', book.get('bookName', '未知'))
            book_id = book.get('id', book.get('bookId', '未知'))
            print(f"    {i}. {book_name} (ID: {book_id})")
            
    except Exception as e:
        print(f"✗ 便捷搜索方法调用失败: {e}")


def test_parameter_formats():
    """测试不同的参数格式"""
    print("\n" + "=" * 60)
    print("测试参数格式处理")
    print("=" * 60)
    
    client = GStudioAPIClient(debug_mode=True)
    
    # 测试布尔值处理
    test_cases = [
        ("布尔值True", True),
        ("布尔值False", False),
        ("None值", None)
    ]
    
    for case_name, finished_value in test_cases:
        print(f"\n测试 {case_name}:")
        
        try:
            params = BookListEditorParams(
                page_size=5,
                page_no=1,
                finished=finished_value
            )
            
            # 不实际发送请求，只测试参数构建
            print(f"  ✓ 参数构建成功: finished={finished_value}")
            
        except Exception as e:
            print(f"  ✗ 参数构建失败: {e}")


def main():
    """主测试函数"""
    print("GStudio 修复后的书籍搜索功能测试")
    print("=" * 60)
    print("此测试将验证:")
    print("• 修复后的参数处理逻辑")
    print("• 布尔值转换为字符串")
    print("• 参数绑定问题解决")
    print("• 书籍搜索功能正常工作")
    print()
    
    # 设置日志
    setup_logging()
    
    try:
        # 运行测试
        test_parameter_formats()
        test_fixed_parameters()
        
        print("\n" + "=" * 60)
        print("测试完成！")
        print("=" * 60)
        print("如果看到'参数绑定失败'错误已解决，说明修复成功")
        print("请查看:")
        print("1. 控制台输出的测试结果")
        print("2. fixed_book_search_test.log 文件中的详细日志")
        
    except Exception as e:
        print(f"测试过程中发生异常: {e}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    main()
