#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
文件管理模块

提供音频文件的存储、管理和组织功能。

作者：Augment Agent
版本：1.0.0
"""

import os
import shutil
import hashlib
from pathlib import Path
from typing import Optional, Dict, Any
from datetime import datetime

from .logger import LoggerMixin


class FileManager(LoggerMixin):
    """文件管理器"""
    
    def __init__(self, base_dir: str = "audio_files"):
        """
        初始化文件管理器
        
        Args:
            base_dir: 基础存储目录
        """
        self.base_dir = Path(base_dir)
        self.base_dir.mkdir(parents=True, exist_ok=True)
        
        # 创建子目录
        self.temp_dir = self.base_dir / "temp"
        self.generated_dir = self.base_dir / "generated"
        self.uploaded_dir = self.base_dir / "uploaded"
        
        for dir_path in [self.temp_dir, self.generated_dir, self.uploaded_dir]:
            dir_path.mkdir(parents=True, exist_ok=True)
    
    def save_audio_file(
        self,
        content: bytes,
        filename: str,
        category: str = "generated",
        book_id: Optional[str] = None,
        chapter_id: Optional[str] = None,
        cue_id: Optional[str] = None
    ) -> Path:
        """
        保存音频文件
        
        Args:
            content: 音频文件内容
            filename: 文件名
            category: 文件类别 (generated, uploaded, temp)
            book_id: 书籍ID
            chapter_id: 章节ID
            cue_id: 内容片ID
            
        Returns:
            Path: 保存的文件路径
        """
        # 确定保存目录
        if category == "generated":
            save_dir = self.generated_dir
        elif category == "uploaded":
            save_dir = self.uploaded_dir
        else:
            save_dir = self.temp_dir
        
        # 创建层级目录结构
        if book_id:
            save_dir = save_dir / book_id
            if chapter_id:
                save_dir = save_dir / chapter_id
        
        save_dir.mkdir(parents=True, exist_ok=True)
        
        # 生成唯一文件名
        if cue_id:
            base_name = f"{cue_id}_{filename}"
        else:
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            name, ext = os.path.splitext(filename)
            base_name = f"{name}_{timestamp}{ext}"
        
        file_path = save_dir / base_name
        
        # 如果文件已存在，添加序号
        counter = 1
        original_path = file_path
        while file_path.exists():
            name, ext = os.path.splitext(original_path.name)
            file_path = original_path.parent / f"{name}_{counter}{ext}"
            counter += 1
        
        # 保存文件
        try:
            with open(file_path, 'wb') as f:
                f.write(content)
            
            self.logger.info(f"音频文件已保存: {file_path}")
            return file_path
            
        except Exception as e:
            self.logger.error(f"保存音频文件失败: {e}")
            raise
    
    def get_file_info(self, file_path: Path) -> Dict[str, Any]:
        """
        获取文件信息
        
        Args:
            file_path: 文件路径
            
        Returns:
            Dict: 文件信息
        """
        try:
            stat = file_path.stat()
            
            # 计算文件哈希
            with open(file_path, 'rb') as f:
                file_hash = hashlib.md5(f.read()).hexdigest()
            
            return {
                'path': str(file_path),
                'name': file_path.name,
                'size': stat.st_size,
                'created_time': datetime.fromtimestamp(stat.st_ctime),
                'modified_time': datetime.fromtimestamp(stat.st_mtime),
                'hash': file_hash
            }
            
        except Exception as e:
            self.logger.error(f"获取文件信息失败: {e}")
            return {}
    
    def move_file(self, source: Path, destination: Path) -> bool:
        """
        移动文件
        
        Args:
            source: 源文件路径
            destination: 目标文件路径
            
        Returns:
            bool: 移动成功返回True
        """
        try:
            # 确保目标目录存在
            destination.parent.mkdir(parents=True, exist_ok=True)
            
            shutil.move(str(source), str(destination))
            self.logger.info(f"文件已移动: {source} -> {destination}")
            return True
            
        except Exception as e:
            self.logger.error(f"移动文件失败: {e}")
            return False
    
    def delete_file(self, file_path: Path) -> bool:
        """
        删除文件
        
        Args:
            file_path: 文件路径
            
        Returns:
            bool: 删除成功返回True
        """
        try:
            if file_path.exists():
                file_path.unlink()
                self.logger.info(f"文件已删除: {file_path}")
                return True
            else:
                self.logger.warning(f"文件不存在: {file_path}")
                return False
                
        except Exception as e:
            self.logger.error(f"删除文件失败: {e}")
            return False
    
    def cleanup_temp_files(self, max_age_hours: int = 24) -> int:
        """
        清理临时文件
        
        Args:
            max_age_hours: 最大保留时间（小时）
            
        Returns:
            int: 清理的文件数量
        """
        try:
            current_time = datetime.now()
            deleted_count = 0
            
            for file_path in self.temp_dir.rglob("*"):
                if file_path.is_file():
                    file_age = current_time - datetime.fromtimestamp(file_path.stat().st_mtime)
                    
                    if file_age.total_seconds() > max_age_hours * 3600:
                        if self.delete_file(file_path):
                            deleted_count += 1
            
            self.logger.info(f"清理了 {deleted_count} 个临时文件")
            return deleted_count
            
        except Exception as e:
            self.logger.error(f"清理临时文件失败: {e}")
            return 0
    
    def get_storage_stats(self) -> Dict[str, Any]:
        """
        获取存储统计信息
        
        Returns:
            Dict: 存储统计信息
        """
        try:
            stats = {}
            
            for category, dir_path in [
                ("generated", self.generated_dir),
                ("uploaded", self.uploaded_dir),
                ("temp", self.temp_dir)
            ]:
                file_count = 0
                total_size = 0
                
                for file_path in dir_path.rglob("*"):
                    if file_path.is_file():
                        file_count += 1
                        total_size += file_path.stat().st_size
                
                stats[category] = {
                    'file_count': file_count,
                    'total_size': total_size,
                    'total_size_mb': round(total_size / 1024 / 1024, 2)
                }
            
            return stats
            
        except Exception as e:
            self.logger.error(f"获取存储统计失败: {e}")
            return {}
