# GStudio API调用记录分析报告

## 概述

基于API调用记录文档 `request_log_20250724_071930.txt` 的分析，我们发现了3个新的API端点，并已将其集成到代码库中。

## 发现的API端点

### 1. 获取书籍角色列表 API

**端点信息：**
- **URL**: `/story_v2/api/content/character/list/book`
- **方法**: GET
- **用途**: 获取指定书籍的所有角色信息

**请求参数：**
```
bookId: 12345 (书籍ID)
roleType: book (角色类型)
skipStat: true (是否跳过统计信息)
```

**响应数据结构：**
```json
{
  "code": 1,
  "data": {
    "list": [
      {
        "id": 5530521,
        "name": "曹喜",
        "type": null,
        "gender": 1,
        "ageType": 2,
        "color": "#FF6B6B",
        "description": "角色描述",
        "nicknames": "",
        "ableToMerge": false,
        "autoMatch": true,
        "cvHumanId": 42544,
        "cvHumanName": "破晓远航 青中拒老监可男主",
        "cvRobotId": 1,
        "cvRobotName": "云希",
        "debutChapterId": "8607068",
        "debutChapterName": "0001集.",
        "numChapter": 5,
        "numChar": 1234,
        "numCue": 56,
        "eqConfig": {
          "bp1": {"freq": 200, "gain": 0, "q": 1, "enabled": false},
          "bp2": {"freq": 1000, "gain": 0, "q": 1, "enabled": false},
          "bp3": {"freq": 5000, "gain": 0, "q": 1, "enabled": false},
          "hs": {"freq": 8000, "gain": 0, "q": 0.7, "enabled": false},
          "ls": {"freq": 100, "gain": 0, "q": 0.7, "enabled": false}
        }
      }
    ]
  }
}
```

### 2. 获取章节角色列表 API

**端点信息：**
- **URL**: `/story_v2/api/content/character/list/chapter`
- **方法**: GET
- **用途**: 获取指定章节的所有角色信息

**请求参数：**
```
chapterId: 8607068 (章节ID)
roleType: chapter (角色类型)
skipStat: true (是否跳过统计信息)
```

**响应数据结构：**
与书籍角色列表API相同，但返回的是该章节中出现的角色。

### 3. 获取章节片段录音任务状态列表 API

**端点信息：**
- **URL**: `/story_v2/api/record/task/state/chapter/cues`
- **方法**: GET
- **用途**: 获取指定章节所有片段的录音任务状态

**请求参数：**
```
chapterId: 8607068 (章节ID)
```

**响应数据结构：**
```json
{
  "code": 1,
  "data": {
    "infoHuman": [
      {
        "beenFinished": false,
        "characterId": 5530521,
        "cueId": 123456,
        "cvId": 42544,
        "numCharCharged": null,
        "taskId": null,
        "taskState": null
      }
    ],
    "infoRobot": [
      {
        "beenFinished": true,
        "characterId": 5530521,
        "cueId": 123457,
        "cvId": 1,
        "numCharCharged": 50,
        "taskId": 789012,
        "taskState": 2
      }
    ]
  }
}
```

## 代码实现

### 1. API端点定义

在 `simple-gstudio-api/gstudio_api.py` 和 `audio_workflow_gui/api/models.py` 中添加了：

```python
class Character:
    """角色相关接口"""
    LIST_BOOK = "/content/character/list/book"
    LIST_CHAPTER = "/content/character/list/chapter"

class Record:
    """录音相关接口"""
    # ... 现有端点
    TASK_STATE_CHAPTER_CUES = "/record/task/state/chapter/cues"
```

### 2. 参数类定义

```python
@dataclass
class CharacterListBookParams:
    """获取书籍角色列表参数"""
    book_id: int
    role_type: str = "book"
    skip_stat: bool = True

@dataclass
class CharacterListChapterParams:
    """获取章节角色列表参数"""
    chapter_id: int
    role_type: str = "chapter"
    skip_stat: bool = True

@dataclass
class RecordTaskStateParams:
    """获取章节片段录音任务状态参数"""
    chapter_id: int
```

### 3. 响应数据类定义

```python
@dataclass
class CharacterInfo:
    """角色信息"""
    id: int
    name: str
    type: Optional[int]
    gender: Optional[int]
    age_type: Optional[int]
    color: str
    description: str
    nicknames: str
    # ... 更多字段

@dataclass
class RecordTaskInfo:
    """录音任务信息"""
    been_finished: bool
    character_id: int
    cue_id: int
    cv_id: Optional[int]
    num_char_charged: Optional[int]
    task_id: Optional[int]
    task_state: Optional[int]

@dataclass
class RecordTaskStateResponse:
    """章节片段录音任务状态响应"""
    info_human: List[RecordTaskInfo]
    info_robot: List[RecordTaskInfo]
```

### 4. 客户端方法实现

在 `audio_workflow_gui/api/client.py` 中添加了：

```python
def get_book_characters(self, params: CharacterListBookParams) -> APIResponse:
    """获取书籍角色列表"""
    query_params = {
        'bookId': params.book_id,
        'roleType': params.role_type,
        'skipStat': str(params.skip_stat).lower()
    }
    return self._request('GET', APIEndpoints.Character.LIST_BOOK, params=query_params)

def get_chapter_characters(self, params: CharacterListChapterParams) -> APIResponse:
    """获取章节角色列表"""
    query_params = {
        'chapterId': params.chapter_id,
        'roleType': params.role_type,
        'skipStat': str(params.skip_stat).lower()
    }
    return self._request('GET', APIEndpoints.Character.LIST_CHAPTER, params=query_params)

def get_chapter_record_task_state(self, params: RecordTaskStateParams) -> APIResponse:
    """获取章节片段录音任务状态列表"""
    query_params = {'chapterId': params.chapter_id}
    return self._request('GET', APIEndpoints.Record.TASK_STATE_CHAPTER_CUES, params=query_params)
```

## 使用示例

### 获取书籍角色列表

```python
from api.client import GStudioAPIClient
from api.models import CharacterListBookParams

# 创建客户端
client = GStudioAPIClient()

# 设置认证token
client.set_token("your_token_here")

# 获取书籍角色列表
params = CharacterListBookParams(book_id=12345)
response = client.get_book_characters(params)

if response.success:
    characters = response.data if response.data else []
    for character in characters:
        print(f"角色: {character['name']}, ID: {character['id']}")
else:
    print(f"请求失败: {response.error}")
```

### 获取章节角色列表

```python
from api.models import CharacterListChapterParams

# 获取章节角色列表
params = CharacterListChapterParams(chapter_id=8607068)
response = client.get_chapter_characters(params)

if response.success:
    characters = response.data if response.data else []
    print(f"章节包含 {len(characters)} 个角色")
```

### 获取录音任务状态

```python
from api.models import RecordTaskStateParams

# 获取录音任务状态
params = RecordTaskStateParams(chapter_id=8607068)
response = client.get_chapter_record_task_state(params)

if response.success:
    data = response.data
    human_tasks = data.get('infoHuman', [])
    robot_tasks = data.get('infoRobot', [])
    
    print(f"人工录音任务: {len(human_tasks)} 个")
    print(f"机器录音任务: {len(robot_tasks)} 个")
    
    # 统计完成状态
    finished_human = sum(1 for task in human_tasks if task['beenFinished'])
    finished_robot = sum(1 for task in robot_tasks if task['beenFinished'])
    
    print(f"人工录音完成: {finished_human}/{len(human_tasks)}")
    print(f"机器录音完成: {finished_robot}/{len(robot_tasks)}")
```

## 错误处理

所有API调用都应该包含适当的错误处理：

```python
try:
    response = client.get_book_characters(params)
    if response.success:
        # 处理成功响应
        data = response.data
    else:
        # 处理API错误
        print(f"API错误: {response.error}")
        print(f"错误码: {response.code}")
except Exception as e:
    # 处理网络或其他异常
    print(f"请求异常: {e}")
```

## 最佳实践

1. **参数验证**: 在调用API前验证必需参数
2. **错误处理**: 始终检查响应状态并处理错误
3. **日志记录**: 记录重要的API调用和错误信息
4. **缓存策略**: 对于不经常变化的数据（如角色列表）考虑缓存
5. **并发控制**: 避免同时发起过多API请求

## 注意事项

1. **认证要求**: 所有API都需要有效的Bearer token
2. **频率限制**: 注意API调用频率，避免触发限制
3. **数据一致性**: 角色信息可能在不同时间点有所变化
4. **错误重试**: 对于网络错误，实现适当的重试机制

## 后续改进建议

1. **批量操作**: 考虑添加批量获取多个章节角色的API
2. **实时更新**: 考虑使用WebSocket获取实时的录音任务状态
3. **数据缓存**: 实现本地缓存减少API调用
4. **性能优化**: 对于大量数据的场景，考虑分页加载
