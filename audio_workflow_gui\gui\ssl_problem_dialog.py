#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
SSL问题处理对话框

当检测到SSL证书验证失败时，自动弹出的问题处理对话框。

作者：Augment Agent
版本：1.0.0
"""

import tkinter as tk
from tkinter import ttk, messagebox
import threading
from typing import Optional, Callable

from utils.logger import LoggerMixin
from utils.ssl_fix import SSLFixer


class SSLProblemDialog(tk.Toplevel, LoggerMixin):
    """SSL问题处理对话框"""
    
    def __init__(self, parent, config, error_message: str, on_solution_applied: Optional[Callable] = None):
        """
        初始化SSL问题处理对话框
        
        Args:
            parent: 父窗口
            config: 应用程序配置对象
            error_message: SSL错误消息
            on_solution_applied: 解决方案应用后的回调函数
        """
        super().__init__(parent)
        LoggerMixin.__init__(self)
        
        self.config = config
        self.error_message = error_message
        self.on_solution_applied = on_solution_applied
        self.ssl_fixer = SSLFixer()
        self.solution_applied = False
        
        self.title("SSL连接问题")
        self.geometry("550x400")
        self.resizable(False, False)
        
        # 设置为模态对话框
        self.transient(parent)
        self.grab_set()
        
        # 居中显示
        self._center_window()
        
        # 创建界面
        self._create_widgets()
        
        # 显示错误信息
        self._display_error_info()
        
        self.logger.info("SSL问题处理对话框已打开")
    
    def _center_window(self):
        """将窗口居中显示"""
        self.update_idletasks()
        width = self.winfo_width()
        height = self.winfo_height()
        x = (self.winfo_screenwidth() // 2) - (width // 2)
        y = (self.winfo_screenheight() // 2) - (height // 2)
        self.geometry(f"{width}x{height}+{x}+{y}")
    
    def _create_widgets(self):
        """创建界面组件"""
        # 主框架
        main_frame = ttk.Frame(self, padding="15")
        main_frame.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))
        
        # 标题
        title_label = ttk.Label(
            main_frame,
            text="🔒 SSL证书验证失败",
            font=("Arial", 12, "bold")
        )
        title_label.grid(row=0, column=0, sticky=tk.W, pady=(0, 10))
        
        # 问题描述
        desc_label = ttk.Label(
            main_frame,
            text="检测到SSL证书验证问题，这可能会阻止应用程序正常连接到服务器。",
            wraplength=500
        )
        desc_label.grid(row=1, column=0, sticky=tk.W, pady=(0, 10))
        
        # 错误信息框架
        error_frame = ttk.LabelFrame(main_frame, text="错误详情", padding="10")
        error_frame.grid(row=2, column=0, sticky=(tk.W, tk.E), pady=(0, 15))
        
        # 错误信息显示
        self.error_text = tk.Text(
            error_frame,
            height=4,
            width=60,
            wrap=tk.WORD,
            state=tk.DISABLED,
            background="#f0f0f0"
        )
        error_scrollbar = ttk.Scrollbar(error_frame, orient=tk.VERTICAL, command=self.error_text.yview)
        self.error_text.configure(yscrollcommand=error_scrollbar.set)
        
        self.error_text.grid(row=0, column=0, sticky=(tk.W, tk.E))
        error_scrollbar.grid(row=0, column=1, sticky=(tk.N, tk.S))
        
        # 解决方案框架
        solution_frame = ttk.LabelFrame(main_frame, text="解决方案", padding="10")
        solution_frame.grid(row=3, column=0, sticky=(tk.W, tk.E), pady=(0, 15))
        
        # 解决方案选项
        self.solution_var = tk.StringVar(value="bypass")
        
        # 临时绕过选项
        bypass_radio = ttk.Radiobutton(
            solution_frame,
            text="临时禁用SSL验证（推荐，快速解决）",
            variable=self.solution_var,
            value="bypass"
        )
        bypass_radio.grid(row=0, column=0, sticky=tk.W, pady=(0, 5))
        
        bypass_desc = ttk.Label(
            solution_frame,
            text="  • 立即解决连接问题，适用于测试环境",
            foreground="gray"
        )
        bypass_desc.grid(row=1, column=0, sticky=tk.W, pady=(0, 10))
        
        # 更新证书选项
        update_radio = ttk.Radiobutton(
            solution_frame,
            text="更新SSL证书包（永久解决）",
            variable=self.solution_var,
            value="update"
        )
        update_radio.grid(row=2, column=0, sticky=tk.W, pady=(0, 5))
        
        update_desc = ttk.Label(
            solution_frame,
            text="  • 下载并安装最新的证书包，可能需要几分钟",
            foreground="gray"
        )
        update_desc.grid(row=3, column=0, sticky=tk.W, pady=(0, 10))
        
        # 手动处理选项
        manual_radio = ttk.Radiobutton(
            solution_frame,
            text="稍后手动处理",
            variable=self.solution_var,
            value="manual"
        )
        manual_radio.grid(row=4, column=0, sticky=tk.W, pady=(0, 5))
        
        manual_desc = ttk.Label(
            solution_frame,
            text="  • 关闭对话框，在设置中手动配置SSL选项",
            foreground="gray"
        )
        manual_desc.grid(row=5, column=0, sticky=tk.W, pady=(0, 10))
        
        # 按钮框架
        button_frame = ttk.Frame(main_frame)
        button_frame.grid(row=4, column=0, pady=(10, 0))
        
        # 应用解决方案按钮
        self.apply_button = ttk.Button(
            button_frame,
            text="应用解决方案",
            command=self._apply_solution
        )
        self.apply_button.pack(side=tk.LEFT, padx=(0, 10))
        
        # 打开SSL设置按钮
        self.settings_button = ttk.Button(
            button_frame,
            text="SSL设置",
            command=self._open_ssl_settings
        )
        self.settings_button.pack(side=tk.LEFT, padx=(0, 10))
        
        # 取消按钮
        self.cancel_button = ttk.Button(
            button_frame,
            text="取消",
            command=self._cancel_clicked
        )
        self.cancel_button.pack(side=tk.LEFT)
        
        # 配置网格权重
        main_frame.columnconfigure(0, weight=1)
        error_frame.columnconfigure(0, weight=1)
        solution_frame.columnconfigure(0, weight=1)
    
    def _display_error_info(self):
        """显示错误信息"""
        self.error_text.config(state=tk.NORMAL)
        self.error_text.delete(1.0, tk.END)
        self.error_text.insert(tk.END, self.error_message)
        self.error_text.config(state=tk.DISABLED)
    
    def _apply_solution(self):
        """应用选择的解决方案"""
        solution = self.solution_var.get()
        
        if solution == "bypass":
            self._apply_ssl_bypass()
        elif solution == "update":
            self._apply_certificate_update()
        elif solution == "manual":
            self._close_for_manual_handling()
    
    def _apply_ssl_bypass(self):
        """应用SSL绕过解决方案"""
        try:
            # 显示安全警告
            if messagebox.askyesno(
                "安全确认",
                "即将禁用SSL证书验证。\n\n"
                "这会降低连接安全性，但可以立即解决连接问题。\n"
                "建议仅在测试环境中使用。\n\n"
                "确定要继续吗？",
                icon="warning"
            ):
                # 禁用SSL验证
                self.config.disable_ssl_verification()
                
                self.solution_applied = True
                messagebox.showinfo(
                    "解决方案已应用",
                    "SSL验证已禁用，连接问题应该已解决。\n\n"
                    "您可以在设置中重新启用SSL验证。"
                )
                
                # 通知解决方案已应用
                if self.on_solution_applied:
                    self.on_solution_applied("ssl_bypass")
                
                self.destroy()
                
        except Exception as e:
            messagebox.showerror("错误", f"应用解决方案失败: {e}")
            self.logger.error(f"应用SSL绕过失败: {e}")
    
    def _apply_certificate_update(self):
        """应用证书更新解决方案"""
        self.apply_button.config(state=tk.DISABLED, text="正在更新...")
        
        def update_certificates():
            try:
                # 尝试自动修复
                fix_result = self.ssl_fixer.auto_fix_common_issues()
                
                # 在主线程中更新UI
                self.after(0, self._handle_update_result, fix_result)
                
            except Exception as e:
                self.after(0, self._handle_update_error, str(e))
        
        threading.Thread(target=update_certificates, daemon=True).start()
    
    def _handle_update_result(self, fix_result):
        """处理更新结果"""
        self.apply_button.config(state=tk.NORMAL, text="应用解决方案")
        
        if fix_result['success']:
            self.solution_applied = True
            messagebox.showinfo(
                "更新成功",
                "SSL证书包已更新，问题应该已解决。\n\n"
                "请重新尝试连接。"
            )
            
            # 通知解决方案已应用
            if self.on_solution_applied:
                self.on_solution_applied("certificate_update")
            
            self.destroy()
        else:
            error_msg = "证书更新未完全成功。\n\n"
            if fix_result['errors']:
                error_msg += "错误信息:\n"
                for error in fix_result['errors']:
                    error_msg += f"• {error}\n"
            error_msg += "\n建议尝试SSL绕过解决方案。"
            
            messagebox.showwarning("更新未完成", error_msg)
    
    def _handle_update_error(self, error_message):
        """处理更新错误"""
        self.apply_button.config(state=tk.NORMAL, text="应用解决方案")
        messagebox.showerror(
            "更新失败",
            f"证书更新失败: {error_message}\n\n"
            "建议尝试SSL绕过解决方案。"
        )
    
    def _close_for_manual_handling(self):
        """关闭对话框进行手动处理"""
        messagebox.showinfo(
            "手动处理",
            "您可以在主菜单的'设置'中找到'SSL设置'选项，\n"
            "手动配置SSL验证选项。"
        )
        self.destroy()
    
    def _open_ssl_settings(self):
        """打开SSL设置对话框"""
        try:
            from gui.ssl_settings_dialog import SSLSettingsDialog
            
            def on_settings_changed():
                self.solution_applied = True
                if self.on_solution_applied:
                    self.on_solution_applied("manual_settings")
            
            ssl_dialog = SSLSettingsDialog(self, self.config, on_settings_changed)
            ssl_dialog.wait_window()
            
            if self.solution_applied:
                self.destroy()
                
        except Exception as e:
            messagebox.showerror("错误", f"无法打开SSL设置: {e}")
            self.logger.error(f"打开SSL设置失败: {e}")
    
    def _cancel_clicked(self):
        """取消按钮点击"""
        self.destroy()
    
    def get_solution_applied(self) -> bool:
        """获取是否已应用解决方案"""
        return self.solution_applied
