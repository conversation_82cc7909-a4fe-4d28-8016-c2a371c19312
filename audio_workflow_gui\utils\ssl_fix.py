#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
SSL证书问题诊断和修复工具

解决SSL证书验证失败的问题。

作者：Augment Agent
版本：1.0.0
"""

import ssl
import socket
import requests
import urllib3
import certifi
import os
import sys
import platform
from typing import Dict, Any, Optional, Tuple
import logging


class SSLFixer:
    """SSL问题修复器"""
    
    def __init__(self):
        self.logger = logging.getLogger(__name__)
    
    def diagnose_ssl_issue(self, hostname: str = "www.gstudios.com.cn", port: int = 443) -> Dict[str, Any]:
        """诊断SSL问题"""
        result = {
            "hostname": hostname,
            "port": port,
            "ssl_support": False,
            "certificate_valid": False,
            "ca_bundle_path": None,
            "system_info": {},
            "errors": [],
            "suggestions": []
        }
        
        # 获取系统信息
        result["system_info"] = {
            "platform": platform.platform(),
            "python_version": sys.version,
            "ssl_version": ssl.OPENSSL_VERSION,
            "certifi_version": certifi.__version__ if hasattr(certifi, '__version__') else "unknown"
        }
        
        # 检查CA证书包路径
        try:
            result["ca_bundle_path"] = certifi.where()
            self.logger.debug(f"CA证书包路径: {result['ca_bundle_path']}")
        except Exception as e:
            result["errors"].append(f"无法获取CA证书包路径: {e}")
        
        # 测试SSL连接
        try:
            # 创建SSL上下文
            context = ssl.create_default_context()
            
            # 尝试连接
            with socket.create_connection((hostname, port), timeout=10) as sock:
                with context.wrap_socket(sock, server_hostname=hostname) as ssock:
                    result["ssl_support"] = True
                    result["certificate_valid"] = True
                    cert = ssock.getpeercert()
                    result["certificate_info"] = {
                        "subject": dict(x[0] for x in cert.get('subject', [])),
                        "issuer": dict(x[0] for x in cert.get('issuer', [])),
                        "version": cert.get('version'),
                        "serial_number": cert.get('serialNumber'),
                        "not_before": cert.get('notBefore'),
                        "not_after": cert.get('notAfter')
                    }
                    self.logger.info(f"SSL连接成功: {hostname}:{port}")
                    
        except ssl.SSLCertVerificationError as e:
            result["errors"].append(f"SSL证书验证失败: {e}")
            result["suggestions"].extend([
                "尝试更新系统CA证书",
                "检查系统时间是否正确",
                "考虑使用自定义CA证书包",
                "临时禁用SSL验证（仅用于测试）"
            ])
        except socket.timeout:
            result["errors"].append("连接超时")
            result["suggestions"].append("检查网络连接和防火墙设置")
        except Exception as e:
            result["errors"].append(f"SSL连接失败: {e}")
        
        return result
    
    def create_unverified_session(self) -> requests.Session:
        """创建不验证SSL的requests会话"""
        session = requests.Session()
        
        # 禁用SSL警告
        urllib3.disable_warnings(urllib3.exceptions.InsecureRequestWarning)
        
        # 设置不验证SSL
        session.verify = False
        
        # 设置适配器
        adapter = requests.adapters.HTTPAdapter(
            max_retries=urllib3.util.retry.Retry(
                total=3,
                backoff_factor=0.3,
                status_forcelist=[500, 502, 503, 504]
            )
        )
        session.mount('http://', adapter)
        session.mount('https://', adapter)
        
        return session
    
    def create_custom_ssl_context(self, verify_ssl: bool = True) -> Optional[ssl.SSLContext]:
        """创建自定义SSL上下文"""
        try:
            if not verify_ssl:
                # 创建不验证证书的上下文
                context = ssl.create_default_context()
                context.check_hostname = False
                context.verify_mode = ssl.CERT_NONE
                return context
            else:
                # 创建默认上下文
                context = ssl.create_default_context()
                
                # 尝试加载系统CA证书
                try:
                    ca_bundle = certifi.where()
                    context.load_verify_locations(ca_bundle)
                    self.logger.debug(f"已加载CA证书包: {ca_bundle}")
                except Exception as e:
                    self.logger.warning(f"无法加载CA证书包: {e}")
                
                return context
                
        except Exception as e:
            self.logger.error(f"创建SSL上下文失败: {e}")
            return None
    
    def test_api_connection(self, url: str, headers: Dict[str, str] = None, 
                          verify_ssl: bool = True, timeout: int = 30) -> Tuple[bool, str]:
        """测试API连接"""
        try:
            if verify_ssl:
                response = requests.get(url, headers=headers, timeout=timeout)
            else:
                # 禁用SSL警告
                urllib3.disable_warnings(urllib3.exceptions.InsecureRequestWarning)
                response = requests.get(url, headers=headers, verify=False, timeout=timeout)
            
            if response.status_code == 200:
                return True, f"连接成功，状态码: {response.status_code}"
            else:
                return False, f"连接失败，状态码: {response.status_code}"
                
        except requests.exceptions.SSLError as e:
            return False, f"SSL错误: {e}"
        except requests.exceptions.ConnectionError as e:
            return False, f"连接错误: {e}"
        except requests.exceptions.Timeout as e:
            return False, f"超时错误: {e}"
        except Exception as e:
            return False, f"未知错误: {e}"
    
    def suggest_fixes(self, diagnosis: Dict[str, Any]) -> list:
        """根据诊断结果建议修复方案"""
        suggestions = []
        
        if not diagnosis["certificate_valid"]:
            suggestions.extend([
                {
                    "title": "临时解决方案：禁用SSL验证",
                    "description": "在开发测试环境中临时禁用SSL证书验证",
                    "risk": "低安全性，仅适用于测试环境",
                    "action": "在API客户端中设置 verify_ssl=False"
                },
                {
                    "title": "更新CA证书包",
                    "description": "更新系统的根证书颁发机构证书",
                    "risk": "安全，推荐方案",
                    "action": "运行: pip install --upgrade certifi"
                },
                {
                    "title": "检查系统时间",
                    "description": "确保系统时间与实际时间同步",
                    "risk": "无风险",
                    "action": "同步系统时间"
                },
                {
                    "title": "配置代理设置",
                    "description": "如果在企业网络环境中，配置正确的代理设置",
                    "risk": "无风险",
                    "action": "设置HTTP_PROXY和HTTPS_PROXY环境变量"
                }
            ])
        
        return suggestions
    
    def auto_fix_common_issues(self) -> Dict[str, Any]:
        """自动修复常见SSL问题"""
        result = {
            "fixes_applied": [],
            "errors": [],
            "success": False
        }
        
        try:
            # 尝试更新certifi
            import subprocess
            
            self.logger.info("尝试更新certifi包...")
            process = subprocess.run([
                sys.executable, "-m", "pip", "install", "--upgrade", "certifi"
            ], capture_output=True, text=True, timeout=60)
            
            if process.returncode == 0:
                result["fixes_applied"].append("已更新certifi包")
                self.logger.info("certifi包更新成功")
            else:
                result["errors"].append(f"更新certifi失败: {process.stderr}")
                
        except subprocess.TimeoutExpired:
            result["errors"].append("更新certifi超时")
        except Exception as e:
            result["errors"].append(f"更新certifi异常: {e}")
        
        # 检查修复结果
        diagnosis = self.diagnose_ssl_issue()
        if diagnosis["certificate_valid"]:
            result["success"] = True
            result["fixes_applied"].append("SSL证书验证问题已解决")
        
        return result


def main():
    """主函数，用于命令行测试"""
    logging.basicConfig(level=logging.INFO)
    
    fixer = SSLFixer()
    
    print("SSL问题诊断工具")
    print("=" * 50)
    
    # 诊断问题
    diagnosis = fixer.diagnose_ssl_issue()
    
    print(f"目标主机: {diagnosis['hostname']}:{diagnosis['port']}")
    print(f"SSL支持: {'是' if diagnosis['ssl_support'] else '否'}")
    print(f"证书有效: {'是' if diagnosis['certificate_valid'] else '否'}")
    print(f"CA证书包: {diagnosis['ca_bundle_path']}")
    
    if diagnosis['errors']:
        print("\n错误信息:")
        for error in diagnosis['errors']:
            print(f"  • {error}")
    
    if diagnosis['suggestions']:
        print("\n建议:")
        for suggestion in diagnosis['suggestions']:
            print(f"  • {suggestion}")
    
    # 建议修复方案
    fixes = fixer.suggest_fixes(diagnosis)
    if fixes:
        print("\n修复方案:")
        for i, fix in enumerate(fixes, 1):
            print(f"  {i}. {fix['title']}")
            print(f"     描述: {fix['description']}")
            print(f"     风险: {fix['risk']}")
            print(f"     操作: {fix['action']}")
            print()


if __name__ == "__main__":
    main()
