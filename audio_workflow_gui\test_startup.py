#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
启动测试脚本

测试应用程序是否能正常启动和初始化。

作者：Augment Agent
版本：1.0.0
"""

import sys
import os
import traceback

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def test_imports():
    """测试所有模块导入"""
    print("测试模块导入...")
    
    try:
        from config.settings import AppConfig
        print("✓ 配置模块导入成功")
    except Exception as e:
        print(f"✗ 配置模块导入失败: {e}")
        return False
    
    try:
        from utils.logger import setup_logger
        print("✓ 日志模块导入成功")
    except Exception as e:
        print(f"✗ 日志模块导入失败: {e}")
        return False
    
    try:
        from api.client import GStudioAPIClient
        print("✓ API客户端模块导入成功")
    except Exception as e:
        print(f"✗ API客户端模块导入失败: {e}")
        return False
    
    try:
        from utils.file_manager import FileManager
        print("✓ 文件管理模块导入成功")
    except Exception as e:
        print(f"✗ 文件管理模块导入失败: {e}")
        return False
    
    return True

def test_config():
    """测试配置系统"""
    print("\n测试配置系统...")
    
    try:
        from config.settings import AppConfig
        config = AppConfig()
        
        # 测试基本配置获取
        api_config = config.get_api_config()
        if api_config:
            print("✓ API配置获取成功")
        else:
            print("✗ API配置获取失败")
            return False
        
        # 测试配置验证
        if config.validate_config():
            print("✓ 配置验证通过")
        else:
            print("✗ 配置验证失败")
            return False
        
        return True
        
    except Exception as e:
        print(f"✗ 配置系统测试失败: {e}")
        traceback.print_exc()
        return False

def test_api_client():
    """测试API客户端"""
    print("\n测试API客户端...")
    
    try:
        from api.client import GStudioAPIClient
        from api.models import APIVersion
        
        client = GStudioAPIClient()
        print("✓ API客户端创建成功")
        
        # 测试URL生成
        url = client._get_url("/test")
        if url:
            print(f"✓ URL生成成功: {url}")
        else:
            print("✗ URL生成失败")
            return False
        
        return True
        
    except Exception as e:
        print(f"✗ API客户端测试失败: {e}")
        traceback.print_exc()
        return False

def test_gui_imports():
    """测试GUI模块导入"""
    print("\n测试GUI模块导入...")
    
    try:
        import tkinter as tk
        print("✓ tkinter导入成功")
    except Exception as e:
        print(f"✗ tkinter导入失败: {e}")
        return False
    
    try:
        from gui.book_search import BookSearchFrame
        print("✓ 书籍搜索模块导入成功")
    except Exception as e:
        print(f"✗ 书籍搜索模块导入失败: {e}")
        return False
    
    try:
        from gui.chapter_manager import ChapterManagerFrame
        print("✓ 章节管理模块导入成功")
    except Exception as e:
        print(f"✗ 章节管理模块导入失败: {e}")
        return False
    
    try:
        from gui.paragraph_processor import ParagraphProcessorFrame
        print("✓ 段落处理模块导入成功")
    except Exception as e:
        print(f"✗ 段落处理模块导入失败: {e}")
        return False
    
    try:
        from gui.audio_generator import AudioGeneratorFrame
        print("✓ 音频生成模块导入成功")
    except Exception as e:
        print(f"✗ 音频生成模块导入失败: {e}")
        return False
    
    try:
        from gui.audio_uploader import AudioUploaderFrame
        print("✓ 音频上传模块导入成功")
    except Exception as e:
        print(f"✗ 音频上传模块导入失败: {e}")
        return False
    
    return True

def test_main_window():
    """测试主窗口创建"""
    print("\n测试主窗口创建...")
    
    try:
        import tkinter as tk
        from config.settings import AppConfig
        from gui.main_window import MainWindow
        
        # 创建根窗口（但不显示）
        root = tk.Tk()
        root.withdraw()  # 隐藏窗口
        
        config = AppConfig()
        main_window = MainWindow(root, config)
        
        print("✓ 主窗口创建成功")
        
        # 清理
        root.destroy()
        
        return True
        
    except Exception as e:
        print(f"✗ 主窗口创建失败: {e}")
        traceback.print_exc()
        return False

def main():
    """主测试函数"""
    print("GStudio 音频工作流 GUI 应用程序启动测试")
    print("=" * 50)
    
    tests = [
        ("模块导入", test_imports),
        ("配置系统", test_config),
        ("API客户端", test_api_client),
        ("GUI模块导入", test_gui_imports),
        ("主窗口创建", test_main_window)
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        print(f"\n{'='*20} {test_name} {'='*20}")
        if test_func():
            passed += 1
            print(f"✓ {test_name} 测试通过")
        else:
            print(f"✗ {test_name} 测试失败")
    
    print(f"\n{'='*50}")
    print(f"测试结果: {passed}/{total} 通过")
    
    if passed == total:
        print("✓ 所有测试通过，应用程序可以正常启动")
        return True
    else:
        print("✗ 部分测试失败，请检查错误信息")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
