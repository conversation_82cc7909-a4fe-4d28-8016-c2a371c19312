#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
GStudio API客户端

提供完整的HTTP客户端实现，封装所有API端点的请求方法。

作者：Augment Agent
版本：1.0.0
"""

import requests
import json
import logging
import time
import socket
import ssl
import traceback
import urllib3
from urllib.parse import urlparse
from typing import Dict, List, Optional, Any, Union
from pathlib import Path
from requests.adapters import HTTPAdapter
from urllib3.util.retry import Retry

from api.models import (
    APIEndpoints, APIVersion, APIResponse, DefaultHeaders,
    BookListEditorParams, BookListEditorResponse,
    ChapterListParams, ChapterRangeParams, TTSTrialParams,
    VoiceCountChapterCuesParams, VoiceListCueParams,
    ChapterCuesEditorParams, VoiceDownloadParams, VoiceUploadParams,
    CharacterListBookParams, CharacterListChapterParams, RecordTaskStateParams,
    ErrorCodes
)
from utils.ssl_fix import SSLFixer
from utils.retry import api_retry, RetryManager, ConnectionResetError as RetryConnectionResetError
from utils.logger import LoggerMixin


class APIClientError(Exception):
    """API客户端异常"""
    pass


# 使用从retry模块导入的ConnectionResetError
ConnectionResetError = RetryConnectionResetError


class RateLimiter:
    """请求频率限制器"""

    def __init__(self, min_interval: float = 0.1):
        """
        初始化频率限制器

        Args:
            min_interval: 最小请求间隔（秒）
        """
        self.min_interval = min_interval
        self.last_request_time = 0.0

    def wait_if_needed(self):
        """如果需要，等待到下次请求时间"""
        current_time = time.time()
        time_since_last = current_time - self.last_request_time

        if time_since_last < self.min_interval:
            wait_time = self.min_interval - time_since_last
            time.sleep(wait_time)

        self.last_request_time = time.time()


class ImprovedHTTPAdapter(HTTPAdapter):
    """改进的HTTP适配器，优化连接池配置"""

    def __init__(self, pool_connections=10, pool_maxsize=20, max_retries=3, pool_block=False, **kwargs):
        # 保存连接池参数
        self.pool_connections = pool_connections
        self.pool_maxsize = pool_maxsize
        self.max_retries = max_retries
        self.pool_block = pool_block

        # 调用父类初始化
        super().__init__(pool_connections=pool_connections, pool_maxsize=pool_maxsize, **kwargs)

    def init_poolmanager(self, *args, **kwargs):
        """初始化连接池管理器"""
        # 不要重复设置maxsize，因为父类已经处理了
        kwargs['block'] = self.pool_block

        # 添加重试配置
        if 'retries' not in kwargs:
            kwargs['retries'] = Retry(
                total=self.max_retries,
                backoff_factor=0.3,
                status_forcelist=[500, 502, 503, 504],
                allowed_methods=["HEAD", "GET", "OPTIONS", "POST"]
            )

        return super().init_poolmanager(*args, **kwargs)


class GStudioAPIClient(LoggerMixin):
    """GStudio API客户端"""
    
    def __init__(
        self,
        base_url: str = "https://www.gstudios.com.cn",
        version: APIVersion = APIVersion.V2,
        timeout: int = 30,
        max_retries: int = 3,
        retry_delay: float = 1.0,
        debug_mode: bool = False,
        verify_ssl: bool = True,
        request_interval: float = 0.1,
        pool_connections: int = 10,
        pool_maxsize: int = 20
    ):
        """
        初始化API客户端

        Args:
            base_url: API基础URL
            version: API版本
            timeout: 请求超时时间（秒）
            max_retries: 最大重试次数
            retry_delay: 重试延迟时间（秒）
            debug_mode: 是否启用调试模式
            verify_ssl: 是否验证SSL证书
            request_interval: 请求间隔时间（秒）
            pool_connections: 连接池连接数
            pool_maxsize: 连接池最大大小
        """
        self.base_url = base_url
        self.version = version
        self.timeout = timeout
        self.debug_mode = debug_mode
        self.verify_ssl = verify_ssl
        self.token: Optional[str] = None

        # 初始化SSL修复器
        self.ssl_fixer = SSLFixer()
        
        # 初始化重试管理器
        self.retry_manager = RetryManager(
            max_retries=max_retries,
            base_delay=retry_delay,
            logger=self.logger
        )

        # 初始化频率限制器
        self.rate_limiter = RateLimiter(min_interval=request_interval)

        # 创建会话
        self.session = requests.Session()
        self.session.timeout = timeout

        # 配置改进的HTTP适配器
        adapter = ImprovedHTTPAdapter(
            pool_connections=pool_connections,
            pool_maxsize=pool_maxsize,
            max_retries=max_retries
        )

        # 为HTTP和HTTPS挂载适配器
        self.session.mount('http://', adapter)
        self.session.mount('https://', adapter)

        # 配置SSL验证
        if not self.verify_ssl:
            # 禁用SSL警告
            urllib3.disable_warnings(urllib3.exceptions.InsecureRequestWarning)
            self.session.verify = False
            self.logger.warning("SSL证书验证已禁用，仅适用于测试环境")
        else:
            self.session.verify = True

        # 记录连接池配置
        if self.debug_mode:
            self.logger.debug(f"连接池配置: connections={pool_connections}, maxsize={pool_maxsize}")
            self.logger.debug(f"请求间隔: {request_interval}秒")
    
    def set_token(self, token: str) -> None:
        """设置认证令牌"""
        self.token = token
        if self.debug_mode and token:
            # 在调试模式下显示token的前几位字符
            masked_token = token[:8] + "..." + token[-4:] if len(token) > 12 else token[:4] + "..."
            self.logger.info(f"API认证令牌已设置: {masked_token}")
        else:
            self.logger.info("API认证令牌已设置")

    def diagnose_ssl_issues(self) -> Dict[str, Any]:
        """诊断SSL连接问题"""
        hostname = urlparse(self.base_url).hostname
        return self.ssl_fixer.diagnose_ssl_issue(hostname)

    def enable_ssl_bypass(self) -> None:
        """启用SSL绕过模式（仅用于测试）"""
        self.verify_ssl = False
        urllib3.disable_warnings(urllib3.exceptions.InsecureRequestWarning)
        self.session.verify = False
        self.logger.warning("已启用SSL绕过模式，仅适用于测试环境")

    def disable_ssl_bypass(self) -> None:
        """禁用SSL绕过模式"""
        self.verify_ssl = True
        self.session.verify = True
        self.logger.info("已禁用SSL绕过模式，恢复SSL证书验证")

    def _test_network_connectivity(self, url: str) -> Dict[str, Any]:
        """
        测试网络连接性

        Args:
            url: 要测试的URL

        Returns:
            Dict: 连接测试结果
        """
        result = {
            'dns_resolution': False,
            'tcp_connection': False,
            'ssl_verification': False,
            'ip_address': None,
            'error_details': []
        }

        try:
            parsed_url = urlparse(url)
            hostname = parsed_url.hostname
            port = parsed_url.port or (443 if parsed_url.scheme == 'https' else 80)

            # DNS解析测试
            try:
                ip_address = socket.gethostbyname(hostname)
                result['dns_resolution'] = True
                result['ip_address'] = ip_address
                self.logger.debug(f"DNS解析成功: {hostname} -> {ip_address}")
            except socket.gaierror as e:
                result['error_details'].append(f"DNS解析失败: {e}")
                self.logger.error(f"DNS解析失败: {hostname} - {e}")
                return result

            # TCP连接测试
            try:
                sock = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
                sock.settimeout(10)
                sock.connect((ip_address, port))
                result['tcp_connection'] = True
                self.logger.debug(f"TCP连接成功: {ip_address}:{port}")
                sock.close()
            except socket.error as e:
                result['error_details'].append(f"TCP连接失败: {e}")
                self.logger.error(f"TCP连接失败: {ip_address}:{port} - {e}")
                return result

            # SSL验证测试（仅HTTPS）
            if parsed_url.scheme == 'https':
                try:
                    context = ssl.create_default_context()
                    sock = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
                    sock.settimeout(10)
                    ssl_sock = context.wrap_socket(sock, server_hostname=hostname)
                    ssl_sock.connect((ip_address, port))
                    result['ssl_verification'] = True
                    self.logger.debug(f"SSL验证成功: {hostname}")
                    ssl_sock.close()
                except ssl.SSLError as e:
                    result['error_details'].append(f"SSL验证失败: {e}")
                    self.logger.error(f"SSL验证失败: {hostname} - {e}")
                except Exception as e:
                    result['error_details'].append(f"SSL连接失败: {e}")
                    self.logger.error(f"SSL连接失败: {hostname} - {e}")
            else:
                result['ssl_verification'] = True  # HTTP不需要SSL

        except Exception as e:
            result['error_details'].append(f"网络测试异常: {e}")
            self.logger.error(f"网络连接测试异常: {e}")

        return result
    
    def _get_headers(self, additional_headers: Optional[Dict[str, str]] = None) -> Dict[str, str]:
        """获取请求头"""
        headers = DefaultHeaders.get_headers(self.token)
        if additional_headers:
            headers.update(additional_headers)
        return headers
    
    def _get_url(self, path: str) -> str:
        """生成完整的API URL"""
        return APIEndpoints.get_url(path, self.version)
    
    @api_retry
    def _request(
        self,
        method: str,
        path: str,
        params: Optional[Dict] = None,
        data: Optional[Dict] = None,
        json_data: Optional[Dict] = None,
        files: Optional[Dict] = None,
        headers: Optional[Dict[str, str]] = None,
        return_binary: bool = False
    ) -> Union[APIResponse, bytes]:
        """
        发送HTTP请求

        Args:
            method: HTTP方法
            path: API路径
            params: URL参数
            data: 表单数据
            json_data: JSON数据
            files: 文件数据
            headers: 额外的请求头
            return_binary: 是否返回二进制数据

        Returns:
            API响应或二进制数据
        """
        url = self._get_url(path)
        request_headers = self._get_headers(headers)

        # 应用频率限制
        self.rate_limiter.wait_if_needed()

        # 记录请求开始时间
        start_time = time.time()

        # 详细的请求日志
        self.logger.info(f"发送 {method} 请求到 {url}")

        if self.debug_mode:
            self.logger.debug("=== HTTP请求详细信息 ===")
            self.logger.debug(f"URL: {url}")
            self.logger.debug(f"方法: {method}")
            self.logger.debug(f"超时设置: {self.timeout}秒")

            # 记录请求头（隐藏敏感信息）
            debug_headers = request_headers.copy()
            if 'authorization' in debug_headers:
                auth_header = debug_headers['authorization']
                if auth_header.startswith('Bearer '):
                    token = auth_header[7:]
                    masked_token = token[:8] + "..." + token[-4:] if len(token) > 12 else token[:4] + "..."
                    debug_headers['authorization'] = f"Bearer {masked_token}"

            self.logger.debug(f"请求头: {json.dumps(debug_headers, indent=2, ensure_ascii=False)}")

            if params:
                self.logger.debug(f"URL参数: {json.dumps(params, indent=2, ensure_ascii=False)}")

            if json_data:
                self.logger.debug(f"JSON数据: {json.dumps(json_data, indent=2, ensure_ascii=False)}")

            if data:
                self.logger.debug(f"表单数据: {json.dumps(data, indent=2, ensure_ascii=False)}")

            if files:
                file_info = {k: f"<文件: {v[0] if isinstance(v, tuple) else 'binary'}>" for k, v in files.items()}
                self.logger.debug(f"文件数据: {json.dumps(file_info, indent=2, ensure_ascii=False)}")

            # 网络连接诊断
            connectivity_result = self._test_network_connectivity(url)
            self.logger.debug(f"网络连接诊断: {json.dumps(connectivity_result, indent=2, ensure_ascii=False)}")

        try:
            response = self.session.request(
                method=method,
                url=url,
                params=params,
                data=data,
                json=json_data,
                files=files,
                headers=request_headers,
                timeout=self.timeout
            )

            # 记录响应时间
            response_time = time.time() - start_time

            # 详细的响应日志
            if self.debug_mode:
                self.logger.debug("=== HTTP响应详细信息 ===")
                self.logger.debug(f"状态码: {response.status_code}")
                self.logger.debug(f"响应时间: {response_time:.3f}秒")
                self.logger.debug(f"响应头: {json.dumps(dict(response.headers), indent=2, ensure_ascii=False)}")

                # 记录响应体（前200字符）
                if return_binary:
                    self.logger.debug(f"响应体: <二进制数据，大小: {len(response.content)} 字节>")
                else:
                    response_text = response.text[:200]
                    if len(response.text) > 200:
                        response_text += "..."
                    self.logger.debug(f"响应体: {response_text}")

            self.logger.info(f"请求完成: {method} {url} - {response.status_code} ({response_time:.3f}s)")

            # 检查HTTP状态码
            response.raise_for_status()

            # 返回二进制数据（如音频文件）
            if return_binary:
                return response.content

            # 解析JSON响应
            try:
                json_response = response.json()
                api_response = APIResponse(
                    code=json_response.get('code', 0),
                    msg=json_response.get('msg', ''),
                    data=json_response.get('data')
                )

                if self.debug_mode:
                    self.logger.debug(f"API响应码: {api_response.code}")
                    self.logger.debug(f"API响应消息: {api_response.msg}")

                # 检查业务状态码
                if api_response.code != ErrorCodes.SUCCESS:
                    raise APIClientError(f"API错误: {api_response.msg}")

                return api_response

            except json.JSONDecodeError as e:
                self.logger.error(f"JSON解析失败: {e}")
                if self.debug_mode:
                    self.logger.debug(f"原始响应内容: {response.text}")
                raise APIClientError(f"JSON解析失败: {e}")

        except requests.exceptions.SSLError as e:
            response_time = time.time() - start_time

            # SSL错误特殊处理
            self.logger.error(f"SSL连接失败: {e}")
            self.logger.error(f"请求失败: {method} {url} - 耗时: {response_time:.3f}秒")

            # 提供SSL问题解决建议
            if "CERTIFICATE_VERIFY_FAILED" in str(e):
                self.logger.error("SSL证书验证失败！")
                self.logger.error("解决方案:")
                self.logger.error("1. 临时解决：调用 client.enable_ssl_bypass() 禁用SSL验证（仅用于测试）")
                self.logger.error("2. 永久解决：运行 pip install --upgrade certifi 更新证书包")
                self.logger.error("3. 诊断问题：调用 client.diagnose_ssl_issues() 获取详细信息")

                if self.debug_mode:
                    # 自动诊断SSL问题
                    try:
                        diagnosis = self.diagnose_ssl_issues()
                        self.logger.debug("=== SSL问题诊断结果 ===")
                        self.logger.debug(f"SSL支持: {diagnosis.get('ssl_support', False)}")
                        self.logger.debug(f"证书有效: {diagnosis.get('certificate_valid', False)}")
                        self.logger.debug(f"CA证书包: {diagnosis.get('ca_bundle_path', 'unknown')}")
                        if diagnosis.get('errors'):
                            self.logger.debug(f"错误信息: {diagnosis['errors']}")
                        if diagnosis.get('suggestions'):
                            self.logger.debug(f"建议: {diagnosis['suggestions']}")
                    except Exception as diag_e:
                        self.logger.debug(f"SSL诊断失败: {diag_e}")

            raise APIClientError(f"SSL连接失败: {e}")

        except requests.exceptions.ConnectionError as e:
            response_time = time.time() - start_time

            # 检查是否是连接重置错误（Windows错误码10054）
            error_str = str(e).lower()
            if "10054" in error_str or "connection reset" in error_str or "远程主机强迫关闭" in error_str:
                self.logger.error(f"连接被远程服务器重置: {e}")
                self.logger.error(f"请求失败: {method} {url} - 耗时: {response_time:.3f}秒")

                if self.debug_mode:
                    self.logger.debug("=== 连接重置错误详细信息 ===")
                    self.logger.debug("可能的原因:")
                    self.logger.debug("1. 服务器端连接超时或负载过高")
                    self.logger.debug("2. 请求频率过快触发了防护机制")
                    self.logger.debug("3. 网络连接不稳定")
                    self.logger.debug("4. 服务器端连接池已满")

                    self.logger.debug("建议的解决方案:")
                    self.logger.debug("1. 增加请求间隔时间")
                    self.logger.debug("2. 减少并发请求数量")
                    self.logger.debug("3. 检查网络连接稳定性")
                    self.logger.debug("4. 联系服务器管理员检查服务器状态")

                raise ConnectionResetError(f"连接被远程服务器重置: {e}")
            else:
                # 其他连接错误
                self.logger.error(f"连接错误: {e}")
                self.logger.error(f"请求失败: {method} {url} - 耗时: {response_time:.3f}秒")
                raise APIClientError(f"连接错误: {e}")

        except requests.exceptions.RequestException as e:
            response_time = time.time() - start_time

            # 详细的错误日志
            self.logger.error(f"HTTP请求失败: {e}")
            self.logger.error(f"请求失败: {method} {url} - 耗时: {response_time:.3f}秒")

            if self.debug_mode:
                self.logger.debug("=== 错误详细信息 ===")
                self.logger.debug(f"异常类型: {type(e).__name__}")
                self.logger.debug(f"异常消息: {str(e)}")
                self.logger.debug(f"调用堆栈: {traceback.format_exc()}")

                # 记录当前配置
                config_info = {
                    'base_url': self.base_url,
                    'version': self.version.value,
                    'timeout': self.timeout,
                    'verify_ssl': self.verify_ssl,
                    'has_token': bool(self.token)
                }
                self.logger.debug(f"当前配置: {json.dumps(config_info, indent=2, ensure_ascii=False)}")

            raise APIClientError(f"HTTP请求失败: {e}")
    
    # 书籍相关API
    def get_book_list_editor(self, params: BookListEditorParams) -> APIResponse:
        """获取书籍列表（编辑器端点）"""
        # 转换为字典格式，正确处理各种数据类型
        request_params = {}

        # 必需参数
        request_params['pageSize'] = params.page_size
        request_params['pageNo'] = params.page_no

        # 可选参数 - 只有非None值才添加
        if params.name is not None:
            request_params['name'] = params.name

        if params.remark is not None:
            request_params['remark'] = params.remark

        if params.finished is not None:
            # 布尔值转换为字符串
            request_params['finished'] = str(params.finished).lower()

        if params.total is not None:
            request_params['total'] = params.total

        if params.sort_item is not None:
            request_params['sortItem'] = params.sort_item

        if params.sort_asc is not None:
            # 布尔值转换为整数：True -> 1, False -> 0
            request_params['sortAsc'] = 1 if params.sort_asc else 0

        if self.debug_mode:
            self.logger.debug(f"书籍列表编辑器请求参数: {request_params}")

        return self._request('GET', APIEndpoints.Book.LIST_EDITOR, params=request_params)

    def get_book_detail(self, book_id: str) -> APIResponse:
        """获取书籍详情"""
        path = APIEndpoints.Book.DETAIL.format(book_id=book_id)
        return self._request('GET', path)
    
    def get_book_partners(self, book_id: str, subject_type: str = "cv") -> APIResponse:
        """获取书籍合作成员列表"""
        params = {
            'bookId': book_id,
            'subjectType': subject_type
        }
        return self._request('GET', APIEndpoints.Book.PARTNER_LIST, params=params)
    
    # 章节相关API
    def get_chapter_list(self, params: ChapterListParams) -> APIResponse:
        """获取章节列表"""
        query_params = {
            'bookId': params.book_id,
            'pageSize': params.page_size,
            'pageNo': params.page_no
        }
        
        # 添加可选参数
        optional_params = [
            'cvHumanId', 'cvHumanRelationType', 'content', 'queryText',
            'editionState', 'executionState', 'auditionState',
            'characterId', 'sortItem', 'sortAsc'
        ]
        
        for param in optional_params:
            value = getattr(params, param.lower().replace('human', '_human'), None)
            if value is not None:
                query_params[param] = value
        
        return self._request('GET', APIEndpoints.Chapter.LIST, params=query_params)
    
    def get_chapter_range(self, params: ChapterRangeParams) -> APIResponse:
        """获取章节范围状态"""
        query_params = {
            'bookId': params.book_id,
            'chapterSeqNumBegin': params.seq_num_begin,
            'chapterSeqNumEnd': params.seq_num_end,
            'rangeMode': params.range_mode.value
        }
        return self._request('GET', APIEndpoints.Chapter.SEQ_RANGE, params=query_params)
    
    def get_chapter_cues(self, params: ChapterCuesEditorParams) -> APIResponse:
        """获取章节内容片列表"""
        query_params = {'chapterId': params.chapterId}
        return self._request('GET', APIEndpoints.Chapter.CUES_LIST_EDITOR, params=query_params)

    # 角色相关API
    def get_book_characters(self, params: CharacterListBookParams) -> APIResponse:
        """获取书籍角色列表"""
        query_params = {
            'bookId': params.book_id,
            'roleType': params.role_type,
            'skipStat': str(params.skip_stat).lower()
        }
        return self._request('GET', APIEndpoints.Character.LIST_BOOK, params=query_params)

    def get_chapter_characters(self, params: CharacterListChapterParams) -> APIResponse:
        """获取章节角色列表"""
        query_params = {
            'chapterId': params.chapter_id,
            'roleType': params.role_type,
            'skipStat': str(params.skip_stat).lower()
        }
        return self._request('GET', APIEndpoints.Character.LIST_CHAPTER, params=query_params)

    # 录音相关API
    def generate_tts_audio(self, params: TTSTrialParams) -> bytes:
        """生成TTS音频"""
        json_data = {
            'bookId': params.bookId,
            'cvRobotId': params.cvRobotId,
            'ssml': params.ssml,
            'genMode': params.genMode.value,
            'speedFactor': params.speedFactor,
            'durationFactorSilence': params.durationFactorSilence
        }

        if params.seed is not None:
            json_data['seed'] = params.seed

        return self._request(
            'POST',
            APIEndpoints.Record.TTS_TRIAL,
            json_data=json_data,
            return_binary=True
        )

    def get_chapter_cv_info(self, params: ChapterRangeParams) -> APIResponse:
        """获取章节CV任务信息"""
        json_data = {
            'bookId': params.book_id,
            'seqNumBegin': params.seq_num_begin,
            'seqNumEnd': params.seq_num_end,
            'rangeMode': params.range_mode.value
        }
        return self._request('POST', APIEndpoints.Record.CHAPTER_CV_INFO, json_data=json_data)

    def cancel_chapter_cv(self, params: ChapterRangeParams) -> APIResponse:
        """取消章节CV任务"""
        json_data = {
            'bookId': params.book_id,
            'seqNumBegin': params.seq_num_begin,
            'seqNumEnd': params.seq_num_end,
            'rangeMode': params.range_mode.value
        }
        return self._request('POST', APIEndpoints.Record.CHAPTER_CV_CANCEL, json_data=json_data)

    def get_chapter_record_task_state(self, params: RecordTaskStateParams) -> APIResponse:
        """获取章节片段录音任务状态列表"""
        query_params = {'chapterId': params.chapter_id}
        return self._request('GET', APIEndpoints.Record.TASK_STATE_CHAPTER_CUES, params=query_params)

    # 录音材料相关API
    def get_voice_count_chapter_cues(self, params: VoiceCountChapterCuesParams) -> APIResponse:
        """获取章节内容统计"""
        query_params = {'chapterId': params.chapterId}
        return self._request('GET', APIEndpoints.Material.VOICE_COUNT_CHAPTER_CUES, params=query_params)

    def get_voice_list_cue(self, params: VoiceListCueParams) -> APIResponse:
        """获取内容片录音列表"""
        query_params = {
            'cueId': params.cueId,
            'characterId': params.characterId
        }
        return self._request('GET', APIEndpoints.Material.VOICE_LIST_CUE, params=query_params)

    def download_voice(self, params: VoiceDownloadParams) -> bytes:
        """下载录音文件"""
        query_params = {'id': params.id}
        return self._request(
            'GET',
            APIEndpoints.Material.VOICE_DOWNLOAD,
            params=query_params,
            return_binary=True
        )

    def upload_voice(self, params: VoiceUploadParams) -> APIResponse:
        """上传音频文件"""
        # 准备文件数据
        if isinstance(params.file, (str, Path)):
            # 文件路径
            file_path = Path(params.file)
            if not file_path.exists():
                raise APIClientError(f"文件不存在: {file_path}")

            filename = params.filename or file_path.name
            with open(file_path, 'rb') as f:
                files = {
                    'file': (filename, f, 'audio/mpeg')
                }
                data = {'cueId': params.cueId}
                return self._request('POST', APIEndpoints.Material.VOICE_UPLOAD, data=data, files=files)
        else:
            # 文件对象
            filename = params.filename or 'audio.mp3'
            files = {
                'file': (filename, params.file, 'audio/mpeg')
            }
            data = {'cueId': params.cueId}
            return self._request('POST', APIEndpoints.Material.VOICE_UPLOAD, data=data, files=files)

    # 用户认证API
    def get_user_portal(self, code: str, state: str) -> APIResponse:
        """用户门户认证"""
        params = {
            'code': code,
            'state': state
        }
        return self._request('GET', APIEndpoints.Auth.PORTAL, params=params)

    def get_user_info(self) -> APIResponse:
        """获取用户信息"""
        return self._request('GET', APIEndpoints.Auth.USER_INFO)

    def refresh_token(self, refresh_token: str) -> APIResponse:
        """刷新令牌"""
        json_data = {'refreshToken': refresh_token}
        return self._request('POST', APIEndpoints.Auth.REFRESH_TOKEN, json_data=json_data)

    def test_api_connection(self) -> Dict[str, Any]:
        """
        测试API连接状态

        Returns:
            Dict: 连接测试结果
        """
        result = {
            'success': False,
            'network_connectivity': {},
            'api_endpoints': {},
            'error_details': [],
            'suggestions': []
        }

        try:
            # 测试网络连接
            base_url = f"{self.base_url}/story_v2/api/content/book/list"
            result['network_connectivity'] = self._test_network_connectivity(base_url)

            # 测试主要API端点
            test_endpoints = [
                ('/content/book/list', '书籍列表'),
                ('/content/chapter/list', '章节列表'),
                ('/user/portal', '用户门户'),
                ('/record/tts/trial', 'TTS试听')
            ]

            for path, name in test_endpoints:
                try:
                    url = self._get_url(path)
                    headers = self._get_headers()

                    # 使用HEAD请求测试端点
                    response = requests.head(url, headers=headers, timeout=10)

                    endpoint_result = {
                        'status_code': response.status_code,
                        'accessible': response.status_code in [200, 401, 403],
                        'needs_auth': response.status_code == 401,
                        'response_time': None
                    }

                    # 如果HEAD成功，测试GET请求的响应时间
                    if endpoint_result['accessible']:
                        start_time = time.time()
                        try:
                            get_response = requests.get(url, headers=headers, timeout=10)
                            endpoint_result['response_time'] = time.time() - start_time
                        except:
                            pass

                    result['api_endpoints'][name] = endpoint_result

                except Exception as e:
                    result['api_endpoints'][name] = {
                        'status_code': None,
                        'accessible': False,
                        'error': str(e)
                    }

            # 分析结果并生成建议
            network_ok = all([
                result['network_connectivity'].get('dns_resolution', False),
                result['network_connectivity'].get('tcp_connection', False),
                result['network_connectivity'].get('ssl_verification', False)
            ])

            api_endpoints_ok = any([
                ep.get('accessible', False) for ep in result['api_endpoints'].values()
            ])

            if network_ok and api_endpoints_ok:
                result['success'] = True
                if any([ep.get('needs_auth', False) for ep in result['api_endpoints'].values()]):
                    result['suggestions'].append("API端点正常，请设置有效的API Token")
                else:
                    result['suggestions'].append("API连接正常")
            else:
                if not network_ok:
                    result['suggestions'].extend([
                        "网络连接存在问题",
                        "请检查网络连接和防火墙设置",
                        "尝试使用VPN或更换网络环境"
                    ])

                if not api_endpoints_ok:
                    result['suggestions'].extend([
                        "API端点不可访问",
                        "请确认API服务器状态",
                        "检查API URL配置是否正确"
                    ])

        except Exception as e:
            result['error_details'].append(f"连接测试异常: {e}")
            result['suggestions'].append("请检查网络连接和配置")

        return result

    # 便捷方法
    def search_books(self, query: str = "", page_size: int = 50, page_no: int = 1) -> List[Dict[str, Any]]:
        """
        搜索书籍（使用编辑器端点）

        Args:
            query: 搜索关键词
            page_size: 每页大小
            page_no: 页码

        Returns:
            List[Dict]: 书籍列表
        """
        try:
            # 构建书籍列表编辑器查询参数
            params = BookListEditorParams(
                page_size=page_size,
                page_no=page_no,
                name=query if query else None
            )

            if self.debug_mode:
                self.logger.debug(f"书籍搜索参数: query='{query}', page_size={page_size}, page_no={page_no}")

            # 使用编辑器端点获取书籍列表
            response = self.get_book_list_editor(params)

            # 处理响应数据
            if response.data and isinstance(response.data, dict):
                book_list = response.data.get('list', [])
                total = response.data.get('total', 0)

                if self.debug_mode:
                    self.logger.debug(f"获取到 {len(book_list)} 本书籍，总计 {total} 本")
                    if book_list:
                        # 显示第一本书的结构示例
                        first_book = book_list[0]
                        self.logger.debug(f"书籍数据结构示例: {list(first_book.keys())}")

                return book_list
            elif response.data and isinstance(response.data, list):
                # 兼容旧格式
                if self.debug_mode:
                    self.logger.debug(f"获取到书籍列表（旧格式）: {len(response.data)} 本")
                return response.data
            else:
                if self.debug_mode:
                    self.logger.debug("未获取到书籍数据")
                return []

        except Exception as e:
            self.logger.error(f"搜索书籍失败: {e}")
            return []

    def get_all_chapters(self, book_id: str) -> List[Dict[str, Any]]:
        """获取书籍的所有章节"""
        try:
            all_chapters = []
            page_no = 1
            page_size = 50

            while True:
                params = ChapterListParams(
                    book_id=book_id,
                    page_size=page_size,
                    page_no=page_no
                )

                response = self.get_chapter_list(params)
                chapters = response.data.get('list', [])

                if not chapters:
                    break

                all_chapters.extend(chapters)

                # 检查是否还有更多页
                total = response.data.get('total', 0)
                if len(all_chapters) >= total:
                    break

                page_no += 1

            # 按序号排序
            all_chapters.sort(key=lambda x: x.get('seqNum', 0))
            return all_chapters

        except Exception as e:
            self.logger.error(f"获取章节列表失败: {e}")
            return []
