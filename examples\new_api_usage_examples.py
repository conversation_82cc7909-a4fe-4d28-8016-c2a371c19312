#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
新增API端点使用示例

演示如何使用基于API调用记录分析新增的角色和录音任务状态相关API
"""

import sys
import os
import json

# 添加项目路径
project_root = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
sys.path.insert(0, os.path.join(project_root, 'audio_workflow_gui'))

def example_get_book_characters():
    """示例：获取书籍角色列表"""
    print("示例：获取书籍角色列表")
    print("=" * 40)
    
    from api.client import GStudioAPIClient
    from api.models import CharacterListBookParams
    
    # 创建客户端
    client = GStudioAPIClient()
    
    # 设置认证token（实际使用时需要有效token）
    # client.set_token("your_token_here")
    
    try:
        # 创建请求参数
        params = CharacterListBookParams(
            book_id=12345,  # 书籍ID
            role_type="book",  # 角色类型
            skip_stat=True  # 跳过统计信息
        )
        
        print(f"请求参数:")
        print(f"  书籍ID: {params.book_id}")
        print(f"  角色类型: {params.role_type}")
        print(f"  跳过统计: {params.skip_stat}")
        
        # 发送请求
        response = client.get_book_characters(params)
        
        if response.success:
            characters = response.data if response.data else []
            print(f"\n成功获取 {len(characters)} 个角色:")
            
            for character in characters:
                print(f"  - {character['name']} (ID: {character['id']})")
                print(f"    性别: {character.get('gender', '未知')}")
                print(f"    CV: {character.get('cvHumanName', '未分配')}")
                print(f"    首次出现: {character.get('debutChapterName', '未知')}")
                print()
        else:
            print(f"请求失败: {response.error}")
            
    except Exception as e:
        print(f"发生异常: {e}")

def example_get_chapter_characters():
    """示例：获取章节角色列表"""
    print("示例：获取章节角色列表")
    print("=" * 40)
    
    from api.client import GStudioAPIClient
    from api.models import CharacterListChapterParams
    
    # 创建客户端
    client = GStudioAPIClient()
    
    try:
        # 创建请求参数
        params = CharacterListChapterParams(
            chapter_id=8607068,  # 章节ID
            role_type="chapter",  # 角色类型
            skip_stat=True  # 跳过统计信息
        )
        
        print(f"请求参数:")
        print(f"  章节ID: {params.chapter_id}")
        print(f"  角色类型: {params.role_type}")
        print(f"  跳过统计: {params.skip_stat}")
        
        # 发送请求
        response = client.get_chapter_characters(params)
        
        if response.success:
            characters = response.data if response.data else []
            print(f"\n章节包含 {len(characters)} 个角色:")
            
            for character in characters:
                print(f"  - {character['name']} (ID: {character['id']})")
                
                # 显示EQ配置（如果有）
                eq_config = character.get('eqConfig')
                if eq_config:
                    print(f"    EQ配置: 已设置")
                    for eq_type, config in eq_config.items():
                        if config.get('enabled'):
                            print(f"      {eq_type}: 频率={config['freq']}Hz, 增益={config['gain']}dB")
                else:
                    print(f"    EQ配置: 未设置")
                print()
        else:
            print(f"请求失败: {response.error}")
            
    except Exception as e:
        print(f"发生异常: {e}")

def example_get_record_task_state():
    """示例：获取录音任务状态"""
    print("示例：获取录音任务状态")
    print("=" * 40)
    
    from api.client import GStudioAPIClient
    from api.models import RecordTaskStateParams
    
    # 创建客户端
    client = GStudioAPIClient()
    
    try:
        # 创建请求参数
        params = RecordTaskStateParams(
            chapter_id=8607068  # 章节ID
        )
        
        print(f"请求参数:")
        print(f"  章节ID: {params.chapter_id}")
        
        # 发送请求
        response = client.get_chapter_record_task_state(params)
        
        if response.success:
            data = response.data
            human_tasks = data.get('infoHuman', [])
            robot_tasks = data.get('infoRobot', [])
            
            print(f"\n录音任务状态统计:")
            print(f"  人工录音任务: {len(human_tasks)} 个")
            print(f"  机器录音任务: {len(robot_tasks)} 个")
            
            # 统计完成状态
            finished_human = sum(1 for task in human_tasks if task['beenFinished'])
            finished_robot = sum(1 for task in robot_tasks if task['beenFinished'])
            
            print(f"\n完成状态:")
            print(f"  人工录音完成: {finished_human}/{len(human_tasks)}")
            print(f"  机器录音完成: {finished_robot}/{len(robot_tasks)}")
            
            # 显示详细任务信息
            if human_tasks:
                print(f"\n人工录音任务详情:")
                for i, task in enumerate(human_tasks[:5]):  # 只显示前5个
                    status = "已完成" if task['beenFinished'] else "未完成"
                    print(f"  {i+1}. 片段ID: {task['cueId']}, 角色ID: {task['characterId']}, 状态: {status}")
                    if task.get('cvId'):
                        print(f"      CV ID: {task['cvId']}")
                    if task.get('taskId'):
                        print(f"      任务ID: {task['taskId']}")
                
                if len(human_tasks) > 5:
                    print(f"  ... 还有 {len(human_tasks) - 5} 个任务")
            
            if robot_tasks:
                print(f"\n机器录音任务详情:")
                for i, task in enumerate(robot_tasks[:5]):  # 只显示前5个
                    status = "已完成" if task['beenFinished'] else "未完成"
                    print(f"  {i+1}. 片段ID: {task['cueId']}, 角色ID: {task['characterId']}, 状态: {status}")
                    if task.get('numCharCharged'):
                        print(f"      计费字符数: {task['numCharCharged']}")
                    if task.get('taskState'):
                        print(f"      任务状态: {task['taskState']}")
                
                if len(robot_tasks) > 5:
                    print(f"  ... 还有 {len(robot_tasks) - 5} 个任务")
        else:
            print(f"请求失败: {response.error}")
            
    except Exception as e:
        print(f"发生异常: {e}")

def example_comprehensive_workflow():
    """示例：综合工作流程"""
    print("示例：综合工作流程")
    print("=" * 40)
    
    from api.client import GStudioAPIClient
    from api.models import (
        CharacterListBookParams, 
        CharacterListChapterParams, 
        RecordTaskStateParams
    )
    
    # 创建客户端
    client = GStudioAPIClient()
    
    try:
        book_id = 12345
        chapter_id = 8607068
        
        print("步骤1: 获取书籍所有角色")
        print("-" * 30)
        
        # 获取书籍角色列表
        book_params = CharacterListBookParams(book_id=book_id)
        book_response = client.get_book_characters(book_params)
        
        if book_response.success:
            all_characters = book_response.data if book_response.data else []
            print(f"书籍总共有 {len(all_characters)} 个角色")
        else:
            print(f"获取书籍角色失败: {book_response.error}")
            return
        
        print("\n步骤2: 获取特定章节角色")
        print("-" * 30)
        
        # 获取章节角色列表
        chapter_params = CharacterListChapterParams(chapter_id=chapter_id)
        chapter_response = client.get_chapter_characters(chapter_params)
        
        if chapter_response.success:
            chapter_characters = chapter_response.data if chapter_response.data else []
            print(f"该章节包含 {len(chapter_characters)} 个角色")
            
            # 找出章节中的主要角色
            main_characters = [
                char for char in chapter_characters 
                if char.get('numCue', 0) > 10  # 台词超过10句的角色
            ]
            print(f"主要角色（台词>10句）: {len(main_characters)} 个")
        else:
            print(f"获取章节角色失败: {chapter_response.error}")
            return
        
        print("\n步骤3: 检查录音任务状态")
        print("-" * 30)
        
        # 获取录音任务状态
        task_params = RecordTaskStateParams(chapter_id=chapter_id)
        task_response = client.get_chapter_record_task_state(task_params)
        
        if task_response.success:
            data = task_response.data
            human_tasks = data.get('infoHuman', [])
            robot_tasks = data.get('infoRobot', [])
            
            # 分析录音进度
            total_tasks = len(human_tasks) + len(robot_tasks)
            finished_tasks = sum(1 for task in human_tasks + robot_tasks if task['beenFinished'])
            
            progress = (finished_tasks / total_tasks * 100) if total_tasks > 0 else 0
            
            print(f"录音进度: {finished_tasks}/{total_tasks} ({progress:.1f}%)")
            
            # 按角色统计录音状态
            character_stats = {}
            for task in human_tasks + robot_tasks:
                char_id = task['characterId']
                if char_id not in character_stats:
                    character_stats[char_id] = {'total': 0, 'finished': 0}
                character_stats[char_id]['total'] += 1
                if task['beenFinished']:
                    character_stats[char_id]['finished'] += 1
            
            print(f"\n各角色录音进度:")
            for char_id, stats in character_stats.items():
                # 找到角色名称
                char_name = "未知角色"
                for char in chapter_characters:
                    if char['id'] == char_id:
                        char_name = char['name']
                        break
                
                char_progress = (stats['finished'] / stats['total'] * 100) if stats['total'] > 0 else 0
                print(f"  {char_name}: {stats['finished']}/{stats['total']} ({char_progress:.1f}%)")
        else:
            print(f"获取录音任务状态失败: {task_response.error}")
        
        print("\n✓ 综合工作流程完成")
        
    except Exception as e:
        print(f"发生异常: {e}")

def main():
    """主函数"""
    print("GStudio 新增API端点使用示例")
    print("=" * 80)
    print("演示基于API调用记录分析新增的API端点使用方法")
    print()
    
    # 运行各个示例
    examples = [
        example_get_book_characters,
        example_get_chapter_characters,
        example_get_record_task_state,
        example_comprehensive_workflow
    ]
    
    for i, example_func in enumerate(examples, 1):
        print(f"\n{'='*80}")
        print(f"示例 {i}")
        print('='*80)
        
        try:
            example_func()
        except Exception as e:
            print(f"示例 {i} 执行失败: {e}")
    
    print(f"\n{'='*80}")
    print("使用说明")
    print('='*80)
    print("1. 这些示例展示了如何使用新增的API端点")
    print("2. 实际使用时需要设置有效的认证token")
    print("3. 请根据实际的书籍ID和章节ID调整参数")
    print("4. 建议在调用API前先验证参数的有效性")
    print("5. 注意处理API调用的错误和异常情况")

if __name__ == "__main__":
    main()
