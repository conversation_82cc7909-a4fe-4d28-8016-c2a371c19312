# 段落处理器角色筛选功能使用指南

## 功能概述

段落处理器现已集成角色筛选功能，允许用户根据特定角色筛选段落内容。该功能基于新实现的 `get_chapter_characters()` API，提供了直观的用户界面和强大的筛选能力。

## 功能特性

### 🎯 核心功能
- **自动角色加载**：章节切换时自动获取角色列表
- **实时筛选**：角色选择变化时立即应用筛选
- **智能匹配**：准确匹配角色ID和名称
- **多条件筛选**：支持角色、段落类型、CV类型组合筛选

### 🔧 用户体验
- **加载指示器**：显示角色加载状态
- **排序显示**：角色按名称字母顺序排列
- **错误处理**：友好的错误提示和重试机制
- **状态记忆**：在同一会话中记住筛选选择

## 界面说明

### 筛选配置区域

```
┌─────────────────────────────────────────────────────────────┐
│ 筛选配置                                                    │
├─────────────────────────────────────────────────────────────┤
│ 段落类型: [旁白    ] CV类型: [机器人 ▼] [应用筛选]          │
│ 角色筛选: [全部角色              ▼] [重新加载角色]          │
│           正在加载角色...                                   │
└─────────────────────────────────────────────────────────────┘
```

#### 组件说明

1. **角色筛选下拉框**
   - 标签：角色筛选
   - 默认值：全部角色
   - 选项格式：`角色名称 (ID: xxx)`
   - 状态：只读

2. **角色状态标签**
   - 显示角色加载状态
   - 成功：`已加载 X 个角色`
   - 失败：`加载失败`
   - 加载中：`正在加载角色...`
   - 无角色：`该章节暂无角色`

3. **重新加载角色按钮**
   - 手动重新加载角色列表
   - 网络错误时的重试选项

## 使用流程

### 1. 选择章节
```
1. 在章节管理器中选择目标章节
2. 系统自动加载该章节的角色列表
3. 角色筛选下拉框更新为可用角色
```

### 2. 加载段落
```
1. 点击"加载章节段落"按钮
2. 系统获取章节内容片数据
3. 自动匹配角色ID和角色名称
```

### 3. 应用筛选
```
1. 选择目标角色（或保持"全部角色"）
2. 设置其他筛选条件（段落类型、CV类型）
3. 系统自动应用筛选或点击"应用筛选"按钮
```

## 筛选逻辑

### 筛选条件组合

角色筛选与现有筛选条件采用 **AND** 逻辑组合：

```
筛选结果 = 段落类型匹配 AND CV类型匹配 AND 角色匹配
```

### 角色匹配规则

- **全部角色**：不限制角色，显示所有符合其他条件的段落
- **特定角色**：只显示该角色的段落

### 示例场景

#### 场景1：筛选旁白角色的机器人段落
```
段落类型: 旁白
CV类型: 机器人
角色筛选: 旁白 (ID: 1)
结果: 只显示旁白角色的机器人朗读段落
```

#### 场景2：筛选主角的人工录音段落
```
段落类型: 对话
CV类型: 人工
角色筛选: 主角 (ID: 2)
结果: 只显示主角的人工录音对话段落
```

## 错误处理

### 常见错误及解决方案

#### 1. 角色加载失败
**错误信息**：`加载失败`
**可能原因**：
- 网络连接问题
- API服务异常
- 章节ID无效

**解决方案**：
1. 检查网络连接
2. 点击"重新加载角色"按钮重试
3. 重新选择章节

#### 2. 角色列表为空
**显示信息**：`该章节暂无角色`
**可能原因**：
- 章节确实没有角色数据
- 角色数据尚未创建

**解决方案**：
1. 确认章节是否有内容
2. 检查章节配置是否正确

#### 3. 角色名称显示为ID
**显示格式**：`角色ID:123`
**可能原因**：
- 角色列表加载失败
- 段落中的角色ID在角色列表中不存在

**解决方案**：
1. 重新加载角色列表
2. 检查数据一致性

## 技术实现

### API集成

使用新实现的角色列表API：

```python
from api.models import CharacterListChapterParams

# 创建请求参数
params = CharacterListChapterParams(
    chapter_id=chapter_id,
    role_type="chapter",
    skip_stat=True
)

# 调用API
response = api_client.get_chapter_characters(params)
```

### 数据流程

```
1. 章节选择 → 触发角色加载
2. API调用 → 获取角色列表
3. UI更新 → 填充下拉框选项
4. 用户选择 → 触发筛选逻辑
5. 筛选应用 → 更新段落显示
```

### 关键方法

- `_load_chapter_characters()`: 加载章节角色列表
- `_update_character_combo()`: 更新角色下拉框
- `_on_character_changed()`: 处理角色选择变化
- `_get_selected_character_id()`: 获取选择的角色ID
- `_apply_filter()`: 应用包含角色筛选的综合筛选

## 最佳实践

### 1. 使用建议
- 先选择章节，等待角色加载完成
- 优先使用角色筛选缩小范围，再设置其他条件
- 网络不稳定时可手动重新加载角色

### 2. 性能优化
- 角色列表在章节切换时自动缓存
- 避免频繁切换角色选择
- 大量段落时建议先应用角色筛选

### 3. 故障排除
- 角色加载失败时检查网络连接
- 筛选结果为空时检查筛选条件组合
- 角色名称异常时重新加载角色列表

## 更新日志

### v1.0.0 (2025-01-24)
- ✅ 集成角色列表API
- ✅ 添加角色筛选下拉框
- ✅ 实现实时筛选功能
- ✅ 添加加载状态指示器
- ✅ 实现错误处理和重试机制
- ✅ 支持角色名称智能匹配
- ✅ 添加角色按名称排序
- ✅ 实现筛选条件记忆功能

## 技术支持

如果在使用过程中遇到问题，请：

1. 查看控制台日志获取详细错误信息
2. 检查网络连接和API服务状态
3. 尝试重新加载角色列表
4. 重新选择章节重置状态

## 相关文档

- [API调用记录分析报告](../API_ANALYSIS_REPORT.md)
- [新增API端点使用示例](../examples/new_api_usage_examples.py)
- [段落处理器用户手册](./PARAGRAPH_PROCESSOR_GUIDE.md)
