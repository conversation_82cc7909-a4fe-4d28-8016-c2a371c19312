
==================================================
=== 请求信息 ===
时间: 2025-04-08 13:43:22
方法: POST
URL: https://www.gstudios.com.cn/story/open/user/login
Headers: {
  "host": "www.gstudios.com.cn",
  "connection": "keep-alive",
  "sec-ch-ua-platform": "Windows",
  "x-requested-with": "XMLHttpRequest",
  "user-agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********",
  "accept": "application/json, text/plain, */*",
  "sec-ch-ua": "\"Microsoft Edge\";v=\"135\", \"Not-A.Brand\";v=\"8\", \"Chromium\";v=\"135\"",
  "sec-ch-ua-mobile": "?0",
  "sec-fetch-site": "same-origin",
  "sec-fetch-mode": "cors",
  "sec-fetch-dest": "empty",
  "accept-encoding": "gzip, deflate, br, zstd",
  "accept-language": "zh-CN,zh;q=0.9,en;q=0.8,en-GB;q=0.7,en-US;q=0.6",
  "authorization": "Bearer d22e0aabbc6441dea7c113d07d0a4ed9"
}
Cookie: collabOperation=false; isVipClubMember=true; wxToken=46385c92df3a44ef9cf3c784d8bdba2a
请求数据: {
  "id": 1214,
  "token": "46385c92df3a44ef9cf3c784d8bdba2a"
}
=== 响应信息 ===
状态码: 200
响应头: {'server': 'CLOUD ELB 1.0.0', 'date': 'Tue, 08 Apr 2025 05:43:20 GMT', 'content-type': 'application/json;charset=UTF-8', 'transfer-encoding': 'chunked', 'connection': 'close', 'x-powered-by': 'Express', 'vary': 'Accept-Encoding', 'content-encoding': 'gzip'}

=== 响应内容 ===
{
  "code": 1,
  "data": {
    "access_token": "d22e0aabbc6441dea7c113d07d0a4ed9",
    "expires_in": "",
    "refresh_token": "",
    "scope": "",
    "token_type": ""
  },
  "msg": "成功!"
}


==================================================
=== 请求信息 ===
时间: 2025-04-08 13:46:58
方法: GET
URL: https://www.gstudios.com.cn/story_v2/api/user/info
Headers: {
  "host": "www.gstudios.com.cn",
  "connection": "keep-alive",
  "sec-ch-ua-platform": "Windows",
  "x-requested-with": "XMLHttpRequest",
  "user-agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********",
  "accept": "application/json, text/plain, */*",
  "sec-ch-ua": "\"Microsoft Edge\";v=\"135\", \"Not-A.Brand\";v=\"8\", \"Chromium\";v=\"135\"",
  "sec-ch-ua-mobile": "?0",
  "sec-fetch-site": "same-origin",
  "sec-fetch-mode": "cors",
  "sec-fetch-dest": "empty",
  "accept-encoding": "gzip, deflate, br, zstd",
  "accept-language": "zh-CN,zh;q=0.9,en;q=0.8,en-GB;q=0.7,en-US;q=0.6",
  "authorization": "Bearer d22e0aabbc6441dea7c113d07d0a4ed9"
}
Cookie: collabOperation=false; isVipClubMember=true; wxToken=46385c92df3a44ef9cf3c784d8bdba2a; user_type=0; user_role=3; user_name=A%E9%A3%9E%E6%89%AC; access_token=d22e0aabbc6441dea7c113d07d0a4ed9; group_name=%E4%BA%9A%E8%A7%86%E6%9C%89%E5%A3%B0
=== 响应信息 ===
状态码: 200
响应头: {'server': 'CLOUD ELB 1.0.0', 'date': 'Tue, 08 Apr 2025 05:46:56 GMT', 'content-type': 'application/json;charset=UTF-8', 'transfer-encoding': 'chunked', 'connection': 'close', 'x-powered-by': 'Express', 'vary': 'Accept-Encoding', 'content-encoding': 'gzip'}

=== 响应内容 ===
{
  "code": 1,
  "data": {
    "avatar": "https://thirdwx.qlogo.cn/mmopen/vi_32/CSvV8hb6YlON9VYAYYdyicA1PibpClOzkpjSxuibvNB4O3uS6E9e6JPyyRNWEWyy3dq9oBqBDricpDgjDJhwLfK5ic8m62UKiar5vx8fJicXdDib1D4/132",
    "collabOperation": false,
    "groupCid": 67516,
    "groupName": "亚视有声",
    "isOperator": false,
    "isStandMaster": false,
    "isVipClubMember": true,
    "name": "A飞扬",
    "personalCid": null,
    "playerId": 1214,
    "role": 3,
    "type": 0
  },
  "msg": "成功!"
}

