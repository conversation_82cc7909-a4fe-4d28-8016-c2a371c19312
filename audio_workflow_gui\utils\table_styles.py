#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
表格样式工具模块

提供Tkinter Treeview表格的条纹化美化功能。

作者：Augment Agent
版本：1.0.0
"""

import tkinter as tk
from tkinter import ttk
from typing import Optional


class TableStyler:
    """表格样式管理器"""
    
    # 条纹化颜色配置
    STRIPE_COLORS = {
        'even': '#F0F8FF',  # 浅蓝色 - 偶数行
        'odd': '#FFFFFF',   # 白色 - 奇数行
        'selected': '#0078D4',  # 蓝色 - 选中行
        'selected_text': '#FFFFFF'  # 白色文字 - 选中行文字
    }
    
    @classmethod
    def apply_striped_style(cls, treeview: ttk.Treeview, style_name: Optional[str] = None) -> str:
        """
        为Treeview应用条纹化样式
        
        Args:
            treeview: 要应用样式的Treeview组件
            style_name: 自定义样式名称，如果为None则自动生成
            
        Returns:
            str: 应用的样式名称
        """
        if style_name is None:
            # 生成唯一的样式名称
            style_name = f"Striped.Treeview.{id(treeview)}"
        
        # 获取样式对象
        style = ttk.Style()
        
        # 配置条纹化样式
        style.configure(style_name, 
                       background=cls.STRIPE_COLORS['odd'],
                       foreground='black',
                       fieldbackground=cls.STRIPE_COLORS['odd'])
        
        # 配置选中状态样式
        style.map(style_name,
                 background=[('selected', cls.STRIPE_COLORS['selected'])],
                 foreground=[('selected', cls.STRIPE_COLORS['selected_text'])])
        
        # 应用样式到Treeview
        treeview.configure(style=style_name)
        
        return style_name
    
    @classmethod
    def update_row_colors(cls, treeview: ttk.Treeview):
        """
        更新表格行的条纹化颜色
        
        Args:
            treeview: 要更新的Treeview组件
        """
        try:
            # 获取所有行
            items = treeview.get_children()
            
            # 为每一行设置标签和颜色
            for index, item in enumerate(items):
                if index % 2 == 0:
                    # 偶数行 - 浅蓝色
                    treeview.set(item, '#0', '')  # 清除可能的旧标签
                    treeview.item(item, tags=('even_row',))
                else:
                    # 奇数行 - 白色
                    treeview.set(item, '#0', '')  # 清除可能的旧标签
                    treeview.item(item, tags=('odd_row',))
            
            # 配置标签样式
            treeview.tag_configure('even_row', background=cls.STRIPE_COLORS['even'])
            treeview.tag_configure('odd_row', background=cls.STRIPE_COLORS['odd'])
            
        except Exception as e:
            # 静默处理错误，避免影响主要功能
            print(f"更新表格条纹化颜色失败: {e}")
    
    @classmethod
    def setup_striped_treeview(cls, treeview: ttk.Treeview, auto_update: bool = True) -> str:
        """
        设置条纹化Treeview的完整配置
        
        Args:
            treeview: 要配置的Treeview组件
            auto_update: 是否自动绑定更新事件
            
        Returns:
            str: 应用的样式名称
        """
        # 应用基础样式
        style_name = cls.apply_striped_style(treeview)
        
        # 初始化行颜色
        cls.update_row_colors(treeview)
        
        if auto_update:
            # 绑定事件，在数据变化时自动更新条纹
            def on_data_change(*args):
                """数据变化时更新条纹"""
                treeview.after_idle(lambda: cls.update_row_colors(treeview))
            
            # 绑定可能的数据变化事件
            # 注意：这些事件可能不是所有Tkinter版本都支持
            try:
                treeview.bind('<<TreeviewSelect>>', on_data_change)
                treeview.bind('<Button-1>', on_data_change)
            except:
                pass
        
        return style_name
    
    @classmethod
    def refresh_stripes(cls, treeview: ttk.Treeview):
        """
        刷新表格的条纹化效果
        
        Args:
            treeview: 要刷新的Treeview组件
        """
        cls.update_row_colors(treeview)


def setup_striped_table(treeview: ttk.Treeview, auto_update: bool = True) -> str:
    """
    便捷函数：设置条纹化表格
    
    Args:
        treeview: 要设置的Treeview组件
        auto_update: 是否自动更新条纹
        
    Returns:
        str: 应用的样式名称
    """
    return TableStyler.setup_striped_treeview(treeview, auto_update)


def refresh_table_stripes(treeview: ttk.Treeview):
    """
    便捷函数：刷新表格条纹
    
    Args:
        treeview: 要刷新的Treeview组件
    """
    TableStyler.refresh_stripes(treeview)
