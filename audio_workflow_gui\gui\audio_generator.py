#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
音频生成模块

实现批量音频生成、本地下载存储和音频预览播放功能。

作者：Augment Agent
版本：1.0.0
"""

import tkinter as tk
from tkinter import ttk, messagebox, filedialog
import threading
import os
import subprocess
import platform
from typing import List, Dict, Any, Optional
from pathlib import Path

from utils.logger import LoggerMixin
from api.models import TTSTrialParams, GenMode


class AudioGeneratorFrame(ttk.Frame, LoggerMixin):
    """音频生成框架"""
    
    def __init__(self, parent, main_window):
        """
        初始化音频生成框架
        
        Args:
            parent: 父窗口
            main_window: 主窗口实例
        """
        super().__init__(parent)
        self.main_window = main_window
        self.filtered_paragraphs = []
        self.generated_audios = []
        self.generation_cancelled = False
        
        self._create_widgets()
        self._setup_layout()
        self._bind_events()
        
        self.logger.info("音频生成模块初始化完成")
    
    def _create_widgets(self):
        """创建界面组件"""
        # 移除段落信息区域（信息现在显示在工具栏中）
        
        # TTS配置区域
        config_frame = ttk.LabelFrame(self, text="TTS配置", padding=10)

        # 配置选项（单行水平布局）
        config_grid_frame = ttk.Frame(config_frame)

        # CV机器人ID
        ttk.Label(config_grid_frame, text="CV机器人ID:").pack(side=tk.LEFT, padx=(0, 5))
        self.cv_robot_id_var = tk.IntVar()
        self.cv_robot_id_entry = ttk.Entry(config_grid_frame, textvariable=self.cv_robot_id_var, width=12)
        self.cv_robot_id_entry.pack(side=tk.LEFT, padx=(0, 15))

        # 语速因子
        ttk.Label(config_grid_frame, text="语速因子:").pack(side=tk.LEFT, padx=(0, 5))
        self.speed_factor_var = tk.IntVar()
        self.speed_factor_entry = ttk.Entry(config_grid_frame, textvariable=self.speed_factor_var, width=12)
        self.speed_factor_entry.pack(side=tk.LEFT, padx=(0, 15))

        # 静音时长因子
        ttk.Label(config_grid_frame, text="静音时长因子:").pack(side=tk.LEFT, padx=(0, 5))
        self.silence_factor_var = tk.IntVar()
        self.silence_factor_entry = ttk.Entry(config_grid_frame, textvariable=self.silence_factor_var, width=12)
        self.silence_factor_entry.pack(side=tk.LEFT, padx=(0, 15))

        # 生成模式
        ttk.Label(config_grid_frame, text="生成模式:").pack(side=tk.LEFT, padx=(0, 5))
        self.gen_mode_var = tk.StringVar(value="默认")
        gen_mode_combo = ttk.Combobox(
            config_grid_frame,
            textvariable=self.gen_mode_var,
            values=["默认"],
            state="readonly",
            width=10
        )
        gen_mode_combo.pack(side=tk.LEFT, padx=(0, 10))
        
        # 批处理配置
        batch_frame = ttk.Frame(config_frame)
        
        ttk.Label(batch_frame, text="批处理大小:").pack(side=tk.LEFT, padx=(0, 5))
        self.batch_size_var = tk.IntVar()
        self.batch_size_entry = ttk.Entry(batch_frame, textvariable=self.batch_size_var, width=10)
        self.batch_size_entry.pack(side=tk.LEFT, padx=(0, 10))
        
        ttk.Label(batch_frame, text="并发数量:").pack(side=tk.LEFT, padx=(0, 5))
        self.concurrent_var = tk.IntVar(value=3)
        self.concurrent_entry = ttk.Entry(batch_frame, textvariable=self.concurrent_var, width=10)
        self.concurrent_entry.pack(side=tk.LEFT, padx=(0, 10))
        
        # 重置配置按钮
        self.reset_config_button = ttk.Button(batch_frame, text="重置配置", command=self._reset_config)
        self.reset_config_button.pack(side=tk.LEFT, padx=(10, 0))
        
        # 生成控制区域
        control_frame = ttk.LabelFrame(self, text="生成控制", padding=10)
        
        # 控制按钮
        control_buttons_frame = ttk.Frame(control_frame)
        
        self.start_generation_button = ttk.Button(
            control_buttons_frame,
            text="开始生成",
            command=self._start_generation,
            state=tk.DISABLED
        )
        self.start_generation_button.pack(side=tk.LEFT, padx=(0, 5))
        
        self.pause_generation_button = ttk.Button(
            control_buttons_frame,
            text="暂停生成",
            command=self._pause_generation,
            state=tk.DISABLED
        )
        self.pause_generation_button.pack(side=tk.LEFT, padx=(0, 5))
        
        self.cancel_generation_button = ttk.Button(
            control_buttons_frame,
            text="取消生成",
            command=self._cancel_generation,
            state=tk.DISABLED
        )
        self.cancel_generation_button.pack(side=tk.LEFT, padx=(0, 10))

        # 将操作按钮移动到取消生成按钮右侧
        self.play_audio_button = ttk.Button(
            control_buttons_frame,
            text="播放音频",
            command=self._play_selected_audio,
            state=tk.DISABLED
        )
        self.play_audio_button.pack(side=tk.LEFT, padx=(0, 5))

        self.open_folder_button = ttk.Button(
            control_buttons_frame,
            text="打开文件夹",
            command=self._open_audio_folder,
            state=tk.DISABLED
        )
        self.open_folder_button.pack(side=tk.LEFT, padx=(0, 5))

        self.export_list_button = ttk.Button(
            control_buttons_frame,
            text="导出列表",
            command=self._export_audio_list,
            state=tk.DISABLED
        )
        self.export_list_button.pack(side=tk.LEFT, padx=(0, 5))

        self.continue_button = ttk.Button(
            control_buttons_frame,
            text="继续下一步",
            command=self._continue_to_upload,
            state=tk.DISABLED
        )
        self.continue_button.pack(side=tk.LEFT, padx=(0, 5))
        
        # 进度显示
        progress_frame = ttk.Frame(control_frame)
        
        ttk.Label(progress_frame, text="生成进度:").pack(side=tk.LEFT, padx=(0, 5))
        self.generation_progress_var = tk.DoubleVar()
        self.generation_progress_bar = ttk.Progressbar(
            progress_frame,
            variable=self.generation_progress_var,
            maximum=100,
            length=300
        )
        self.generation_progress_bar.pack(side=tk.LEFT, padx=(0, 5))
        
        self.generation_status_label = ttk.Label(progress_frame, text="就绪")
        self.generation_status_label.pack(side=tk.LEFT, padx=(5, 0))
        
        # 生成结果区域
        results_frame = ttk.LabelFrame(self, text="生成结果", padding=10)
        
        # 创建Treeview显示生成结果（移除段落ID列）
        columns = ("seq_num", "text_preview", "status", "file_path", "file_size")
        self.results_tree = ttk.Treeview(results_frame, columns=columns, show="headings", height=10)

        # 设置列标题
        self.results_tree.heading("seq_num", text="序号")
        self.results_tree.heading("text_preview", text="内容预览")
        self.results_tree.heading("status", text="状态")
        self.results_tree.heading("file_path", text="文件路径")
        self.results_tree.heading("file_size", text="文件大小")

        # 设置列宽（重新调整以充分利用界面空间）
        self.results_tree.column("seq_num", width=80)  # 增加序号列宽
        self.results_tree.column("text_preview", width=250)  # 增加内容预览列宽
        self.results_tree.column("status", width=100)  # 增加状态列宽
        self.results_tree.column("file_path", width=300)  # 增加文件路径列宽
        self.results_tree.column("file_size", width=100)  # 增加文件大小列宽
        
        # 添加滚动条
        self.results_scrollbar_y = ttk.Scrollbar(results_frame, orient=tk.VERTICAL, command=self.results_tree.yview)
        self.results_scrollbar_x = ttk.Scrollbar(results_frame, orient=tk.HORIZONTAL, command=self.results_tree.xview)
        self.results_tree.configure(yscrollcommand=self.results_scrollbar_y.set, xscrollcommand=self.results_scrollbar_x.set)
        
        # 操作按钮区域（按钮已移动到控制按钮区域）
        operations_frame = ttk.Frame(self)
        
        # 保存组件引用
        self.config_frame = config_frame
        self.config_grid_frame = config_grid_frame
        self.batch_frame = batch_frame
        self.control_frame = control_frame
        self.control_buttons_frame = control_buttons_frame
        self.progress_frame = progress_frame
        self.results_frame = results_frame
        self.operations_frame = operations_frame
    
    def _setup_layout(self):
        """设置布局"""
        self.config_frame.pack(fill=tk.X, pady=(0, 10))
        self.config_grid_frame.pack(fill=tk.X, pady=(0, 5))
        self.batch_frame.pack(fill=tk.X)
        
        self.control_frame.pack(fill=tk.X, pady=(0, 10))
        self.control_buttons_frame.pack(fill=tk.X, pady=(0, 5))
        self.progress_frame.pack(fill=tk.X)
        
        self.results_frame.pack(fill=tk.BOTH, expand=True, pady=(0, 10))
        
        # 结果树形视图布局
        self.results_tree.grid(row=0, column=0, sticky="nsew")
        self.results_scrollbar_y.grid(row=0, column=1, sticky="ns")
        self.results_scrollbar_x.grid(row=1, column=0, sticky="ew")
        
        self.results_frame.grid_rowconfigure(0, weight=1)
        self.results_frame.grid_columnconfigure(0, weight=1)
    
    def _bind_events(self):
        """绑定事件"""
        # 标签页激活时更新段落信息
        self.bind("<Visibility>", self._on_visibility_changed)
        
        # 结果树选择事件
        self.results_tree.bind("<<TreeviewSelect>>", self._on_result_select)
        
        # 双击播放音频
        self.results_tree.bind("<Double-1>", lambda e: self._play_selected_audio())
    
    def _on_visibility_changed(self, event):
        """标签页可见性改变事件"""
        if event.widget == self:
            self._update_paragraphs_info()
            self._load_default_config()
            # 更新角色配置
            self.update_character_config()
    
    def _on_result_select(self, event):
        """结果选择事件处理"""
        selection = self.results_tree.selection()
        if selection:
            self.play_audio_button.config(state=tk.NORMAL)
        else:
            self.play_audio_button.config(state=tk.DISABLED)
    
    def _update_paragraphs_info(self):
        """更新段落信息显示"""
        workflow_state = self.main_window.get_workflow_state()
        self.filtered_paragraphs = workflow_state.get('filtered_paragraphs', [])

        if self.filtered_paragraphs:
            # 启用开始生成按钮
            self.start_generation_button.config(state=tk.NORMAL)
        else:
            # 禁用开始生成按钮
            self.start_generation_button.config(state=tk.DISABLED)
    
    def _load_default_config(self):
        """加载默认配置"""
        audio_config = self.main_window.config.get_audio_config()
        filter_config = self.main_window.config.get_filter_config()

        # 尝试从段落处理器获取选中角色的cvRobotId
        selected_character = self.main_window.get_selected_character_info()
        if selected_character and selected_character.get('cvRobotId'):
            cv_robot_id = selected_character.get('cvRobotId')
            self.logger.info(f"使用选中角色的CV机器人ID: {cv_robot_id} (角色: {selected_character.get('name', '未知')})")
        else:
            cv_robot_id = audio_config.get('default_cv_robot_id', 568)
            self.logger.info(f"使用默认CV机器人ID: {cv_robot_id}")

        self.cv_robot_id_var.set(cv_robot_id)
        self.speed_factor_var.set(audio_config.get('default_speed_factor', 100))
        self.silence_factor_var.set(audio_config.get('default_silence_factor', 100))
        self.batch_size_var.set(filter_config.get('batch_size', 10))

    def update_character_config(self):
        """更新角色配置（当角色选择变化时调用）"""
        try:
            # 获取当前选中的角色信息
            selected_character = self.main_window.get_selected_character_info()

            if selected_character and selected_character.get('cvRobotId'):
                cv_robot_id = selected_character.get('cvRobotId')
                character_name = selected_character.get('name', '未知角色')

                # 更新CV机器人ID
                self.cv_robot_id_var.set(cv_robot_id)

                # 更新状态显示
                self.main_window.update_status(f"已更新TTS配置：使用角色 '{character_name}' 的CV机器人ID ({cv_robot_id})")
                self.logger.info(f"角色配置已更新: {character_name} -> CV机器人ID: {cv_robot_id}")
            else:
                # 使用默认配置
                audio_config = self.main_window.config.get_audio_config()
                default_cv_robot_id = audio_config.get('default_cv_robot_id', 568)
                self.cv_robot_id_var.set(default_cv_robot_id)

                self.main_window.update_status(f"使用默认TTS配置：CV机器人ID ({default_cv_robot_id})")
                self.logger.info(f"使用默认CV机器人ID: {default_cv_robot_id}")

        except Exception as e:
            self.logger.error(f"更新角色配置失败: {e}")

    def _reset_config(self):
        """重置配置"""
        self._load_default_config()
        messagebox.showinfo("提示", "配置已重置为默认值")
    
    def _start_generation(self):
        """开始生成音频"""
        if not self.filtered_paragraphs:
            messagebox.showwarning("提示", "没有待处理的段落")
            return
        
        # 验证配置
        if not self._validate_config():
            return
        
        # 重置状态
        self.generation_cancelled = False
        self.generated_audios = []
        
        # 清空结果列表
        for item in self.results_tree.get_children():
            self.results_tree.delete(item)
        
        # 更新按钮状态
        self.start_generation_button.config(state=tk.DISABLED)
        self.pause_generation_button.config(state=tk.NORMAL)
        self.cancel_generation_button.config(state=tk.NORMAL)
        
        # 开始生成
        self.main_window.update_status("开始生成音频...")
        
        def generation_thread():
            try:
                self._generate_audio_batch()
            except Exception as e:
                self.logger.error(f"音频生成失败: {e}")
                self.after(0, lambda: self._handle_generation_error(str(e)))
        
        threading.Thread(target=generation_thread, daemon=True).start()
    
    def _validate_config(self) -> bool:
        """验证配置"""
        try:
            cv_robot_id = self.cv_robot_id_var.get()
            speed_factor = self.speed_factor_var.get()
            silence_factor = self.silence_factor_var.get()
            batch_size = self.batch_size_var.get()
            concurrent = self.concurrent_var.get()
            
            if cv_robot_id <= 0:
                messagebox.showerror("配置错误", "CV机器人ID必须大于0")
                return False
            
            if not (50 <= speed_factor <= 200):
                messagebox.showerror("配置错误", "语速因子应在50-200之间")
                return False
            
            if not (50 <= silence_factor <= 200):
                messagebox.showerror("配置错误", "静音时长因子应在50-200之间")
                return False
            
            if not (1 <= batch_size <= 50):
                messagebox.showerror("配置错误", "批处理大小应在1-50之间")
                return False
            
            if not (1 <= concurrent <= 10):
                messagebox.showerror("配置错误", "并发数量应在1-10之间")
                return False
            
            return True
            
        except tk.TclError:
            messagebox.showerror("配置错误", "请输入有效的数值")
            return False

    def _generate_audio_batch(self):
        """批量生成音频"""
        api_client = self._get_api_client()
        if not api_client:
            return

        workflow_state = self.main_window.get_workflow_state()
        selected_book = workflow_state.get('selected_book')
        book_id = selected_book.get('bookId') if selected_book else ""

        total_count = len(self.filtered_paragraphs)
        completed_count = 0

        for i, paragraph in enumerate(self.filtered_paragraphs):
            if self.generation_cancelled:
                break

            try:
                # 更新进度
                progress = (i / total_count) * 100
                self.after(0, lambda p=progress, idx=i+1, total=total_count:
                          self._update_generation_progress(p, f"正在生成 {idx}/{total}"))

                # 获取音频生成的文本内容，优先使用SSML字段
                audio_content = self._get_audio_content(paragraph)

                # 生成TTS参数
                tts_params = TTSTrialParams(
                    bookId=book_id,
                    cvRobotId=self.cv_robot_id_var.get(),
                    ssml=audio_content,
                    genMode=GenMode.DEFAULT,
                    speedFactor=self.speed_factor_var.get(),
                    durationFactorSilence=self.silence_factor_var.get()
                )

                # 调用API生成音频
                audio_data = api_client.generate_tts_audio(tts_params)

                # 保存音频文件
                filename = f"cue_{paragraph['cue_id']}.mp3"
                file_path = self.main_window.file_manager.save_audio_file(
                    content=audio_data,
                    filename=filename,
                    category="generated",
                    book_id=book_id,
                    chapter_id=str(workflow_state.get('selected_chapter', {}).get('chapterId', '')),
                    cue_id=str(paragraph['cue_id'])
                )

                # 记录生成结果
                result = {
                    'paragraph': paragraph,
                    'file_path': file_path,
                    'file_size': len(audio_data),
                    'status': '成功'
                }
                self.generated_audios.append(result)

                # 更新UI
                self.after(0, lambda r=result: self._add_generation_result(r))

                completed_count += 1

            except Exception as e:
                self.logger.error(f"生成音频失败 (段落ID: {paragraph['cue_id']}): {e}")

                # 记录失败结果
                result = {
                    'paragraph': paragraph,
                    'file_path': '',
                    'file_size': 0,
                    'status': f'失败: {str(e)}'
                }
                self.generated_audios.append(result)

                # 更新UI
                self.after(0, lambda r=result: self._add_generation_result(r))

        # 完成生成
        final_progress = 100 if not self.generation_cancelled else (completed_count / total_count) * 100
        status_text = "生成完成" if not self.generation_cancelled else "生成已取消"

        self.after(0, lambda: self._finish_generation(final_progress, status_text, completed_count, total_count))

    def _get_audio_content(self, paragraph: Dict[str, Any]) -> str:
        """获取音频生成的文本内容，优先使用SSML字段"""
        try:
            # 首先尝试从原始数据中获取SSML字段
            raw_data = paragraph.get('raw_data', {})
            if raw_data:
                # 优先使用SSML字段
                ssml_content = raw_data.get('ssml', '').strip()
                if ssml_content:
                    self.logger.debug(f"使用SSML字段内容 (段落ID: {paragraph.get('cue_id', 'unknown')})")
                    return ssml_content

                # 如果SSML字段不存在或为空，使用text字段作为备选
                text_content = raw_data.get('text', '').strip()
                if text_content:
                    self.logger.debug(f"SSML字段为空，使用text字段内容 (段落ID: {paragraph.get('cue_id', 'unknown')})")
                    return text_content

            # 如果原始数据不可用，从处理后的段落数据中获取
            processed_text = paragraph.get('text', '').strip()
            if processed_text:
                self.logger.debug(f"使用处理后的text字段内容 (段落ID: {paragraph.get('cue_id', 'unknown')})")
                return processed_text

            # 如果所有字段都为空，记录警告并返回空字符串
            self.logger.warning(f"段落ID {paragraph.get('cue_id', 'unknown')} 没有可用的文本内容")
            return ""

        except Exception as e:
            self.logger.error(f"获取音频内容失败 (段落ID: {paragraph.get('cue_id', 'unknown')}): {e}")
            # 发生异常时，尝试返回基本的text字段
            return paragraph.get('text', '')

    def _update_generation_progress(self, progress: float, status: str):
        """更新生成进度"""
        self.generation_progress_var.set(progress)
        self.generation_status_label.config(text=status)
        self.main_window.update_progress(progress, status)

    def _add_generation_result(self, result: Dict[str, Any]):
        """添加生成结果到列表"""
        paragraph = result['paragraph']
        file_path = result['file_path']
        file_size = result['file_size']
        status = result['status']

        # 格式化文件大小
        if file_size > 0:
            if file_size < 1024:
                size_text = f"{file_size}B"
            elif file_size < 1024 * 1024:
                size_text = f"{file_size/1024:.1f}KB"
            else:
                size_text = f"{file_size/(1024*1024):.1f}MB"
        else:
            size_text = "-"

        # 添加到树形视图（移除段落ID列）
        self.results_tree.insert("", tk.END, values=(
            paragraph['seq_num'],
            paragraph['text_preview'],
            status,
            str(file_path) if file_path else "-",
            size_text
        ))

    def _finish_generation(self, progress: float, status: str, completed: int, total: int):
        """完成生成"""
        self._update_generation_progress(progress, status)

        # 更新按钮状态
        self.start_generation_button.config(state=tk.NORMAL)
        self.pause_generation_button.config(state=tk.DISABLED)
        self.cancel_generation_button.config(state=tk.DISABLED)

        # 启用操作按钮
        if self.generated_audios:
            self.open_folder_button.config(state=tk.NORMAL)
            self.export_list_button.config(state=tk.NORMAL)

            # 如果有成功生成的音频，启用继续按钮
            success_count = sum(1 for r in self.generated_audios if r['status'] == '成功')
            if success_count > 0:
                self.continue_button.config(state=tk.NORMAL)

        # 更新工作流状态
        self.main_window.update_workflow_state('generated_audios', self.generated_audios)

        # 显示完成消息
        success_count = sum(1 for r in self.generated_audios if r['status'] == '成功')
        message = f"音频生成完成!\n成功: {success_count}/{total}\n失败: {total - success_count}"

        if success_count < total:
            messagebox.showwarning("生成完成", message)
        else:
            messagebox.showinfo("生成完成", message)

        self.main_window.update_status(f"音频生成完成: {success_count}/{total}")
        self.logger.info(f"音频生成完成: 成功 {success_count}, 失败 {total - success_count}")

    def _pause_generation(self):
        """暂停生成"""
        # 这里可以实现暂停逻辑
        messagebox.showinfo("提示", "暂停功能待实现")

    def _cancel_generation(self):
        """取消生成"""
        if messagebox.askyesno("确认", "确定要取消音频生成吗？"):
            self.generation_cancelled = True
            self.logger.info("用户取消了音频生成")

    def _play_selected_audio(self):
        """播放选中的音频"""
        selection = self.results_tree.selection()
        if not selection:
            messagebox.showwarning("提示", "请先选择一个音频文件")
            return

        item = self.results_tree.item(selection[0])
        file_path = item['values'][4]

        if file_path == "-" or not file_path:
            messagebox.showwarning("提示", "该音频文件不存在")
            return

        try:
            # 使用系统默认程序播放音频
            if platform.system() == "Windows":
                os.startfile(file_path)
            elif platform.system() == "Darwin":  # macOS
                subprocess.run(["open", file_path])
            else:  # Linux
                subprocess.run(["xdg-open", file_path])

        except Exception as e:
            messagebox.showerror("错误", f"播放音频失败: {e}")

    def _open_audio_folder(self):
        """打开音频文件夹"""
        try:
            audio_dir = self.main_window.file_manager.generated_dir

            if platform.system() == "Windows":
                os.startfile(audio_dir)
            elif platform.system() == "Darwin":  # macOS
                subprocess.run(["open", audio_dir])
            else:  # Linux
                subprocess.run(["xdg-open", audio_dir])

        except Exception as e:
            messagebox.showerror("错误", f"打开文件夹失败: {e}")

    def _export_audio_list(self):
        """导出音频列表"""
        if not self.generated_audios:
            messagebox.showwarning("提示", "没有生成结果可导出")
            return

        filename = filedialog.asksaveasfilename(
            title="导出音频列表",
            defaultextension=".csv",
            filetypes=[("CSV文件", "*.csv"), ("文本文件", "*.txt"), ("所有文件", "*.*")]
        )

        if filename:
            try:
                import csv

                with open(filename, 'w', newline='', encoding='utf-8') as f:
                    writer = csv.writer(f)

                    # 写入标题行
                    writer.writerow([
                        "段落ID", "序号", "内容预览", "状态", "文件路径", "文件大小"
                    ])

                    # 写入数据行
                    for result in self.generated_audios:
                        paragraph = result['paragraph']
                        writer.writerow([
                            paragraph['cue_id'],
                            paragraph['seq_num'],
                            paragraph['text_preview'],
                            result['status'],
                            str(result['file_path']) if result['file_path'] else "-",
                            result['file_size']
                        ])

                messagebox.showinfo("成功", f"音频列表已导出到: {filename}")

            except Exception as e:
                messagebox.showerror("错误", f"导出失败: {e}")

    def _continue_to_upload(self):
        """继续到上传步骤"""
        if not self.generated_audios:
            messagebox.showwarning("提示", "没有生成的音频文件")
            return

        # 检查是否有成功生成的音频
        success_audios = [r for r in self.generated_audios if r['status'] == '成功']
        if not success_audios:
            messagebox.showwarning("提示", "没有成功生成的音频文件")
            return

        # 切换到下一步
        self.main_window.next_step()
        self.logger.info(f"进入音频上传步骤，共 {len(success_audios)} 个音频文件")

    def _handle_generation_error(self, error_message: str):
        """处理生成错误"""
        messagebox.showerror("生成失败", f"音频生成时发生错误:\n{error_message}")
        self.main_window.update_status("生成失败")

        # 恢复按钮状态
        self.start_generation_button.config(state=tk.NORMAL)
        self.pause_generation_button.config(state=tk.DISABLED)
        self.cancel_generation_button.config(state=tk.DISABLED)

    def _get_api_client(self):
        """获取API客户端"""
        # 从书籍搜索模块获取API客户端
        book_search_frame = self.main_window.book_search_frame
        if hasattr(book_search_frame, 'api_client') and book_search_frame.api_client:
            return book_search_frame.api_client

        messagebox.showerror("错误", "API客户端未初始化，请先在书籍搜索页面进行搜索")
        return None
