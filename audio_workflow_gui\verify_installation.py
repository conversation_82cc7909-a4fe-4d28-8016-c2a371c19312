#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
安装验证脚本

验证应用程序是否正确安装和配置。

作者：Augment Agent
版本：1.0.0
"""

import sys
import os
import platform
from pathlib import Path

def check_python_version():
    """检查Python版本"""
    print("检查Python版本...")
    version = sys.version_info
    
    if version.major < 3 or (version.major == 3 and version.minor < 7):
        print(f"✗ Python版本过低: {version.major}.{version.minor}")
        print("  需要Python 3.7或更高版本")
        return False
    else:
        print(f"✓ Python版本: {version.major}.{version.minor}.{version.micro}")
        return True

def check_dependencies():
    """检查依赖包"""
    print("\n检查依赖包...")
    
    required_packages = [
        ('tkinter', 'GUI框架'),
        ('requests', 'HTTP客户端'),
        ('json', 'JSON处理'),
        ('pathlib', '路径处理'),
        ('threading', '多线程支持'),
        ('logging', '日志系统')
    ]
    
    missing_packages = []
    
    for package, description in required_packages:
        try:
            __import__(package)
            print(f"✓ {package} - {description}")
        except ImportError:
            print(f"✗ {package} - {description} (缺失)")
            missing_packages.append(package)
    
    if missing_packages:
        print(f"\n缺失的包: {', '.join(missing_packages)}")
        print("请运行: pip install -r requirements.txt")
        return False
    
    return True

def check_project_structure():
    """检查项目结构"""
    print("\n检查项目结构...")
    
    required_files = [
        'main.py',
        'requirements.txt',
        'README.md',
        'config/settings.py',
        'config/config.json',
        'api/client.py',
        'api/models.py',
        'gui/main_window.py',
        'utils/logger.py',
        'utils/retry.py',
        'utils/file_manager.py'
    ]
    
    required_dirs = [
        'config',
        'api',
        'gui',
        'utils',
        'tests',
        'audio_files'
    ]
    
    missing_files = []
    missing_dirs = []
    
    # 检查文件
    for file_path in required_files:
        if not Path(file_path).exists():
            missing_files.append(file_path)
        else:
            print(f"✓ {file_path}")
    
    # 检查目录
    for dir_path in required_dirs:
        if not Path(dir_path).exists():
            missing_dirs.append(dir_path)
        else:
            print(f"✓ {dir_path}/")
    
    if missing_files:
        print(f"\n缺失的文件: {', '.join(missing_files)}")
    
    if missing_dirs:
        print(f"\n缺失的目录: {', '.join(missing_dirs)}")
    
    return len(missing_files) == 0 and len(missing_dirs) == 0

def check_permissions():
    """检查文件权限"""
    print("\n检查文件权限...")
    
    # 检查主程序是否可执行
    main_py = Path('main.py')
    if main_py.exists():
        if os.access(main_py, os.R_OK):
            print("✓ main.py 可读")
        else:
            print("✗ main.py 不可读")
            return False
    
    # 检查配置目录是否可写
    config_dir = Path('config')
    if config_dir.exists():
        if os.access(config_dir, os.W_OK):
            print("✓ config/ 目录可写")
        else:
            print("✗ config/ 目录不可写")
            return False
    
    # 检查音频文件目录是否可写
    audio_dir = Path('audio_files')
    if audio_dir.exists():
        if os.access(audio_dir, os.W_OK):
            print("✓ audio_files/ 目录可写")
        else:
            print("✗ audio_files/ 目录不可写")
            return False
    
    return True

def check_configuration():
    """检查配置文件"""
    print("\n检查配置文件...")
    
    config_file = Path('config/config.json')
    if not config_file.exists():
        print("✗ 配置文件不存在")
        return False
    
    try:
        import json
        with open(config_file, 'r', encoding='utf-8') as f:
            config = json.load(f)
        
        # 检查必要的配置项
        required_keys = [
            'api.base_url',
            'api.version',
            'paths.audio_files',
            'logging.level'
        ]
        
        for key in required_keys:
            keys = key.split('.')
            value = config
            try:
                for k in keys:
                    value = value[k]
                print(f"✓ 配置项 {key}: {value}")
            except KeyError:
                print(f"✗ 缺失配置项: {key}")
                return False
        
        return True
        
    except json.JSONDecodeError as e:
        print(f"✗ 配置文件格式错误: {e}")
        return False
    except Exception as e:
        print(f"✗ 读取配置文件失败: {e}")
        return False

def run_startup_test():
    """运行启动测试"""
    print("\n运行启动测试...")
    
    try:
        # 尝试导入主要模块
        sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))
        
        from config.settings import AppConfig
        from api.client import GStudioAPIClient
        from utils.logger import setup_logger
        
        # 测试配置加载
        config = AppConfig()
        print("✓ 配置系统正常")
        
        # 测试API客户端
        client = GStudioAPIClient()
        print("✓ API客户端正常")
        
        # 测试日志系统
        logger = setup_logger()
        print("✓ 日志系统正常")
        
        return True
        
    except Exception as e:
        print(f"✗ 启动测试失败: {e}")
        return False

def main():
    """主验证函数"""
    print("GStudio 音频工作流 GUI 应用程序安装验证")
    print("=" * 50)
    
    print(f"操作系统: {platform.system()} {platform.release()}")
    print(f"Python路径: {sys.executable}")
    print(f"工作目录: {os.getcwd()}")
    print()
    
    checks = [
        ("Python版本", check_python_version),
        ("依赖包", check_dependencies),
        ("项目结构", check_project_structure),
        ("文件权限", check_permissions),
        ("配置文件", check_configuration),
        ("启动测试", run_startup_test)
    ]
    
    passed = 0
    total = len(checks)
    
    for check_name, check_func in checks:
        print(f"{'='*20} {check_name} {'='*20}")
        if check_func():
            passed += 1
            print(f"✓ {check_name} 检查通过\n")
        else:
            print(f"✗ {check_name} 检查失败\n")
    
    print("=" * 50)
    print(f"验证结果: {passed}/{total} 通过")
    
    if passed == total:
        print("✓ 安装验证通过，应用程序可以正常使用")
        print("\n下一步:")
        print("1. 运行 python main.py 启动应用程序")
        print("2. 或者运行 start.bat (Windows) / ./start.sh (Linux/macOS)")
        print("3. 在应用程序中设置API Token")
        return True
    else:
        print("✗ 安装验证失败，请解决上述问题后重试")
        print("\n建议:")
        print("1. 检查Python版本是否为3.7+")
        print("2. 运行 pip install -r requirements.txt")
        print("3. 确保所有文件完整下载")
        return False

if __name__ == "__main__":
    success = main()
    input("\n按回车键退出...")
    sys.exit(0 if success else 1)
