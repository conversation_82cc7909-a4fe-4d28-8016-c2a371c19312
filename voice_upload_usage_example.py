#!/usr/bin/env python3
"""
音频上传API使用示例
演示如何使用新添加的音频文件上传功能
"""

import sys
import os
import requests
import json
from datetime import datetime

# 添加simple-gstudio-api目录到Python路径
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'simple-gstudio-api'))

from gstudio_api import (
    APIEndpoints,
    VoiceUploadParams,
    VoiceUploadResponse,
    DefaultHeaders
)

def demo_voice_upload_basic():
    """演示基本的音频上传参数构建"""
    print("=== 示例1: 基本音频上传参数 ===")
    
    # 创建上传参数
    upload_params = VoiceUploadParams(
        file="path/to/audio.mp3",
        cueId=691699346
    )
    
    print(f"参数: file={upload_params.file}, cueId={upload_params.cueId}")
    print(f"filename: {upload_params.filename} (自动推断)")
    
    # 构建API URL
    upload_url = APIEndpoints.get_url(APIEndpoints.Material.VOICE_UPLOAD)
    print(f"API端点: {upload_url}")
    
    print("请求方法: POST")
    print("Content-Type: multipart/form-data (由requests自动设置)")

def demo_voice_upload_with_filename():
    """演示带自定义文件名的音频上传"""
    print("\n=== 示例2: 带自定义文件名的音频上传 ===")
    
    # 创建上传参数（与实际API调用记录一致）
    upload_params = VoiceUploadParams(
        file="曹先森MK-III.mp3",
        cueId=691699346,
        filename="曹先森MK-III.mp3"
    )
    
    print(f"参数: file={upload_params.file}")
    print(f"cueId: {upload_params.cueId}")
    print(f"filename: {upload_params.filename}")
    
    # 构建API URL
    upload_url = APIEndpoints.get_url(APIEndpoints.Material.VOICE_UPLOAD)
    print(f"API端点: {upload_url}")

def demo_multipart_request_structure():
    """演示multipart/form-data请求结构"""
    print("\n=== 示例3: multipart/form-data请求结构 ===")
    
    upload_params = VoiceUploadParams(
        file="audio.mp3",
        cueId=691699346,
        filename="曹先森MK-III.mp3"
    )
    
    print("multipart/form-data结构:")
    print("------WebKitFormBoundary...")
    print('Content-Disposition: form-data; name="file"; filename="曹先森MK-III.mp3"')
    print("Content-Type: audio/mpeg")
    print("")
    print("[二进制音频数据]")
    print("")
    print("------WebKitFormBoundary...")
    print('Content-Disposition: form-data; name="cueId"')
    print("")
    print(f"{upload_params.cueId}")
    print("------WebKitFormBoundary...--")

def demo_requests_usage():
    """演示使用requests库进行实际上传"""
    print("\n=== 示例4: 使用requests库进行音频上传 ===")
    
    # 创建上传参数
    upload_params = VoiceUploadParams(
        file="path/to/audio.mp3",
        cueId=691699346,
        filename="audio.mp3"
    )
    
    # 构建请求
    url = APIEndpoints.get_url(APIEndpoints.Material.VOICE_UPLOAD)
    
    print("Python代码示例:")
    print(f"""
import requests

# API配置
url = "{url}"
headers = {{
    "authorization": "Bearer your_token_here"
}}

# 准备multipart/form-data
files = {{
    'file': ('{upload_params.filename}', open('{upload_params.file}', 'rb'), 'audio/mpeg')
}}
data = {{
    'cueId': {upload_params.cueId}
}}

# 发送请求
response = requests.post(url, headers=headers, files=files, data=data)

# 处理响应
if response.status_code == 200:
    result = response.json()
    print("上传成功!")
    print(f"识别文本: {{result['data']['content']}}")
    print(f"材料ID: {{result['data']['materialId']}}")
    print(f"有效时长: {{result['data']['durationEffectiveMs']}}ms")
else:
    print(f"上传失败: {{response.status_code}}")
""")

def demo_response_structure():
    """演示响应数据结构"""
    print("\n=== 示例5: 响应数据结构 ===")
    
    # 模拟响应数据（与实际API响应一致）
    response_data = VoiceUploadResponse(
        content="甘泉攻。",
        durationEffectiveMs=950,
        durationTotalMs=1360,
        leftBlankHeadMs=210,
        leftBlankTailMs=200,
        materialId=332540673,
        snrDb=43.383324
    )
    
    print("响应数据结构:")
    print(json.dumps({
        "code": 1,
        "msg": "成功!",
        "data": {
            "content": response_data.content,
            "durationEffectiveMs": response_data.durationEffectiveMs,
            "durationTotalMs": response_data.durationTotalMs,
            "leftBlankHeadMs": response_data.leftBlankHeadMs,
            "leftBlankTailMs": response_data.leftBlankTailMs,
            "materialId": response_data.materialId,
            "snrDb": response_data.snrDb
        }
    }, indent=2, ensure_ascii=False))
    
    print("\n字段说明:")
    print(f"- content: 语音识别的文本内容 ('{response_data.content}')")
    print(f"- durationEffectiveMs: 有效音频时长 ({response_data.durationEffectiveMs}ms)")
    print(f"- durationTotalMs: 总音频时长 ({response_data.durationTotalMs}ms)")
    print(f"- leftBlankHeadMs: 头部空白时长 ({response_data.leftBlankHeadMs}ms)")
    print(f"- leftBlankTailMs: 尾部空白时长 ({response_data.leftBlankTailMs}ms)")
    print(f"- materialId: 生成的录音材料ID ({response_data.materialId})")
    print(f"- snrDb: 信噪比 ({response_data.snrDb}dB)")

def demo_complete_upload_function():
    """演示完整的上传函数"""
    print("\n=== 示例6: 完整的上传函数 ===")
    
    print("""
def upload_voice_file(token, file_path, cue_id, filename=None):
    \"\"\"
    上传音频文件到GStudio
    
    Args:
        token: 认证令牌
        file_path: 音频文件路径
        cue_id: 内容片ID
        filename: 可选的文件名
    
    Returns:
        dict: 上传结果，包含语音识别和音频分析数据
    \"\"\"
    import requests
    import os
    
    # 构建API URL
    url = "https://www.gstudios.com.cn/story_v2/api/material/voice/upload"
    
    # 构建请求头
    headers = {
        "authorization": f"Bearer {token}"
    }
    
    # 检查文件是否存在
    if not os.path.exists(file_path):
        raise FileNotFoundError(f"音频文件不存在: {file_path}")
    
    # 推断文件名
    if filename is None:
        filename = os.path.basename(file_path)
    
    # 准备multipart/form-data
    files = {
        'file': (filename, open(file_path, 'rb'), 'audio/mpeg')
    }
    data = {
        'cueId': cue_id
    }
    
    try:
        # 发送请求
        response = requests.post(url, headers=headers, files=files, data=data)
        
        if response.status_code == 200:
            result = response.json()
            if result.get('code') == 1:
                return result['data']
            else:
                raise Exception(f"API错误: {result.get('msg', '未知错误')}")
        else:
            raise Exception(f"HTTP错误: {response.status_code}")
            
    finally:
        # 确保文件被关闭
        files['file'][1].close()

# 使用示例
try:
    result = upload_voice_file(
        token="your_token_here",
        file_path="path/to/audio.mp3",
        cue_id=691699346,
        filename="曹先森MK-III.mp3"
    )
    print(f"上传成功! 材料ID: {result['materialId']}")
    print(f"识别文本: {result['content']}")
except Exception as e:
    print(f"上传失败: {e}")
""")

def main():
    """主函数"""
    print("=== 音频上传API使用示例 ===\n")
    
    # 演示所有功能
    demo_voice_upload_basic()
    demo_voice_upload_with_filename()
    demo_multipart_request_structure()
    demo_requests_usage()
    demo_response_structure()
    demo_complete_upload_function()
    
    print("\n=== API端点信息 ===")
    upload_url = APIEndpoints.get_url(APIEndpoints.Material.VOICE_UPLOAD)
    print(f"音频上传: {upload_url}")
    
    print("\n=== 重要说明 ===")
    print("1. 此API使用POST方法和multipart/form-data格式")
    print("2. 需要Bearer Token认证")
    print("3. 支持MP3音频格式")
    print("4. 上传后自动进行语音识别和音频质量分析")
    print("5. 返回的materialId可用于后续录音管理操作")
    print("6. 请确保音频文件质量良好以获得最佳识别效果")
    print("7. 注意文件大小限制（具体限制请参考API文档）")

if __name__ == "__main__":
    main()
