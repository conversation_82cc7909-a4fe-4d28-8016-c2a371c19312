#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
SSL配置集成测试脚本

测试SSL配置功能在GUI应用程序中的集成。

作者：Augment Agent
版本：1.0.0
"""

import sys
import os
import tkinter as tk
import logging

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from config.settings import AppConfig
from gui.main_window import MainWindow
from gui.ssl_settings_dialog import SSLSettingsDialog
from gui.ssl_problem_dialog import SSLProblemDialog


def setup_logging():
    """设置日志"""
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
        handlers=[
            logging.StreamHandler(sys.stdout),
            logging.FileHandler('ssl_config_integration_test.log', encoding='utf-8')
        ]
    )


def test_ssl_config_system():
    """测试SSL配置系统"""
    print("=" * 60)
    print("测试SSL配置系统")
    print("=" * 60)
    
    # 创建配置对象
    config = AppConfig()
    
    print("1. 测试配置文件SSL设置...")
    print(f"   SSL验证启用: {config.is_ssl_verification_enabled()}")
    print(f"   SSL自动修复启用: {config.is_ssl_auto_fix_enabled()}")
    
    # 测试SSL配置方法
    print("\n2. 测试SSL配置方法...")
    
    # 禁用SSL验证
    config.disable_ssl_verification()
    print(f"   禁用后SSL验证状态: {config.is_ssl_verification_enabled()}")
    
    # 启用SSL验证
    config.enable_ssl_verification()
    print(f"   启用后SSL验证状态: {config.is_ssl_verification_enabled()}")
    
    # 测试SSL配置信息
    ssl_config = config.get_ssl_config()
    print(f"\n3. SSL配置信息:")
    for key, value in ssl_config.items():
        print(f"   {key}: {value}")
    
    return True


def test_ssl_settings_dialog():
    """测试SSL设置对话框"""
    print("\n" + "=" * 60)
    print("测试SSL设置对话框")
    print("=" * 60)
    
    try:
        # 创建根窗口（隐藏）
        root = tk.Tk()
        root.withdraw()
        
        # 创建配置对象
        config = AppConfig()
        
        print("1. 创建SSL设置对话框...")
        
        def on_settings_changed():
            print("   ✓ 设置更改回调被调用")
        
        # 创建对话框（但不显示）
        ssl_dialog = SSLSettingsDialog(root, config, on_settings_changed)
        
        print("   ✓ SSL设置对话框创建成功")
        
        # 测试对话框的方法
        ssl_dialog._load_current_settings()
        print("   ✓ 当前设置加载成功")
        
        # 关闭对话框
        ssl_dialog.destroy()
        root.destroy()
        
        return True
        
    except Exception as e:
        print(f"   ✗ SSL设置对话框测试失败: {e}")
        return False


def test_ssl_problem_dialog():
    """测试SSL问题对话框"""
    print("\n" + "=" * 60)
    print("测试SSL问题对话框")
    print("=" * 60)
    
    try:
        # 创建根窗口（隐藏）
        root = tk.Tk()
        root.withdraw()
        
        # 创建配置对象
        config = AppConfig()
        
        print("1. 创建SSL问题对话框...")
        
        error_message = "SSL certificate verify failed: unable to get local issuer certificate"
        
        def on_solution_applied(solution_type):
            print(f"   ✓ 解决方案应用回调被调用: {solution_type}")
        
        # 创建对话框（但不显示）
        ssl_problem_dialog = SSLProblemDialog(root, config, error_message, on_solution_applied)
        
        print("   ✓ SSL问题对话框创建成功")
        
        # 测试对话框的方法
        ssl_problem_dialog._display_error_info()
        print("   ✓ 错误信息显示成功")
        
        # 关闭对话框
        ssl_problem_dialog.destroy()
        root.destroy()
        
        return True
        
    except Exception as e:
        print(f"   ✗ SSL问题对话框测试失败: {e}")
        return False


def test_main_window_ssl_integration():
    """测试主窗口SSL集成"""
    print("\n" + "=" * 60)
    print("测试主窗口SSL集成")
    print("=" * 60)
    
    try:
        # 创建根窗口（隐藏）
        root = tk.Tk()
        root.withdraw()
        
        # 创建配置对象
        config = AppConfig()
        
        print("1. 创建主窗口...")
        main_window = MainWindow(root, config)
        
        print("   ✓ 主窗口创建成功")
        
        # 测试SSL状态更新
        print("\n2. 测试SSL状态更新...")
        main_window.update_ssl_status()
        print("   ✓ SSL状态更新成功")
        
        # 测试SSL设置方法
        print("\n3. 测试SSL相关方法...")
        
        # 测试刷新API客户端
        main_window._refresh_api_clients()
        print("   ✓ API客户端刷新成功")
        
        # 关闭窗口
        root.destroy()
        
        return True
        
    except Exception as e:
        print(f"   ✗ 主窗口SSL集成测试失败: {e}")
        return False


def test_api_client_ssl_config():
    """测试API客户端SSL配置"""
    print("\n" + "=" * 60)
    print("测试API客户端SSL配置")
    print("=" * 60)
    
    try:
        from api.client import GStudioAPIClient
        
        # 创建配置对象
        config = AppConfig()
        
        print("1. 测试启用SSL验证的API客户端...")
        config.enable_ssl_verification()
        
        client_ssl_enabled = GStudioAPIClient(
            verify_ssl=config.is_ssl_verification_enabled(),
            debug_mode=False
        )
        print(f"   SSL验证状态: {client_ssl_enabled.verify_ssl}")
        print("   ✓ 启用SSL的API客户端创建成功")
        
        print("\n2. 测试禁用SSL验证的API客户端...")
        config.disable_ssl_verification()
        
        client_ssl_disabled = GStudioAPIClient(
            verify_ssl=config.is_ssl_verification_enabled(),
            debug_mode=False
        )
        print(f"   SSL验证状态: {client_ssl_disabled.verify_ssl}")
        print("   ✓ 禁用SSL的API客户端创建成功")
        
        # 恢复SSL验证
        config.enable_ssl_verification()
        
        return True
        
    except Exception as e:
        print(f"   ✗ API客户端SSL配置测试失败: {e}")
        return False


def test_ssl_auto_detection():
    """测试SSL自动检测"""
    print("\n" + "=" * 60)
    print("测试SSL自动检测")
    print("=" * 60)
    
    try:
        from utils.ssl_fix import SSLFixer
        
        ssl_fixer = SSLFixer()
        
        print("1. 运行SSL诊断...")
        diagnosis = ssl_fixer.diagnose_ssl_issue("www.gstudios.com.cn")
        
        print(f"   SSL支持: {diagnosis['ssl_support']}")
        print(f"   证书有效: {diagnosis['certificate_valid']}")
        print(f"   CA证书包: {diagnosis.get('ca_bundle_path', '未知')}")
        
        if diagnosis.get('errors'):
            print("   发现的错误:")
            for error in diagnosis['errors']:
                print(f"     • {error}")
        
        if diagnosis.get('suggestions'):
            print("   建议:")
            for suggestion in diagnosis['suggestions']:
                print(f"     • {suggestion}")
        
        print("   ✓ SSL诊断完成")
        
        return True
        
    except Exception as e:
        print(f"   ✗ SSL自动检测测试失败: {e}")
        return False


def main():
    """主测试函数"""
    print("GStudio SSL配置集成测试")
    print("=" * 60)
    print("测试SSL配置功能在GUI应用程序中的集成")
    print()
    
    # 设置日志
    setup_logging()
    
    success_count = 0
    total_tests = 6
    
    try:
        # 运行测试
        tests = [
            ("SSL配置系统", test_ssl_config_system),
            ("SSL设置对话框", test_ssl_settings_dialog),
            ("SSL问题对话框", test_ssl_problem_dialog),
            ("主窗口SSL集成", test_main_window_ssl_integration),
            ("API客户端SSL配置", test_api_client_ssl_config),
            ("SSL自动检测", test_ssl_auto_detection)
        ]
        
        for test_name, test_func in tests:
            try:
                if test_func():
                    print(f"✅ {test_name}测试通过")
                    success_count += 1
                else:
                    print(f"❌ {test_name}测试失败")
            except Exception as e:
                print(f"❌ {test_name}测试异常: {e}")
        
        print("\n" + "=" * 60)
        print("测试结果总结")
        print("=" * 60)
        
        print(f"通过测试: {success_count}/{total_tests}")
        
        if success_count == total_tests:
            print("🎉 所有测试通过！SSL配置功能集成成功！")
            print()
            print("功能总结:")
            print("1. ✅ 配置文件SSL设置支持")
            print("2. ✅ SSL设置对话框功能")
            print("3. ✅ SSL问题自动检测和处理")
            print("4. ✅ 主窗口SSL状态显示")
            print("5. ✅ API客户端SSL配置集成")
            print("6. ✅ SSL诊断和修复工具")
            print()
            print("现在用户可以:")
            print("• 在设置菜单中配置SSL选项")
            print("• 自动检测和解决SSL证书问题")
            print("• 在遇到SSL问题时获得解决建议")
            print("• 安全地切换SSL验证模式")
        else:
            print("⚠ 部分测试未通过，请检查失败的测试项目")
        
    except Exception as e:
        print(f"测试过程中发生异常: {e}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    main()
