GStudio API 端点文档

基础信息
基础URL: https://www.gstudios.com.cn
API版本: story_v2
认证方式: Bearer Token

通用响应结构
所有API响应都遵循以下基础格式：
{
    "code": 1,           // 响应码：1=成功，其他值表示错误
    "msg": "string",     // 响应消息
    "data": object       // 响应数据，具体结构因接口而异
}

请求头部说明
所有API请求都需要包含以下标准头部：
{
    "host": "www.gstudios.com.cn",
    "connection": "keep-alive",
    "sec-ch-ua-platform": "Windows",
    "x-requested-with": "XMLHttpRequest",
    "user-agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********",
    "accept": "application/json, text/plain, */*",
    "sec-ch-ua": string,            // 浏览器信息
    "sec-ch-ua-mobile": "?0",       // 移动端标识
    "sec-fetch-site": "same-origin",
    "sec-fetch-mode": "cors",
    "sec-fetch-dest": "empty",
    "accept-encoding": "gzip, deflate, br, zstd",
    "accept-language": "zh-CN,zh;q=0.9,en;q=0.8,en-GB;q=0.7,en-US;q=0.6",
    "authorization": "Bearer {token}" // Bearer认证令牌
}

响应头部说明
服务器响应包含以下标准头部：
{
    "Server": "CLOUD ELB 1.0.0",
    "Date": string,                          // 响应时间
    "Content-Type": "application/json;charset=UTF-8",
    "Transfer-Encoding": "chunked",
    "Connection": "keep-alive",
    "X-Powered-By": "Express",
    "Vary": "Accept-Encoding",
    "Content-Encoding": "gzip"               // 支持gzip压缩
}

1. 书籍相关接口

1.1 获取书籍列表
路径: /story_v2/api/content/book/list
方法: GET
说明: 获取用户可访问的书籍列表
响应数据结构:
{
    "code": 1,
    "msg": "success",
    "data": {
        "list": [{
            "bookId": "string",      // 书籍ID
            "bookName": "string",    // 书籍名称
            "price": number,         // 价格
            "description": "string"  // 描述
        }],
        "total": number,            // 总记录数
        "pageSize": number,         // 每页大小
        "pageNo": number           // 当前页码
    }
}

1.2 获取书籍列表编辑器
路径: /story_v2/api/content/book/list/editor
方法: GET
说明: 获取书籍编辑器列表，支持筛选和分页功能，用于编辑器界面的书籍管理
参数:
  name: 书籍名称筛选（可选，支持模糊匹配）
  remark: 备注筛选（可选，支持模糊匹配）
  finished: 完成状态筛选（可选，true=已完成，false=未完成）
  pageSize: 每页数量（必填，示例：50）
  pageNo: 页码（必填，从1开始）
  total: 总数（可选，用于分页计算）
  sortItem: 排序项（可选，如：name、createdTime、updatedTime）
  sortAsc: 排序方向（可选，true=升序，false=降序）
响应数据结构:
{
    "code": 1,
    "msg": "成功!",
    "data": {
        "list": [{
            "id": number,                    // 书籍ID
            "name": "string",               // 书籍名称
            "description": "string",        // 书籍描述
            "finished": boolean,            // 是否完成
            "remark": "string",             // 备注信息
            "auditionRateLimit": number,    // 试音频率限制
            "auditionShowCv": boolean,      // 是否显示CV试音
            "communityVisible": boolean,    // 社区可见性
            "createdTime": number,          // 创建时间（时间戳）
            "updatedTime": number,          // 更新时间（时间戳）
            "pinned": boolean,              // 是否置顶
            "readonly": boolean,            // 是否只读
            "teamName": "string",           // 团队名称
            "useMasterComp": boolean,       // 使用主压缩
            "useVoiceComp": boolean,        // 使用语音压缩
            "useVoiceDenoise": boolean,     // 使用语音降噪
            "userCanAudit": boolean,        // 用户可审核
            "userCanDelete": boolean,       // 用户可删除
            "userCanEdit": boolean,         // 用户可编辑
            "vadSensitivity": number        // VAD敏感度
        }],
        "total": number                     // 总记录数
    }
}

使用示例:

```http
GET /story_v2/api/content/book/list/editor?name=&remark=&finished=false&pageSize=50&pageNo=1&total=0&sortItem=&sortAsc=
```

注意事项:

- 此API主要用于编辑器界面的书籍管理功能
- 响应数据包含完整的书籍配置信息和权限控制字段
- 支持多种筛选条件的组合使用
- 时间字段使用Unix时间戳格式

1.3 获取章节列表
路径: /story_v2/api/content/chapter/list
方法: GET
说明: 获取指定书籍的章节列表，支持多种筛选条件
参数:
  bookId: 书籍ID（必填）
  pageSize: 每页数量（必填，示例：50）
  pageNo: 页码（必填，从1开始）
  cvHumanId: CV人员ID（可选）
  cvHumanRelationType: CV人员关系类型（可选）
  content: 内容筛选（可选）
  queryText: 查询文本（可选）
  editionState: 编辑状态（可选，0=未发布，1=编辑中，2=已提交，3=已取消）
  executionState: 录音状态（可选，0=未发布，1-3=旧状态，4=录音中，5=返音中，6=已完成）
  auditionState: 审听状态（可选，0=未发布，1=发布中，2=待审听，3=已提交，4=已取消，5=异常）
  characterId: 角色ID（可选）
  sortItem: 排序项（可选）
  sortAsc: 排序方向（可选，true=升序，false=降序）
响应数据结构:
{
    "code": 1,
    "msg": "success",
    "data": {
        "list": [{
            "chapterId": "string",     // 章节ID
            "chapterName": "string",   // 章节名称
            "seqNum": number,          // 章节序号
            "editionState": number,    // 编辑状态
            "executionState": number,  // 执行状态
            "auditionState": number,   // 试音状态
            "content": "string",       // 章节内容
            "characterInfo": {         // 角色信息
                "characterId": "string",
                "characterName": "string"
            }
        }],
        "total": number,
        "pageSize": number,
        "pageNo": number
    }
}

1.3 获取章节状态列表
路径: /story_v2/api/content/chapter/list/seq/range
方法: GET
说明: 获取指定范围内章节的状态信息
参数:
  bookId: 书籍ID
  chapterSeqNumBegin: 起始章节序号
  chapterSeqNumEnd: 结束章节序号
响应数据结构:
{
    "code": 1,
    "msg": "success",
    "data": [{
        "chapterId": "string",
        "seqNum": number,
        "editionState": number,
        "executionState": number,
        "auditionState": number
    }]
}

2. 角色相关接口

2.1 获取角色列表
路径: /story_v2/api/content/character/list/book
方法: GET
说明: 获取指定书籍的角色列表
参数:
  bookId: 书籍ID
  roleType: 角色类型
响应数据结构:
{
    "code": 1,
    "msg": "success",
    "data": [{
        "characterId": "string",     // 角色ID
        "characterName": "string",   // 角色名称
        "roleType": number,         // 角色类型
        "description": "string"     // 角色描述
    }]
}

3. 合作成员相关接口

3.1 获取已邀请合作成员列表
路径: /story_v2/api/content/book/relation/freelancer/by/book
方法: GET
说明: 获取已经邀请的合作成员列表
参数:
  bookId: 书籍ID（必填）
  freelancerName: 成员名称（可选，用于筛选）
  sortItem: 排序项（可选）
  sortAsc: 排序方向（可选）
响应数据结构:
{
    "code": 1,                             // 状态码：1表示成功
    "msg": "success",                      // 响应消息
    "data": {
        "list": [{
            "monthly": boolean,            // 是否月薪制（false=非月薪）
            "playerAvatar": "string",      // 成员头像
            "playerDesc": "string",        // 成员描述
            "playerName": "string",        // 成员名称
            "price": number,               // 价格（单位：分，如25000=250元）
            "priceProposal": number,       // 价格提议
            "proposalConfirmedTime": number,// 提议确认时间（13位时间戳）
            "proposalState": number,       // 提议状态（0=未提议）
            "redeemCode": "string",        // 兑换码
            "redeemCreatedTime": number,   // 兑换码创建时间（13位时间戳）
            "redeemId": number,            // 兑换ID
            "redeemState": number,         // 兑换状态（1=已兑换）
            "redeemSubjectId": number,     // 兑换主体ID
            "redeemSubjectName": "string", // 兑换主体名称
            "relationEnabled": number,     // 关系状态（1=启用）
            "relationId": number,          // 关系ID
            "relationType": number         // 关系类型（3=CV/配音）
        }]
    }
}

常见价格档位:
- 25000 (250元)
- 20000 (200元)
- 15000 (150元)
- 12000 (120元)
- 10000 (100元)

状态说明:
1. redeemState（兑换状态）
   - 0: 未兑换
   - 1: 已兑换

2. relationEnabled（关系状态）
   - 0: 禁用
   - 1: 启用
   - null: 未建立关系

3. proposalState（提议状态）
   - 0: 未提议
   - 1: 已提议待确认
   - 2: 已确认
   - 3: 已拒绝
   - null: 无提议

4. relationType（关系类型）
   - 1: 主编
   - 2: 编辑
   - 3: CV/配音
   - 4: 校对
   - 5: 美工

3.2 获取可邀请的合作成员列表
路径: /story_v2/api/content/book/partner/list
方法: GET
说明: 获取可以邀请的合作成员列表
响应数据结构:
{
    "code": 1,
    "msg": "success",
    "data": [{
        "userId": "string",
        "userName": "string",
        "userType": number,
        "availableStatus": number    // 可用状态
    }]
}

3.3 获取合作成员列表
路径: /story_v2/api/content/book/partner/list
方法: GET
说明: 获取书籍的合作成员列表
参数:
  bookId: string       // 书籍ID
  subjectType: string  // 主体类型
响应数据结构:
{
    "code": 1,
    "msg": "success",
    "data": {
        "list": [{
            "subjectId": number,     // 主体ID
            "subjectName": string,   // 主体名称
            "subjectType": number,   // 主体类型
            "status": number         // 状态
        }]
    }
}

4. 录音相关接口

4.1 TTS试听音频生成
路径: /story_v2/api/record/tts/trial
方法: POST
说明: 生成TTS试听音频，返回MP3格式的音频文件
请求参数:
{
    "bookId": "string",                    // 书籍ID（必填，示例："39726"）
    "cvRobotId": number,                   // CV机器人ID（必填，示例：568）
    "ssml": "string",                      // SSML文本内容（必填）
    "genMode": number,                     // 生成模式（可选，默认：0）
    "speedFactor": number,                 // 语速因子（可选，默认：100）
    "durationFactorSilence": number,       // 静音时长因子（可选，默认：100）
    "seed": number                         // 随机种子（可选，默认：null）
}
响应格式:
- Content-Type: audio/mpeg
- 响应体: 二进制MP3音频数据
- 状态码: 200（成功）

注意：此API返回二进制音频数据，而非标准JSON响应格式。

4.2 获取章节及CV任务状态列表
路径: /story_v2/api/record/task/batch/cancel/chapter/cv/info
方法: POST
说明: 批量获取章节和CV任务的状态信息
请求参数:
  bookId: 书籍ID（必填，示例："33524"）
  seqNumBegin: 起始序号（必填，示例：0）
  seqNumEnd: 结束序号（必填，示例：38）
  rangeMode: 范围模式（必填，示例：0）
响应数据结构:
{
    "code": 1,
    "msg": "success",
    "data": [{
        "chapterId": "string",        // 章节ID
        "cvTaskId": "string",         // CV任务ID
        "status": number,             // 任务状态
        "executionState": number,     // 录音状态（0=未发布，1-3=旧状态，4=录音中，5=返音中，6=已完成）
        "auditionState": number       // 审听状态（0=未发布，1=发布中，2=待审听，3=已提交，4=已取消，5=异常）
    }]
}

4.3 获取主播进度
路径: /story_v2/api/record/task/progress/book
方法: GET
说明: 获取主播在特定书籍中的录制进度
参数:
  bookId: 书籍ID
  cvName: CV名称(可选)
  executionState: 执行状态(可选)
  pageSize: 每页数量
  pageNo: 页码
  total: 总数
响应数据结构:
{
    "code": 1,
    "msg": "success",
    "data": {
        "list": [{
            "cvId": "string",           // CV ID
            "cvName": "string",         // CV 名称
            "totalChapters": number,    // 总章节数
            "completedChapters": number, // 已完成章节数
            "progress": number,         // 进度百分比
            "lastUpdateTime": "string"  // 最后更新时间
        }],
        "total": number,
        "pageSize": number,
        "pageNo": number
    }
}

5. 录音材料相关接口

5.1 获取章节内容统计
路径: /story_v2/api/material/voice/count/chapter/cues
方法: GET
说明: 获取章节中每个内容片的录音统计信息
参数:
  chapterId: 章节ID（必填，示例：8607068）
响应数据结构:
{
    "code": 1,
    "msg": "成功!",
    "data": {
        "list": [{
            "count": number,              // 录音数量
            "cueId": number,              // 内容片ID
            "selectedMaterialId": number  // 选中的录音材料ID
        }]
    }
}

5.2 获取章节内容片录音列表
路径: /story_v2/api/material/voice/list/cue
方法: GET
说明: 获取指定内容片的所有录音材料列表
参数:
  cueId: 内容片ID（必填，示例：691699346）
  characterId: 角色ID（必填，示例：5530515）
响应数据结构:
{
    "code": 1,
    "msg": "成功!",
    "data": {
        "list": [{
            "characterName": "string",           // 角色名称
            "createdTime": number,               // 创建时间戳
            "cvName": "string",                  // CV名称
            "cvType": number,                    // CV类型（0=机器人，1=人工）
            "durationEffectiveMs": number,       // 有效时长（毫秒）
            "gainDb": number,                    // 音量增益（分贝）
            "materialId": number,                // 录音材料ID
            "needBill": boolean,                 // 是否需要计费
            "numCharCharged": number,            // 计费字符数
            "robotDurationFactorSilence": number, // 机器人静音时长因子
            "robotGenMode": number,              // 机器人生成模式
            "robotSpeedFactor": number,          // 机器人语速因子
            "robotStyle": "string",              // 机器人风格
            "robotStyleStandard": "string",      // 机器人标准风格
            "sourceType": number                 // 来源类型
        }],
        "selectedMaterialId": number             // 当前选中的录音材料ID
    }
}

5.3 下载录音文件
路径: /story_v2/api/material/voice/download
方法: GET
说明: 下载指定的录音文件
参数:
  id: 录音材料ID（必填，示例：305346872）
响应格式:
- Content-Type: application/octet-stream
- Content-Disposition: attachment;filename=录音下载-{timestamp}.mp3
- 响应体: 二进制MP3音频数据
- 状态码: 200（成功）

注意：此API返回二进制音频文件，而非标准JSON响应格式。

5.4 上传音频文件
路径: /story_v2/api/material/voice/upload
方法: POST
说明: 上传音频文件进行语音识别和音频分析
Content-Type: multipart/form-data
请求参数:
  file: 音频文件（必填，支持MP3格式）
  cueId: 内容片ID（必填，示例：691699346）
响应数据结构:
{
    "code": 1,
    "msg": "成功!",
    "data": {
        "content": "string",              // 识别的文本内容
        "durationEffectiveMs": number,   // 有效时长（毫秒）
        "durationTotalMs": number,       // 总时长（毫秒）
        "leftBlankHeadMs": number,       // 头部空白时长（毫秒）
        "leftBlankTailMs": number,       // 尾部空白时长（毫秒）
        "materialId": number,            // 生成的材料ID
        "snrDb": number                  // 信噪比（分贝）
    }
}

使用示例（Python requests）:
```python
import requests

url = "https://www.gstudios.com.cn/story_v2/api/material/voice/upload"
headers = {
    "authorization": "Bearer your_token_here"
}

# 准备multipart/form-data
files = {
    'file': ('audio.mp3', open('path/to/audio.mp3', 'rb'), 'audio/mpeg')
}
data = {
    'cueId': 691699346
}

response = requests.post(url, headers=headers, files=files, data=data)
result = response.json()
```

注意：
- 此API使用multipart/form-data格式上传文件
- 支持MP3音频格式
- 上传后会自动进行语音识别和音频质量分析
- 返回的materialId可用于后续的录音管理操作

6. 章节内容相关接口

6.1 获取章节编辑器内容列表
路径: /story_v2/api/content/chapter/cues/list/editor
方法: GET
说明: 获取章节在编辑器中的完整内容结构
参数:
  chapterId: 章节ID（必填，示例：8607068）
响应数据结构:
{
    "code": 1,
    "msg": "成功!",
    "data": {
        "characterIds": [],              // 角色ID列表
        "cues": [{
            "action": number,            // 动作类型
            "chapterTitle": boolean,     // 是否为章节标题
            "characterId": number,       // 角色ID
            "cutInMs": number,           // 切入时间（毫秒）
            "cutMode": number,           // 切割模式
            "cutOutMs": number,          // 切出时间（毫秒）
            "delayMs": number,           // 延迟时间（毫秒）
            "durationMs": number,        // 持续时间（毫秒）
            "envType": number,           // 环境类型
            "fadeInMs": number,          // 淡入时间（毫秒）
            "fadeMode": number,          // 淡化模式
            "fadeOutMs": number,         // 淡出时间（毫秒）
            "gainDb": number,            // 音量增益（分贝）
            "gainMode": number,          // 增益模式
            "id": number,                // 内容片ID
            "loopMode": number,          // 循环模式
            "materialId": number,        // 录音材料ID
            "materialItemId": number,    // 录音材料项ID
            "materialItemName": "string", // 录音材料项名称
            "materialMode": number,      // 录音材料模式
            "materialName": "string",    // 录音材料名称
            "mode": number,              // 模式
            "pairedCueId": "string",     // 配对内容片ID
            "perfTips": "string",        // 表演提示
            "seqNum": number,            // 序号
            "ssml": "string",            // SSML文本
            "text": "string",            // 文本内容
            "type": number               // 类型
        }],
        "revision": number               // 版本号
    }
}

7. 用户认证接口

7.1 用户门户
路径: /user/portal
方法: GET
说明: 用户认证入口
参数:
  code: 认证码
  state: 状态值
响应数据结构:
{
    "code": 1,
    "msg": "success",
    "data": {
        "token": "string",           // 访问令牌
        "refreshToken": "string",    // 刷新令牌
        "expiresIn": number,        // 过期时间(秒)
        "userInfo": {
            "userId": "string",
            "userName": "string",
            "userType": number,
            "userRole": number
        }
    }
}

状态码说明

通用状态码
1: 成功
401: 未授权
403: 禁止访问
404: 资源不存在
500: 服务器错误

业务状态码
编辑状态(editionState)
  0: 未发布
  1: 编辑中
  2: 已提交
  3: 已取消

录音状态(executionState)
  0: 未发布
  1: (录音中) - 旧状态，保留兼容性
  2: (返音中) - 旧状态，保留兼容性
  3: (已完成) - 旧状态，保留兼容性
  4: 录音中
  5: 返音中
  6: 已完成

审听状态(auditionState)
  0: 未发布
  1: 发布中
  2: 待审听
  3: 已提交
  4: 已取消
  5: 异常
  5: 异常

兑换状态(redeemState)
  0: 未兑换
  1: 已兑换

关系状态(relationEnabled)
  0: 禁用
  1: 启用
  null: 未建立关系

提议状态(proposalState)
  0: 未提议
  1: 已提议待确认
  2: 已确认
  3: 已拒绝
  null: 无提议

关系类型(relationType)
  1: 主编
  2: 编辑
  3: CV/配音
  4: 校对
  5: 美工

错误处理
1. 当API调用出现错误时，响应中可能包含以下字段：
   - 请求出错: 具体错误信息
   - 错误堆栈: 开发环境下可能返回详细的错误堆栈信息

2. 常见错误类型：
   - 'AutoInviteApp' object has no attribute 'format_json_response'
   - 认证令牌失效或不存在
   - 请求参数错误
   - 服务器内部错误

价格说明
1. 价格单位：分
2. 常见价格档位：
   - 10000 (100元)
   - 12000 (120元)
   - 15000 (150元)
   - 20000 (200元)
   - 25000 (250元)
   - 30000 (300元)

时间戳说明
1. 系统使用13位毫秒级时间戳
2. 示例字段：
   - redeemCreatedTime
   - proposalConfirmedTime
3. 时间格式：
   - API请求时间格式：YYYY-MM-DD HH:mm:ss
   - 时间戳示例：1744115320230

状态码说明

1. 编辑状态（editionState）
   - 0: 未发布
   - 1: 编辑中
   - 2: 已提交
   - 3: 已取消

2. 录音状态（executionState）
   - 0: 未发布
   - 1: (录音中) - 旧状态，保留兼容性
   - 2: (返音中) - 旧状态，保留兼容性
   - 3: (已完成) - 旧状态，保留兼容性
   - 4: 录音中
   - 5: 返音中
   - 6: 已完成

3. 审听状态（auditionState）
   - 0: 未发布
   - 1: 发布中
   - 2: 待审听
   - 3: 已提交
   - 4: 已取消
   - 5: 异常
   - 5: 异常

4. CV任务状态（status）
   - 0: 未开始
   - 1: 进行中
   - 2: 已完成
   - 3: 已取消

5. 范围模式（rangeMode）
   - 0: 按序号范围
   - 1: 按章节ID范围