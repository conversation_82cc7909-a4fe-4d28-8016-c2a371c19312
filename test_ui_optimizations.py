#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
UI优化功能测试脚本

测试表格条纹化美化和TTS配置机器人ID显示优化功能。

作者：Augment Agent
版本：1.0.0
"""

import tkinter as tk
from tkinter import ttk
import sys
import os

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def test_table_striping():
    """测试表格条纹化功能"""
    print("=== 测试表格条纹化功能 ===")
    
    try:
        from audio_workflow_gui.utils.table_styles import setup_striped_table, refresh_table_stripes
        
        # 创建测试窗口
        root = tk.Tk()
        root.title("表格条纹化测试")
        root.geometry("800x600")
        
        # 创建测试表格
        frame = ttk.Frame(root)
        frame.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)
        
        # 创建Treeview
        columns = ("id", "name", "status", "time")
        tree = ttk.Treeview(frame, columns=columns, show="headings", height=15)
        
        # 设置列标题
        tree.heading("id", text="ID")
        tree.heading("name", text="名称")
        tree.heading("status", text="状态")
        tree.heading("time", text="时间")
        
        # 设置列宽
        tree.column("id", width=80)
        tree.column("name", width=200)
        tree.column("status", width=100)
        tree.column("time", width=150)
        
        # 添加滚动条
        scrollbar = ttk.Scrollbar(frame, orient=tk.VERTICAL, command=tree.yview)
        tree.configure(yscrollcommand=scrollbar.set)
        
        # 布局
        tree.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        scrollbar.pack(side=tk.RIGHT, fill=tk.Y)
        
        # 应用条纹化样式
        setup_striped_table(tree, auto_update=True)
        
        # 添加测试数据
        test_data = [
            ("1", "测试项目1", "进行中", "2025-01-26 10:00"),
            ("2", "测试项目2", "完成", "2025-01-26 10:15"),
            ("3", "测试项目3", "待处理", "2025-01-26 10:30"),
            ("4", "测试项目4", "进行中", "2025-01-26 10:45"),
            ("5", "测试项目5", "完成", "2025-01-26 11:00"),
            ("6", "测试项目6", "待处理", "2025-01-26 11:15"),
            ("7", "测试项目7", "进行中", "2025-01-26 11:30"),
            ("8", "测试项目8", "完成", "2025-01-26 11:45"),
            ("9", "测试项目9", "待处理", "2025-01-26 12:00"),
            ("10", "测试项目10", "进行中", "2025-01-26 12:15"),
        ]
        
        for data in test_data:
            tree.insert("", tk.END, values=data)
        
        # 刷新条纹
        refresh_table_stripes(tree)
        
        # 添加控制按钮
        button_frame = ttk.Frame(root)
        button_frame.pack(fill=tk.X, padx=10, pady=5)
        
        def add_row():
            """添加新行测试"""
            import random
            new_id = len(tree.get_children()) + 1
            statuses = ["进行中", "完成", "待处理"]
            tree.insert("", tk.END, values=(
                str(new_id),
                f"新测试项目{new_id}",
                random.choice(statuses),
                "2025-01-26 12:30"
            ))
            refresh_table_stripes(tree)
        
        def clear_rows():
            """清空所有行"""
            for item in tree.get_children():
                tree.delete(item)
        
        ttk.Button(button_frame, text="添加行", command=add_row).pack(side=tk.LEFT, padx=5)
        ttk.Button(button_frame, text="清空", command=clear_rows).pack(side=tk.LEFT, padx=5)
        ttk.Button(button_frame, text="关闭", command=root.destroy).pack(side=tk.RIGHT, padx=5)
        
        # 添加说明标签
        info_label = ttk.Label(root, text="测试表格条纹化效果：偶数行应显示浅蓝色背景，奇数行显示白色背景")
        info_label.pack(pady=5)
        
        print("✓ 表格条纹化测试窗口已创建")
        print("  - 检查表格是否显示交替的背景色")
        print("  - 测试添加行功能是否保持条纹效果")
        
        # 5秒后自动关闭（可手动关闭）
        root.after(5000, root.destroy)
        root.mainloop()
        
        return True
        
    except Exception as e:
        print(f"✗ 表格条纹化测试失败: {e}")
        return False

def test_robot_name_display():
    """测试机器人名称显示功能"""
    print("\n=== 测试机器人名称显示功能 ===")
    
    try:
        # 创建测试窗口
        root = tk.Tk()
        root.title("机器人名称显示测试")
        root.geometry("600x400")
        
        # 创建主框架
        main_frame = ttk.Frame(root)
        main_frame.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)
        
        # 标题
        title_label = ttk.Label(main_frame, text="TTS配置机器人ID显示优化测试", 
                               font=("Arial", 14, "bold"))
        title_label.pack(pady=10)
        
        # 模拟TTS配置界面
        config_frame = ttk.LabelFrame(main_frame, text="TTS配置", padding=10)
        config_frame.pack(fill=tk.X, pady=10)
        
        # 机器人配置显示
        robot_frame = ttk.Frame(config_frame)
        robot_frame.pack(fill=tk.X, pady=5)
        
        ttk.Label(robot_frame, text="CV机器人:").pack(side=tk.LEFT, padx=(0, 5))
        robot_display_var = tk.StringVar()
        robot_display_label = ttk.Label(robot_frame, textvariable=robot_display_var, 
                                       width=25, relief="sunken", background="white")
        robot_display_label.pack(side=tk.LEFT, padx=(0, 15))
        
        # 测试不同的机器人ID显示
        test_robots = [
            (568, "曹先森MK-III"),
            (1, "云希"),
            (999, None),  # 未知机器人
        ]
        
        def update_robot_display(robot_id, robot_name=None):
            """更新机器人显示"""
            if robot_name:
                display_text = f"{robot_name} ({robot_id})"
            else:
                display_text = f"机器人ID: {robot_id}"
            robot_display_var.set(display_text)
        
        # 测试按钮
        button_frame = ttk.Frame(main_frame)
        button_frame.pack(fill=tk.X, pady=10)
        
        for robot_id, robot_name in test_robots:
            btn_text = f"测试 {robot_name or f'ID:{robot_id}'}"
            ttk.Button(button_frame, text=btn_text, 
                      command=lambda rid=robot_id, rname=robot_name: update_robot_display(rid, rname)
                      ).pack(side=tk.LEFT, padx=5)
        
        # 说明文本
        info_text = tk.Text(main_frame, wrap=tk.WORD, height=8)
        info_text.pack(fill=tk.BOTH, expand=True, pady=10)
        
        info_content = """测试说明：

1. 点击不同的测试按钮，观察机器人显示是否正确更新
2. 已知机器人应显示为：名称 (ID)
3. 未知机器人应显示为：机器人ID: XXX

预期结果：
- 曹先森MK-III (568)
- 云希 (1)  
- 机器人ID: 999

这个测试验证了TTS配置界面中机器人ID显示优化的功能。
原来只显示数字ID，现在显示更友好的名称格式。"""
        
        info_text.insert(1.0, info_content)
        info_text.config(state=tk.DISABLED)
        
        # 初始显示
        update_robot_display(568, "曹先森MK-III")
        
        # 关闭按钮
        ttk.Button(main_frame, text="关闭", command=root.destroy).pack(pady=5)
        
        print("✓ 机器人名称显示测试窗口已创建")
        print("  - 检查机器人名称是否正确显示")
        print("  - 测试不同机器人ID的显示格式")
        
        # 5秒后自动关闭（可手动关闭）
        root.after(5000, root.destroy)
        root.mainloop()
        
        return True
        
    except Exception as e:
        print(f"✗ 机器人名称显示测试失败: {e}")
        return False

def main():
    """主测试函数"""
    print("GStudio UI优化功能测试")
    print("=" * 50)
    
    # 测试表格条纹化
    striping_success = test_table_striping()
    
    # 测试机器人名称显示
    robot_display_success = test_robot_name_display()
    
    # 总结测试结果
    print("\n" + "=" * 50)
    print("测试结果总结:")
    print(f"表格条纹化功能: {'✓ 通过' if striping_success else '✗ 失败'}")
    print(f"机器人名称显示: {'✓ 通过' if robot_display_success else '✗ 失败'}")
    
    if striping_success and robot_display_success:
        print("\n🎉 所有UI优化功能测试通过！")
        return True
    else:
        print("\n❌ 部分测试失败，请检查相关功能")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
