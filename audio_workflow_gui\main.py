#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
GStudio 音频工作流 GUI 应用程序主入口

这是应用程序的主入口文件，负责启动GUI界面。

使用方法：
    python main.py

作者：Augment Agent
版本：1.0.0
"""

import sys
import os
import tkinter as tk
from tkinter import messagebox
import logging

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from config.settings import AppConfig
from utils.logger import setup_logger
from gui.main_window import MainWindow


def main():
    """主函数"""
    try:
        # 初始化配置
        config = AppConfig()
        
        # 设置日志
        logger = setup_logger(config.get('logging.level', 'INFO'))
        logger.info("启动 GStudio 音频工作流 GUI 应用程序")
        
        # 创建主窗口
        root = tk.Tk()
        app = MainWindow(root, config)
        
        # 启动应用程序
        logger.info("应用程序界面已启动")
        root.mainloop()
        
    except Exception as e:
        error_msg = f"应用程序启动失败: {str(e)}"
        print(error_msg)
        
        # 尝试显示错误对话框
        try:
            root = tk.Tk()
            root.withdraw()  # 隐藏主窗口
            messagebox.showerror("启动错误", error_msg)
        except:
            pass
        
        sys.exit(1)


if __name__ == "__main__":
    main()
