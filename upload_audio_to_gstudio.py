#!/usr/bin/env python3
"""
GStudio音频上传工具
使用真实参数上传音频文件到GStudio平台

使用方法:
    python upload_audio_to_gstudio.py <音频文件路径>
    
示例:
    python upload_audio_to_gstudio.py "audio.mp3"
    python upload_audio_to_gstudio.py "C:/path/to/your/audio.mp3"
"""

import sys
import os
import requests
import json
from datetime import datetime
from pathlib import Path

# 添加simple-gstudio-api目录到Python路径
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'simple-gstudio-api'))

try:
    from gstudio_api import APIEndpoints, VoiceUploadParams, VoiceUploadResponse
except ImportError as e:
    print(f"❌ 无法导入GStudio API库: {e}")
    print("请确保simple-gstudio-api目录存在且包含正确的gstudio_api.py文件")
    sys.exit(1)

# 真实的API参数
API_TOKEN = "3dfb89119562456cb8818120139f6ae1"
CUE_ID = 691699346

def validate_audio_file(file_path):
    """
    验证音频文件
    
    Args:
        file_path: 音频文件路径
        
    Returns:
        Path: 验证后的文件路径对象
        
    Raises:
        FileNotFoundError: 文件不存在
        ValueError: 文件格式不支持
    """
    file_path = Path(file_path)
    
    # 检查文件是否存在
    if not file_path.exists():
        raise FileNotFoundError(f"音频文件不存在: {file_path}")
    
    # 检查是否是文件（不是目录）
    if not file_path.is_file():
        raise ValueError(f"指定路径不是文件: {file_path}")
    
    # 检查文件扩展名
    supported_extensions = ['.mp3', '.MP3']
    if file_path.suffix not in supported_extensions:
        raise ValueError(f"不支持的文件格式: {file_path.suffix}。支持的格式: {', '.join(supported_extensions)}")
    
    # 检查文件大小（假设最大50MB）
    file_size = file_path.stat().st_size
    max_size = 50 * 1024 * 1024  # 50MB
    if file_size > max_size:
        raise ValueError(f"文件过大: {file_size / 1024 / 1024:.1f}MB，最大支持: {max_size / 1024 / 1024}MB")
    
    print(f"✓ 文件验证通过: {file_path.name} ({file_size / 1024:.1f}KB)")
    return file_path

def upload_audio_file(file_path, token=API_TOKEN, cue_id=CUE_ID):
    """
    上传音频文件到GStudio平台
    
    Args:
        file_path: 音频文件路径
        token: API认证令牌
        cue_id: 内容片ID
        
    Returns:
        dict: 上传结果数据
        
    Raises:
        Exception: 上传失败时抛出异常
    """
    # 验证文件
    file_path = validate_audio_file(file_path)
    
    # 创建上传参数
    upload_params = VoiceUploadParams(
        file=str(file_path),
        cueId=cue_id,
        filename=file_path.name
    )
    
    print(f"📤 开始上传音频文件...")
    print(f"   文件: {upload_params.filename}")
    print(f"   内容片ID: {upload_params.cueId}")
    
    # 构建API URL
    url = APIEndpoints.get_url(APIEndpoints.Material.VOICE_UPLOAD)
    print(f"   API端点: {url}")
    
    # 构建请求头
    headers = {
        "authorization": f"Bearer {token}",
        "user-agent": "GStudio-Audio-Upload-Tool/1.0"
    }
    
    # 准备multipart/form-data
    try:
        with open(file_path, 'rb') as audio_file:
            files = {
                'file': (upload_params.filename, audio_file, 'audio/mpeg')
            }
            data = {
                'cueId': upload_params.cueId
            }
            
            print(f"🚀 正在上传...")
            
            # 发送请求
            response = requests.post(
                url, 
                headers=headers, 
                files=files, 
                data=data,
                timeout=60  # 60秒超时
            )
            
    except requests.exceptions.Timeout:
        raise Exception("上传超时，请检查网络连接或稍后重试")
    except requests.exceptions.ConnectionError:
        raise Exception("网络连接错误，请检查网络连接")
    except Exception as e:
        raise Exception(f"文件读取或上传错误: {e}")
    
    # 处理响应
    print(f"📡 响应状态码: {response.status_code}")
    
    if response.status_code != 200:
        try:
            error_data = response.json()
            error_msg = error_data.get('msg', '未知错误')
        except:
            error_msg = f"HTTP {response.status_code} 错误"
        raise Exception(f"上传失败: {error_msg}")
    
    # 解析响应数据
    try:
        result = response.json()
    except json.JSONDecodeError:
        raise Exception("响应数据格式错误，无法解析JSON")
    
    # 检查API响应状态
    if result.get('code') != 1:
        error_msg = result.get('msg', '未知API错误')
        raise Exception(f"API错误: {error_msg}")
    
    return result['data']

def display_upload_result(result_data):
    """
    显示上传结果
    
    Args:
        result_data: 上传结果数据
    """
    print(f"\n🎉 上传成功！")
    print(f"=" * 50)
    
    # 基本信息
    print(f"📋 基本信息:")
    print(f"   录音材料ID: {result_data['materialId']}")
    print(f"   识别文本: '{result_data['content']}'")
    
    # 音频时长信息
    print(f"\n⏱️  时长分析:")
    total_ms = result_data['durationTotalMs']
    effective_ms = result_data['durationEffectiveMs']
    head_blank_ms = result_data['leftBlankHeadMs']
    tail_blank_ms = result_data['leftBlankTailMs']
    
    print(f"   总时长: {total_ms}ms ({total_ms/1000:.2f}秒)")
    print(f"   有效时长: {effective_ms}ms ({effective_ms/1000:.2f}秒)")
    print(f"   头部空白: {head_blank_ms}ms ({head_blank_ms/1000:.2f}秒)")
    print(f"   尾部空白: {tail_blank_ms}ms ({tail_blank_ms/1000:.2f}秒)")
    
    # 音频质量信息
    print(f"\n🔊 音频质量:")
    snr_db = result_data['snrDb']
    print(f"   信噪比: {snr_db:.2f}dB")
    
    # 质量评估
    if snr_db >= 40:
        quality = "优秀"
    elif snr_db >= 30:
        quality = "良好"
    elif snr_db >= 20:
        quality = "一般"
    else:
        quality = "较差"
    print(f"   质量评估: {quality}")
    
    # 效率分析
    efficiency = (effective_ms / total_ms) * 100 if total_ms > 0 else 0
    print(f"\n📊 效率分析:")
    print(f"   有效音频占比: {efficiency:.1f}%")
    
    print(f"=" * 50)

def main():
    """主函数"""
    print("🎵 GStudio音频上传工具")
    print("=" * 50)
    
    # 检查命令行参数
    if len(sys.argv) != 2:
        print("❌ 使用方法错误")
        print(f"正确用法: python {sys.argv[0]} <音频文件路径>")
        print(f"示例: python {sys.argv[0]} audio.mp3")
        sys.exit(1)
    
    audio_file_path = sys.argv[1]
    print(f"📁 目标文件: {audio_file_path}")
    
    try:
        # 上传音频文件
        result_data = upload_audio_file(audio_file_path)
        
        # 显示结果
        display_upload_result(result_data)
        
        # 保存结果到文件（可选）
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        result_file = f"upload_result_{timestamp}.json"
        
        with open(result_file, 'w', encoding='utf-8') as f:
            json.dump({
                'upload_time': timestamp,
                'file_path': audio_file_path,
                'result': result_data
            }, f, indent=2, ensure_ascii=False)
        
        print(f"\n💾 结果已保存到: {result_file}")
        
    except FileNotFoundError as e:
        print(f"❌ 文件错误: {e}")
        sys.exit(1)
    except ValueError as e:
        print(f"❌ 参数错误: {e}")
        sys.exit(1)
    except Exception as e:
        print(f"❌ 上传失败: {e}")
        sys.exit(1)

if __name__ == "__main__":
    main()
