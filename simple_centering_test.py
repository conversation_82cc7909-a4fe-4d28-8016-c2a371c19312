#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
简化的窗口居中功能测试

直接测试居中逻辑，不依赖完整的应用程序结构。

作者：Augment Agent
版本：1.0.0
"""

import tkinter as tk
import time

class CenteringTest:
    """窗口居中测试类"""
    
    def __init__(self):
        self.root = tk.Tk()
        self.root.title("窗口居中功能测试")
        
        # 设置初始窗口尺寸
        self.root.geometry("1200x920")
        
        # 设置最小尺寸
        self.root.minsize(800, 600)
        
        # 创建界面
        self.create_widgets()
        
        # 执行居中测试
        self.test_centering()
    
    def create_widgets(self):
        """创建测试界面"""
        # 主框架
        main_frame = tk.Frame(self.root, bg='lightblue', padx=20, pady=20)
        main_frame.pack(fill=tk.BOTH, expand=True)
        
        # 标题
        title_label = tk.Label(main_frame, text="GStudio 窗口居中功能测试", 
                              font=("Arial", 16, "bold"), bg='lightblue')
        title_label.pack(pady=10)
        
        # 信息显示区域
        self.info_frame = tk.Frame(main_frame, bg='white', relief=tk.SUNKEN, bd=2)
        self.info_frame.pack(fill=tk.BOTH, expand=True, pady=10)
        
        # 信息文本
        self.info_text = tk.Text(self.info_frame, wrap=tk.WORD, font=("Consolas", 10))
        scrollbar = tk.Scrollbar(self.info_frame, orient=tk.VERTICAL, command=self.info_text.yview)
        self.info_text.configure(yscrollcommand=scrollbar.set)
        
        self.info_text.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        scrollbar.pack(side=tk.RIGHT, fill=tk.Y)
        
        # 按钮框架
        button_frame = tk.Frame(main_frame, bg='lightblue')
        button_frame.pack(fill=tk.X, pady=10)
        
        # 测试按钮
        tk.Button(button_frame, text="重新居中", command=self.center_window_enhanced,
                 font=("Arial", 12)).pack(side=tk.LEFT, padx=5)
        
        tk.Button(button_frame, text="基础居中", command=self.center_window_basic,
                 font=("Arial", 12)).pack(side=tk.LEFT, padx=5)
        
        tk.Button(button_frame, text="获取信息", command=self.print_window_info,
                 font=("Arial", 12)).pack(side=tk.LEFT, padx=5)
        
        tk.Button(button_frame, text="关闭", command=self.root.destroy,
                 font=("Arial", 12)).pack(side=tk.RIGHT, padx=5)
    
    def log(self, message):
        """记录日志到界面"""
        timestamp = time.strftime("%H:%M:%S")
        log_message = f"[{timestamp}] {message}\n"
        self.info_text.insert(tk.END, log_message)
        self.info_text.see(tk.END)
        self.root.update_idletasks()
        print(log_message.strip())
    
    def center_window_basic(self):
        """基础窗口居中方法"""
        self.log("开始执行基础窗口居中...")
        
        def do_center():
            try:
                # 强制更新窗口
                self.root.update_idletasks()
                
                # 获取窗口实际尺寸
                window_width = self.root.winfo_width()
                window_height = self.root.winfo_height()
                
                self.log(f"窗口实际尺寸: {window_width} x {window_height}")
                
                # 如果尺寸无效，使用默认值
                if window_width <= 1 or window_height <= 1:
                    window_width = 1200
                    window_height = 920
                    self.root.geometry(f"{window_width}x{window_height}")
                    self.root.update_idletasks()
                    self.log(f"使用默认尺寸: {window_width} x {window_height}")
                
                # 获取屏幕尺寸
                screen_width = self.root.winfo_screenwidth()
                screen_height = self.root.winfo_screenheight()
                
                self.log(f"屏幕尺寸: {screen_width} x {screen_height}")
                
                # 考虑任务栏
                taskbar_height = 50
                available_height = screen_height - taskbar_height
                
                # 计算居中位置
                x = (screen_width - window_width) // 2
                y = (available_height - window_height) // 2
                
                # 边界检查
                x = max(0, min(x, screen_width - window_width))
                y = max(0, min(y, available_height - window_height))
                
                self.log(f"计算位置: ({x}, {y})")
                
                # 设置窗口位置
                self.root.geometry(f"{window_width}x{window_height}+{x}+{y}")
                
                self.log("基础居中完成")
                
            except Exception as e:
                self.log(f"基础居中失败: {e}")
        
        # 延迟执行
        self.root.after(100, do_center)
    
    def center_window_enhanced(self):
        """增强窗口居中方法"""
        self.log("开始执行增强窗口居中...")
        
        def do_enhanced_center():
            try:
                # 强制更新窗口
                self.root.update_idletasks()
                
                # 获取窗口实际尺寸
                window_width = self.root.winfo_width()
                window_height = self.root.winfo_height()
                
                if window_width <= 1 or window_height <= 1:
                    window_width = 1200
                    window_height = 920
                
                # 获取屏幕信息
                screen_width = self.root.winfo_screenwidth()
                screen_height = self.root.winfo_screenheight()
                
                # 尝试获取鼠标位置
                try:
                    mouse_x = self.root.winfo_pointerx()
                    mouse_y = self.root.winfo_pointery()
                    self.log(f"鼠标位置: ({mouse_x}, {mouse_y})")
                except:
                    mouse_x, mouse_y = screen_width // 2, screen_height // 2
                    self.log("无法获取鼠标位置，使用屏幕中心")
                
                # 考虑系统UI元素
                taskbar_height = 50
                title_bar_height = 30
                available_width = screen_width
                available_height = screen_height - taskbar_height - title_bar_height
                
                # 计算居中位置
                x = (available_width - window_width) // 2
                y = (available_height - window_height) // 2
                
                # 边界检查
                min_x = 0
                max_x = screen_width - window_width
                min_y = 0
                max_y = screen_height - window_height - taskbar_height
                
                x = max(min_x, min(x, max_x))
                y = max(min_y, min(y, max_y))
                
                self.log(f"增强计算位置: ({x}, {y})")
                self.log(f"边界范围: X[{min_x}, {max_x}], Y[{min_y}, {max_y}]")
                
                # 设置窗口位置
                self.root.geometry(f"{window_width}x{window_height}+{x}+{y}")
                
                self.log("增强居中完成")
                
            except Exception as e:
                self.log(f"增强居中失败: {e}")
        
        # 延迟执行
        self.root.after(200, do_enhanced_center)
    
    def print_window_info(self):
        """打印当前窗口信息"""
        try:
            self.root.update_idletasks()
            
            # 获取窗口信息
            geometry = self.root.geometry()
            width = self.root.winfo_width()
            height = self.root.winfo_height()
            x = self.root.winfo_x()
            y = self.root.winfo_y()
            
            # 获取屏幕信息
            screen_width = self.root.winfo_screenwidth()
            screen_height = self.root.winfo_screenheight()
            
            self.log("=== 当前窗口信息 ===")
            self.log(f"几何信息: {geometry}")
            self.log(f"窗口尺寸: {width} x {height}")
            self.log(f"窗口位置: ({x}, {y})")
            self.log(f"屏幕尺寸: {screen_width} x {screen_height}")
            
            # 计算期望位置
            expected_x = (screen_width - width) // 2
            expected_y = (screen_height - height - 50) // 2
            
            self.log(f"期望位置: ({expected_x}, {expected_y})")
            
            # 计算偏差
            x_diff = abs(x - expected_x)
            y_diff = abs(y - expected_y)
            
            self.log(f"位置偏差: X轴 {x_diff}px, Y轴 {y_diff}px")
            
            # 判断是否居中
            tolerance = 50
            is_centered = x_diff <= tolerance and y_diff <= tolerance
            
            self.log(f"居中状态: {'✓ 已居中' if is_centered else '✗ 未居中'}")
            
        except Exception as e:
            self.log(f"获取窗口信息失败: {e}")
    
    def test_centering(self):
        """执行居中测试"""
        self.log("窗口居中功能测试开始")
        self.log("请观察窗口是否正确居中显示")
        
        # 延迟执行初始居中
        self.root.after(500, self.center_window_basic)
        
        # 延迟执行信息打印
        self.root.after(1000, self.print_window_info)
    
    def run(self):
        """运行测试"""
        self.root.mainloop()

if __name__ == "__main__":
    print("GStudio 简化窗口居中功能测试")
    print("=" * 50)
    
    test = CenteringTest()
    test.run()
    
    print("测试完成")
