# GStudio音频上传工具使用说明

## 📋 工具概述

`upload_audio_to_gstudio.py` 是一个完整的、可立即执行的Python脚本，用于将MP3音频文件上传到GStudio平台进行语音识别和音频分析。

## 🚀 快速开始

### 基本用法
```bash
python upload_audio_to_gstudio.py <音频文件路径>
```

### 使用示例
```bash
# 上传当前目录下的音频文件
python upload_audio_to_gstudio.py audio.mp3

# 上传指定路径的音频文件
python upload_audio_to_gstudio.py "C:/path/to/your/audio.mp3"

# 上传示例文件（由测试脚本生成）
python upload_audio_to_gstudio.py sample_audio.mp3
```

## ⚙️ 配置信息

### 内置参数
- **API令牌**: `3dfb89119562456cb8818120139f6ae1`
- **内容片ID**: `691699346`
- **API端点**: `https://www.gstudios.com.cn/story_v2/api/material/voice/upload`

### 支持的文件格式
- MP3 (`.mp3`, `.MP3`)
- 最大文件大小: 50MB

## 📁 文件结构

确保以下文件存在于同一目录：
```
项目目录/
├── upload_audio_to_gstudio.py     # 主上传脚本
├── test_upload_tool.py            # 测试脚本
├── simple-gstudio-api/            # API库目录
│   └── gstudio_api.py            # GStudio API库
└── sample_audio.mp3              # 示例音频文件（可选）
```

## 🔧 功能特性

### 1. 文件验证
- ✅ 检查文件是否存在
- ✅ 验证文件格式（仅支持MP3）
- ✅ 检查文件大小限制
- ✅ 确保是文件而非目录

### 2. 上传处理
- ✅ 使用multipart/form-data格式
- ✅ 自动设置正确的Content-Type
- ✅ 60秒上传超时保护
- ✅ 网络错误重试提示

### 3. 响应解析
- ✅ 语音识别文本内容
- ✅ 音频时长分析（总时长、有效时长）
- ✅ 空白检测（头部、尾部空白时长）
- ✅ 音频质量分析（信噪比）
- ✅ 自动质量评估

### 4. 错误处理
- ✅ 文件不存在错误
- ✅ 格式不支持错误
- ✅ 网络连接错误
- ✅ API响应错误
- ✅ 超时错误

## 📊 输出示例

### 成功上传输出
```
🎵 GStudio音频上传工具
==================================================
📁 目标文件: audio.mp3
✓ 文件验证通过: audio.mp3 (1234.5KB)
📤 开始上传音频文件...
   文件: audio.mp3
   内容片ID: 691699346
   API端点: https://www.gstudios.com.cn/story_v2/api/material/voice/upload
🚀 正在上传...
📡 响应状态码: 200

🎉 上传成功！
==================================================
📋 基本信息:
   录音材料ID: 332540673
   识别文本: '甘泉攻。'

⏱️  时长分析:
   总时长: 1360ms (1.36秒)
   有效时长: 950ms (0.95秒)
   头部空白: 210ms (0.21秒)
   尾部空白: 200ms (0.20秒)

🔊 音频质量:
   信噪比: 43.38dB
   质量评估: 优秀

📊 效率分析:
   有效音频占比: 69.9%
==================================================

💾 结果已保存到: upload_result_20250723_123456.json
```

### 错误输出示例
```
❌ 文件错误: 音频文件不存在: nonexistent.mp3
❌ 参数错误: 不支持的文件格式: .wav。支持的格式: .mp3, .MP3
❌ 上传失败: 网络连接错误，请检查网络连接
```

## 🧪 测试工具

### 运行测试
```bash
python test_upload_tool.py
```

### 测试功能
- ✅ API配置验证
- ✅ 参数类创建测试
- ✅ 文件验证功能测试
- ✅ multipart/form-data结构测试
- ✅ 错误处理测试
- ✅ 自动生成示例MP3文件

## 📝 使用步骤

### 1. 准备环境
```bash
# 确保安装了requests库
pip install requests

# 验证文件结构
ls -la
```

### 2. 运行测试（可选）
```bash
python test_upload_tool.py
```

### 3. 上传音频文件
```bash
python upload_audio_to_gstudio.py your_audio.mp3
```

### 4. 查看结果
- 控制台会显示详细的上传结果
- 结果会自动保存到JSON文件中

## 🔍 技术细节

### API调用流程
1. **文件验证** → 检查文件存在性、格式、大小
2. **参数构建** → 创建VoiceUploadParams对象
3. **请求构建** → 构建multipart/form-data请求
4. **文件上传** → 发送POST请求到GStudio API
5. **响应处理** → 解析JSON响应数据
6. **结果展示** → 格式化显示分析结果

### 使用的API库
- `APIEndpoints.Material.VOICE_UPLOAD` - API端点定义
- `VoiceUploadParams` - 上传参数数据类
- `VoiceUploadResponse` - 响应数据类（用于类型提示）

### 网络请求配置
- **超时时间**: 60秒
- **Content-Type**: multipart/form-data（自动设置）
- **User-Agent**: GStudio-Audio-Upload-Tool/1.0
- **认证方式**: Bearer Token

## ⚠️ 注意事项

### 文件要求
- 仅支持MP3格式音频文件
- 文件大小不超过50MB
- 确保音频文件质量良好以获得最佳识别效果

### 网络要求
- 需要稳定的网络连接
- 上传大文件时请耐心等待
- 如遇网络错误，请稍后重试

### API限制
- 使用真实的API令牌，请勿滥用
- 上传频率请控制在合理范围内
- 遵守GStudio平台的使用条款

## 🐛 故障排除

### 常见问题

**Q: 提示"无法导入GStudio API库"**
A: 确保`simple-gstudio-api/gstudio_api.py`文件存在且正确

**Q: 上传超时**
A: 检查网络连接，或尝试上传较小的文件

**Q: 文件格式不支持**
A: 确保文件是MP3格式，扩展名为`.mp3`

**Q: API错误**
A: 检查API令牌是否有效，或稍后重试

### 调试模式
如需调试，可以在脚本中添加更多日志输出：
```python
import logging
logging.basicConfig(level=logging.DEBUG)
```

## 📞 技术支持

如遇问题，请检查：
1. 文件路径是否正确
2. 网络连接是否正常
3. API库是否正确安装
4. 音频文件格式是否支持

---

**版本**: 1.0  
**更新时间**: 2025-07-23  
**兼容性**: Python 3.6+
