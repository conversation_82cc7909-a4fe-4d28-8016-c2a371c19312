#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
API客户端测试

测试API客户端的基本功能。

作者：Augment Agent
版本：1.0.0
"""

import unittest
import sys
import os

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from api.client import GStudioAPIClient
from api.models import APIVersion, ChapterListParams
from config.settings import AppConfig


class TestAPIClient(unittest.TestCase):
    """API客户端测试类"""
    
    def setUp(self):
        """测试前准备"""
        self.config = AppConfig()
        api_config = self.config.get_api_config()
        
        self.client = GStudioAPIClient(
            base_url=api_config.get('base_url'),
            version=APIVersion.V2,
            timeout=api_config.get('timeout', 30),
            request_interval=self.config.get('api.request_interval', 0.1)
        )
    
    def test_client_initialization(self):
        """测试客户端初始化"""
        self.assertIsNotNone(self.client)
        self.assertEqual(self.client.base_url, "https://www.gstudios.com.cn")
        self.assertEqual(self.client.version, APIVersion.V2)
    
    def test_url_generation(self):
        """测试URL生成"""
        url = self.client._get_url("/test/path")
        expected_url = "https://www.gstudios.com.cn/story_v2/api/test/path"
        self.assertEqual(url, expected_url)
    
    def test_headers_generation(self):
        """测试请求头生成"""
        headers = self.client._get_headers()
        self.assertIn("user-agent", headers)
        self.assertIn("accept", headers)
        
        # 测试带token的请求头
        self.client.set_token("test_token")
        headers_with_token = self.client._get_headers()
        self.assertIn("authorization", headers_with_token)
        self.assertEqual(headers_with_token["authorization"], "Bearer test_token")
    
    def test_chapter_list_params(self):
        """测试章节列表参数"""
        params = ChapterListParams(
            book_id="12345",
            page_size=50,
            page_no=1
        )
        
        self.assertEqual(params.book_id, "12345")
        self.assertEqual(params.page_size, 50)
        self.assertEqual(params.page_no, 1)


class TestConfiguration(unittest.TestCase):
    """配置测试类"""
    
    def setUp(self):
        """测试前准备"""
        self.config = AppConfig()
    
    def test_default_config_loading(self):
        """测试默认配置加载"""
        api_config = self.config.get_api_config()
        
        self.assertIsNotNone(api_config)
        self.assertEqual(api_config.get('base_url'), "https://www.gstudios.com.cn")
        self.assertEqual(api_config.get('version'), "story_v2")
    
    def test_config_validation(self):
        """测试配置验证"""
        self.assertTrue(self.config.validate_config())
    
    def test_config_get_set(self):
        """测试配置获取和设置"""
        # 测试获取
        timeout = self.config.get('api.timeout', 30)
        self.assertEqual(timeout, 30)
        
        # 测试设置
        self.config.set('api.test_key', 'test_value')
        test_value = self.config.get('api.test_key')
        self.assertEqual(test_value, 'test_value')


if __name__ == '__main__':
    unittest.main()
