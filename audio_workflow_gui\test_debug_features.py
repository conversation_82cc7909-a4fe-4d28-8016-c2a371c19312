#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
调试功能测试脚本

测试增强的调试功能和日志输出。

作者：Augment Agent
版本：1.0.0
"""

import sys
import os
import logging

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from api.client import GStudioAPIClient
from api.models import APIVersion
from config.settings import AppConfig


def setup_debug_logging():
    """设置调试级别的日志"""
    logging.basicConfig(
        level=logging.DEBUG,
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
        handlers=[
            logging.StreamHandler(sys.stdout),
            logging.FileHandler('debug_test.log', encoding='utf-8')
        ]
    )


def test_api_client_debug_mode():
    """测试API客户端的调试模式"""
    print("=" * 60)
    print("测试API客户端调试模式")
    print("=" * 60)
    
    # 创建调试模式的API客户端
    client = GStudioAPIClient(
        debug_mode=True,
        timeout=10,
        max_retries=2,
        retry_delay=1.0
    )
    
    # 设置一个测试token
    client.set_token("test_token_12345678901234567890")
    
    print("\n1. 测试API连接诊断...")
    try:
        result = client.test_api_connection()
        print(f"连接测试结果: {'成功' if result['success'] else '失败'}")
        
        if result.get('suggestions'):
            print("建议:")
            for suggestion in result['suggestions']:
                print(f"  • {suggestion}")
    
    except Exception as e:
        print(f"连接测试异常: {e}")
    
    print("\n2. 测试书籍搜索（会失败，用于观察调试日志）...")
    try:
        books = client.search_books("测试")
        print(f"搜索到 {len(books)} 本书籍")
    except Exception as e:
        print(f"搜索失败（预期）: {e}")


def test_config_debug_settings():
    """测试配置中的调试设置"""
    print("\n" + "=" * 60)
    print("测试配置调试设置")
    print("=" * 60)
    
    config = AppConfig()
    
    print(f"当前日志级别: {config.get('logging.level', 'INFO')}")
    
    # 临时设置为DEBUG级别
    original_level = config.get('logging.level', 'INFO')
    config.set('logging.level', 'DEBUG')
    
    print(f"设置后日志级别: {config.get('logging.level')}")
    
    # 恢复原始设置
    config.set('logging.level', original_level)
    print(f"恢复后日志级别: {config.get('logging.level')}")


def test_network_connectivity():
    """测试网络连接诊断功能"""
    print("\n" + "=" * 60)
    print("测试网络连接诊断")
    print("=" * 60)
    
    client = GStudioAPIClient(debug_mode=True)
    
    test_urls = [
        "https://www.gstudios.com.cn/story_v2/api/content/book/list",
        "https://www.google.com",
        "https://httpbin.org/status/404"
    ]
    
    for url in test_urls:
        print(f"\n测试URL: {url}")
        try:
            result = client._test_network_connectivity(url)
            print(f"  DNS解析: {'✓' if result['dns_resolution'] else '✗'}")
            print(f"  TCP连接: {'✓' if result['tcp_connection'] else '✗'}")
            print(f"  SSL验证: {'✓' if result['ssl_verification'] else '✗'}")
            if result.get('ip_address'):
                print(f"  IP地址: {result['ip_address']}")
            if result.get('error_details'):
                print(f"  错误: {result['error_details']}")
        except Exception as e:
            print(f"  测试异常: {e}")


def test_error_handling():
    """测试错误处理和日志记录"""
    print("\n" + "=" * 60)
    print("测试错误处理和日志记录")
    print("=" * 60)
    
    client = GStudioAPIClient(debug_mode=True, max_retries=2, timeout=5)
    
    print("\n测试无效端点（会产生详细的错误日志）...")
    try:
        # 尝试访问一个不存在的端点
        response = client._request('GET', '/invalid/endpoint')
    except Exception as e:
        print(f"捕获到预期的异常: {type(e).__name__}: {e}")


def main():
    """主测试函数"""
    print("GStudio API调试功能测试")
    print("=" * 60)
    print("此测试将演示增强的调试功能，包括:")
    print("• 详细的HTTP请求/响应日志")
    print("• 网络连接诊断")
    print("• API连接测试")
    print("• 错误处理和重试详情")
    print("• 配置调试设置")
    print()
    
    # 设置调试日志
    setup_debug_logging()
    
    try:
        # 运行各项测试
        test_config_debug_settings()
        test_network_connectivity()
        test_api_client_debug_mode()
        test_error_handling()
        
        print("\n" + "=" * 60)
        print("测试完成！")
        print("=" * 60)
        print("请查看:")
        print("1. 控制台输出的详细调试信息")
        print("2. debug_test.log 文件中的完整日志")
        print("3. 各种网络和API测试的结果")
        
    except Exception as e:
        print(f"测试过程中发生异常: {e}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    main()
    input("\n按回车键退出...")
