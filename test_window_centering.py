#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
窗口居中功能测试脚本

用于测试修复后的窗口居中功能是否正常工作。

作者：Augment Agent
版本：1.0.0
"""

import tkinter as tk
import sys
import os

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def test_window_centering():
    """测试窗口居中功能"""
    print("开始测试窗口居中功能...")
    
    try:
        # 导入必要的模块
        from audio_workflow_gui.config.settings import AppConfig
        from audio_workflow_gui.gui.main_window import MainWindow
        
        # 创建Tkinter根窗口
        root = tk.Tk()
        
        # 创建配置对象
        config = AppConfig()
        
        # 创建主窗口实例
        print("创建主窗口...")
        main_window = MainWindow(root, config)
        
        # 获取窗口信息
        def print_window_info():
            """打印窗口信息"""
            try:
                root.update_idletasks()
                
                # 获取窗口尺寸和位置
                geometry = root.geometry()
                width = root.winfo_width()
                height = root.winfo_height()
                x = root.winfo_x()
                y = root.winfo_y()
                
                # 获取屏幕尺寸
                screen_width = root.winfo_screenwidth()
                screen_height = root.winfo_screenheight()
                
                print(f"\n窗口信息:")
                print(f"  几何信息: {geometry}")
                print(f"  窗口尺寸: {width} x {height}")
                print(f"  窗口位置: ({x}, {y})")
                print(f"  屏幕尺寸: {screen_width} x {screen_height}")
                
                # 计算窗口是否居中
                expected_x = (screen_width - width) // 2
                expected_y = (screen_height - height - 50) // 2  # 考虑任务栏
                
                print(f"  期望位置: ({expected_x}, {expected_y})")
                
                # 计算偏差
                x_diff = abs(x - expected_x)
                y_diff = abs(y - expected_y)
                
                print(f"  位置偏差: X轴 {x_diff}px, Y轴 {y_diff}px")
                
                # 判断是否居中（允许一定误差）
                tolerance = 50  # 50像素的容差
                is_centered = x_diff <= tolerance and y_diff <= tolerance
                
                print(f"  居中状态: {'✓ 已居中' if is_centered else '✗ 未居中'}")
                
                return is_centered
                
            except Exception as e:
                print(f"获取窗口信息失败: {e}")
                return False
        
        # 延迟检查窗口信息，确保居中操作完成
        def delayed_check():
            """延迟检查窗口状态"""
            print("\n=== 初始窗口状态 ===")
            is_centered_initial = print_window_info()
            
            # 再次延迟检查，确保增强居中方法执行完成
            def final_check():
                print("\n=== 最终窗口状态 ===")
                is_centered_final = print_window_info()
                
                # 测试结果
                print(f"\n=== 测试结果 ===")
                if is_centered_final:
                    print("✓ 窗口居中功能正常工作")
                else:
                    print("✗ 窗口居中功能存在问题")
                
                # 提供手动验证提示
                print(f"\n请手动验证:")
                print(f"1. 窗口是否在屏幕中央显示")
                print(f"2. 窗口是否完全可见（未被任务栏遮挡）")
                print(f"3. 在多显示器环境下，窗口是否在主显示器居中")
                
                # 添加关闭按钮测试
                def close_test():
                    print("测试完成，关闭窗口")
                    root.destroy()
                
                # 5秒后自动关闭（可以手动关闭）
                root.after(5000, close_test)
            
            # 1秒后进行最终检查
            root.after(1000, final_check)
        
        # 1秒后开始检查
        root.after(1000, delayed_check)
        
        print("窗口已创建，开始GUI事件循环...")
        print("窗口将在几秒后自动关闭，或者您可以手动关闭")
        
        # 启动GUI事件循环
        root.mainloop()
        
        print("测试完成")
        
    except ImportError as e:
        print(f"导入模块失败: {e}")
        print("请确保在项目根目录下运行此脚本")
        return False
    except Exception as e:
        print(f"测试过程中发生错误: {e}")
        return False
    
    return True

def test_basic_centering():
    """基础居中功能测试"""
    print("\n=== 基础居中功能测试 ===")
    
    # 创建简单的测试窗口
    root = tk.Tk()
    root.title("基础居中测试")
    root.geometry("800x600")
    
    # 模拟居中逻辑
    def center_window():
        root.update_idletasks()
        
        # 获取窗口和屏幕尺寸
        window_width = root.winfo_width()
        window_height = root.winfo_height()
        screen_width = root.winfo_screenwidth()
        screen_height = root.winfo_screenheight()
        
        # 计算居中位置
        x = (screen_width - window_width) // 2
        y = (screen_height - window_height - 50) // 2  # 考虑任务栏
        
        # 设置位置
        root.geometry(f"{window_width}x{window_height}+{x}+{y}")
        
        print(f"基础测试 - 窗口尺寸: {window_width}x{window_height}")
        print(f"基础测试 - 屏幕尺寸: {screen_width}x{screen_height}")
        print(f"基础测试 - 计算位置: ({x}, {y})")
    
    # 延迟居中
    root.after(100, center_window)
    
    # 添加标签
    label = tk.Label(root, text="基础居中测试\n\n这个窗口应该在屏幕中央显示", 
                     font=("Arial", 14), justify=tk.CENTER)
    label.pack(expand=True)
    
    # 添加关闭按钮
    close_btn = tk.Button(root, text="关闭", command=root.destroy)
    close_btn.pack(pady=10)
    
    # 3秒后自动关闭
    root.after(3000, root.destroy)
    
    root.mainloop()

if __name__ == "__main__":
    print("GStudio 窗口居中功能测试")
    print("=" * 50)
    
    # 首先运行基础测试
    test_basic_centering()
    
    # 然后运行完整测试
    success = test_window_centering()
    
    if success:
        print("\n所有测试完成")
    else:
        print("\n测试过程中遇到问题")
