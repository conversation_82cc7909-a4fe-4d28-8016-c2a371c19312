#!/usr/bin/env python3
"""
测试新发现的API端点
验证Material和Chapter相关的新API功能
"""

import sys
import os

# 添加simple-gstudio-api目录到Python路径
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'simple-gstudio-api'))

try:
    from gstudio_api import (
        APIEndpoints,
        VoiceCountChapterCuesParams,
        VoiceListCueParams,
        ChapterCuesEditorParams,
        VoiceDownloadParams,
        APIVersion
    )
    print("✓ 成功导入所有新增的类和参数")
except ImportError as e:
    print(f"✗ 导入失败: {e}")
    sys.exit(1)

def test_material_api_endpoints():
    """测试Material API端点"""
    print("\n测试Material API端点...")
    
    try:
        # 测试章节内容统计API
        voice_count_url = APIEndpoints.get_url(APIEndpoints.Material.VOICE_COUNT_CHAPTER_CUES)
        expected_count_url = "https://www.gstudios.com.cn/story_v2/api/material/voice/count/chapter/cues"
        assert voice_count_url == expected_count_url
        print(f"✓ 章节内容统计端点正确: {voice_count_url}")
        
        # 测试章节内容片录音列表API
        voice_list_url = APIEndpoints.get_url(APIEndpoints.Material.VOICE_LIST_CUE)
        expected_list_url = "https://www.gstudios.com.cn/story_v2/api/material/voice/list/cue"
        assert voice_list_url == expected_list_url
        print(f"✓ 章节内容片录音列表端点正确: {voice_list_url}")
        
        # 测试录音文件下载API
        voice_download_url = APIEndpoints.get_url(APIEndpoints.Material.VOICE_DOWNLOAD)
        expected_download_url = "https://www.gstudios.com.cn/story_v2/api/material/voice/download"
        assert voice_download_url == expected_download_url
        print(f"✓ 录音文件下载端点正确: {voice_download_url}")
        
    except Exception as e:
        print(f"✗ Material API端点测试失败: {e}")
        return False
    
    return True

def test_chapter_api_endpoints():
    """测试Chapter API端点"""
    print("\n测试Chapter API端点...")
    
    try:
        # 测试章节编辑器内容列表API
        chapter_cues_url = APIEndpoints.get_url(APIEndpoints.Chapter.CUES_LIST_EDITOR)
        expected_cues_url = "https://www.gstudios.com.cn/story_v2/api/content/chapter/cues/list/editor"
        assert chapter_cues_url == expected_cues_url
        print(f"✓ 章节编辑器内容列表端点正确: {chapter_cues_url}")
        
    except Exception as e:
        print(f"✗ Chapter API端点测试失败: {e}")
        return False
    
    return True

def test_parameter_classes():
    """测试参数类"""
    print("\n测试参数类...")
    
    try:
        # 测试VoiceCountChapterCuesParams
        voice_count_params = VoiceCountChapterCuesParams(chapterId=8607068)
        assert voice_count_params.chapterId == 8607068
        print(f"✓ VoiceCountChapterCuesParams创建成功: chapterId={voice_count_params.chapterId}")
        
        # 测试VoiceListCueParams
        voice_list_params = VoiceListCueParams(cueId=691699346, characterId=5530515)
        assert voice_list_params.cueId == 691699346
        assert voice_list_params.characterId == 5530515
        print(f"✓ VoiceListCueParams创建成功: cueId={voice_list_params.cueId}, characterId={voice_list_params.characterId}")
        
        # 测试ChapterCuesEditorParams
        chapter_cues_params = ChapterCuesEditorParams(chapterId=8607068)
        assert chapter_cues_params.chapterId == 8607068
        print(f"✓ ChapterCuesEditorParams创建成功: chapterId={chapter_cues_params.chapterId}")
        
        # 测试VoiceDownloadParams
        voice_download_params = VoiceDownloadParams(id=305346872)
        assert voice_download_params.id == 305346872
        print(f"✓ VoiceDownloadParams创建成功: id={voice_download_params.id}")
        
    except Exception as e:
        print(f"✗ 参数类测试失败: {e}")
        return False
    
    return True

def test_api_version_compatibility():
    """测试API版本兼容性"""
    print("\n测试API版本兼容性...")
    
    try:
        # 测试V2版本（默认）
        v2_material_url = APIEndpoints.get_url(APIEndpoints.Material.VOICE_COUNT_CHAPTER_CUES)
        assert "story_v2" in v2_material_url
        print(f"✓ V2版本Material API正确: {v2_material_url}")
        
        # 测试V1版本
        v1_material_url = APIEndpoints.get_url(APIEndpoints.Material.VOICE_COUNT_CHAPTER_CUES, APIVersion.V1)
        assert "story" in v1_material_url and "story_v2" not in v1_material_url
        print(f"✓ V1版本Material API正确: {v1_material_url}")
        
        # 测试Chapter API版本兼容性
        v2_chapter_url = APIEndpoints.get_url(APIEndpoints.Chapter.CUES_LIST_EDITOR)
        assert "story_v2" in v2_chapter_url
        print(f"✓ V2版本Chapter API正确: {v2_chapter_url}")
        
        v1_chapter_url = APIEndpoints.get_url(APIEndpoints.Chapter.CUES_LIST_EDITOR, APIVersion.V1)
        assert "story" in v1_chapter_url and "story_v2" not in v1_chapter_url
        print(f"✓ V1版本Chapter API正确: {v1_chapter_url}")
        
    except Exception as e:
        print(f"✗ API版本兼容性测试失败: {e}")
        return False
    
    return True

def test_consistency_with_api_log():
    """测试与API调用记录的一致性"""
    print("\n测试与API调用记录的一致性...")
    
    try:
        # 验证参数值与实际API调用记录一致
        
        # 1. 章节内容统计API - chapterId=8607068
        voice_count_params = VoiceCountChapterCuesParams(chapterId=8607068)
        assert voice_count_params.chapterId == 8607068
        print("✓ 章节内容统计API参数与调用记录一致")
        
        # 2. 章节内容片录音列表API - cueId=691699346, characterId=5530515
        voice_list_params = VoiceListCueParams(cueId=691699346, characterId=5530515)
        assert voice_list_params.cueId == 691699346
        assert voice_list_params.characterId == 5530515
        print("✓ 章节内容片录音列表API参数与调用记录一致")
        
        # 3. 章节编辑器内容列表API - chapterId=8607068
        chapter_cues_params = ChapterCuesEditorParams(chapterId=8607068)
        assert chapter_cues_params.chapterId == 8607068
        print("✓ 章节编辑器内容列表API参数与调用记录一致")
        
        # 4. 录音文件下载API - id=305346872
        voice_download_params = VoiceDownloadParams(id=305346872)
        assert voice_download_params.id == 305346872
        print("✓ 录音文件下载API参数与调用记录一致")
        
        # 验证API路径与调用记录一致
        paths = {
            APIEndpoints.Material.VOICE_COUNT_CHAPTER_CUES: "/material/voice/count/chapter/cues",
            APIEndpoints.Material.VOICE_LIST_CUE: "/material/voice/list/cue",
            APIEndpoints.Material.VOICE_DOWNLOAD: "/material/voice/download",
            APIEndpoints.Chapter.CUES_LIST_EDITOR: "/content/chapter/cues/list/editor"
        }
        
        for endpoint, expected_path in paths.items():
            assert endpoint == expected_path, f"路径不匹配: {endpoint} != {expected_path}"
        
        print("✓ 所有API路径与调用记录完全一致")
        
    except Exception as e:
        print(f"✗ 与API调用记录一致性测试失败: {e}")
        return False
    
    return True

def main():
    """主测试函数"""
    print("开始测试新发现的API端点...")
    
    tests = [
        test_material_api_endpoints,
        test_chapter_api_endpoints,
        test_parameter_classes,
        test_api_version_compatibility,
        test_consistency_with_api_log
    ]
    
    passed = 0
    total = len(tests)
    
    for test in tests:
        if test():
            passed += 1
        else:
            print(f"✗ 测试失败: {test.__name__}")
    
    print(f"\n测试结果: {passed}/{total} 通过")
    
    if passed == total:
        print("🎉 所有测试通过！新API端点更新成功。")
        return True
    else:
        print("❌ 部分测试失败，请检查代码。")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
