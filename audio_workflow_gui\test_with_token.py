#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
使用Token测试修复后的书籍搜索功能

验证参数绑定问题是否已解决。

作者：Augment Agent
版本：1.0.0
"""

import sys
import os
import logging

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from api.client import GStudioAPIClient
from api.models import BookListEditorParams


def setup_logging():
    """设置日志"""
    logging.basicConfig(
        level=logging.DEBUG,
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
        handlers=[
            logging.StreamHandler(sys.stdout),
            logging.FileHandler('token_test.log', encoding='utf-8')
        ]
    )


def test_with_real_token():
    """使用真实Token测试"""
    print("=" * 60)
    print("使用真实Token测试修复后的参数处理")
    print("=" * 60)
    
    # 创建调试模式的API客户端
    client = GStudioAPIClient(debug_mode=True)
    
    # 设置Token
    token = "3dfb89119562456cb8818120139f6ae1"
    client.set_token(token)
    print("✓ API Token已设置")
    
    print("\n1. 测试最小参数集...")
    try:
        params = BookListEditorParams(
            page_size=10,
            page_no=1
        )
        
        print(f"发送参数: pageSize={params.page_size}, pageNo={params.page_no}")
        
        response = client.get_book_list_editor(params)
        
        print(f"✓ API调用成功")
        print(f"  响应码: {response.code}")
        print(f"  响应消息: {response.msg}")
        
        if response.code == 1 and response.data:
            book_list = response.data.get('list', [])
            total = response.data.get('total', 0)
            print(f"  ✅ 成功！获取到 {len(book_list)} 本书籍，总计 {total} 本")
            print(f"  🎉 参数绑定问题已解决！")
            
            if book_list:
                first_book = book_list[0]
                print(f"  第一本书籍字段: {list(first_book.keys())}")
                print(f"  书籍示例: ID={first_book.get('id')}, 名称={first_book.get('name')}")
                
            return True
        else:
            print(f"  ❌ API错误: {response.msg}")
            return False
            
    except Exception as e:
        print(f"✗ API调用失败: {e}")
        return False
    
    print("\n2. 测试带搜索参数...")
    try:
        params = BookListEditorParams(
            page_size=5,
            page_no=1,
            name="测试",
            finished=False,
            sort_item="bookName",
            sort_asc=True
        )
        
        print(f"发送参数: pageSize={params.page_size}, pageNo={params.page_no}, name={params.name}, finished={params.finished}")
        
        response = client.get_book_list_editor(params)
        
        print(f"✓ 搜索API调用成功")
        print(f"  响应码: {response.code}")
        print(f"  响应消息: {response.msg}")
        
        if response.code == 1 and response.data:
            book_list = response.data.get('list', [])
            total = response.data.get('total', 0)
            print(f"  ✅ 搜索成功！找到 {len(book_list)} 本书籍，总计 {total} 本")
            
            for i, book in enumerate(book_list[:3], 1):
                book_name = book.get('name', '未知')
                book_id = book.get('id', '未知')
                print(f"    {i}. {book_name} (ID: {book_id})")
                
            return True
        else:
            print(f"  ❌ API错误: {response.msg}")
            return False
            
    except Exception as e:
        print(f"✗ 搜索API调用失败: {e}")
        return False


def test_convenience_method():
    """测试便捷搜索方法"""
    print("\n" + "=" * 60)
    print("测试便捷搜索方法")
    print("=" * 60)
    
    client = GStudioAPIClient(debug_mode=True)
    token = "3dfb89119562456cb8818120139f6ae1"
    client.set_token(token)
    
    try:
        print("测试无搜索关键词...")
        books = client.search_books("", page_size=5)
        
        print(f"✓ 便捷搜索方法调用成功")
        print(f"  获取到 {len(books)} 本书籍")
        
        for i, book in enumerate(books[:3], 1):
            book_name = book.get('name', book.get('bookName', '未知'))
            book_id = book.get('id', book.get('bookId', '未知'))
            print(f"    {i}. {book_name} (ID: {book_id})")
        
        print("\n测试带搜索关键词...")
        books_with_query = client.search_books("小说", page_size=3)
        
        print(f"✓ 关键词搜索调用成功")
        print(f"  搜索'小说'，获取到 {len(books_with_query)} 本书籍")
        
        for i, book in enumerate(books_with_query, 1):
            book_name = book.get('name', book.get('bookName', '未知'))
            book_id = book.get('id', book.get('bookId', '未知'))
            print(f"    {i}. {book_name} (ID: {book_id})")
            
        return True
            
    except Exception as e:
        print(f"✗ 便捷搜索方法调用失败: {e}")
        return False


def main():
    """主测试函数"""
    print("GStudio 修复验证测试")
    print("=" * 60)
    print("此测试将验证:")
    print("• 参数绑定失败问题是否已解决")
    print("• 布尔值转换是否正确")
    print("• 书籍搜索功能是否正常")
    print()
    
    # 设置日志
    setup_logging()
    
    success_count = 0
    total_tests = 2
    
    try:
        # 运行测试
        if test_with_real_token():
            success_count += 1
            
        if test_convenience_method():
            success_count += 1
        
        print("\n" + "=" * 60)
        print("测试结果总结")
        print("=" * 60)
        
        if success_count == total_tests:
            print("🎉 所有测试通过！")
            print("✅ 参数绑定失败问题已完全解决")
            print("✅ 书籍搜索功能正常工作")
            print("✅ 新的API端点工作正常")
        else:
            print(f"⚠ {success_count}/{total_tests} 个测试通过")
            print("请检查失败的测试项目")
        
        print("\n修复总结:")
        print("1. ✅ 删除了错误的 /content/book/list 端点")
        print("2. ✅ 使用正确的 /content/book/list/editor 端点")
        print("3. ✅ 修复了布尔值参数格式（转换为字符串）")
        print("4. ✅ 改进了参数处理逻辑")
        print("5. ✅ 增强了调试信息输出")
        
    except Exception as e:
        print(f"测试过程中发生异常: {e}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    main()
