#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
段落处理模块

实现段落获取和智能筛选，自动识别'旁白'且朗读者为'机器人'的段落。

作者：Augment Agent
版本：1.0.0
"""

import tkinter as tk
from tkinter import ttk, messagebox
import threading
from typing import List, Dict, Any, Optional

from utils.logger import LoggerMixin
from api.models import ChapterCuesEditorParams, CharacterListChapterParams
from utils.table_styles import setup_striped_table, refresh_table_stripes


class ParagraphProcessorFrame(ttk.Frame, LoggerMixin):
    """段落处理框架"""
    
    def __init__(self, parent, main_window):
        """
        初始化段落处理框架
        
        Args:
            parent: 父窗口
            main_window: 主窗口实例
        """
        super().__init__(parent)
        self.main_window = main_window
        self.all_paragraphs = []
        self.filtered_paragraphs = []
        self.chapter_characters = []  # 当前章节的角色列表
        self.character_loading = False  # 角色加载状态
        
        self._create_widgets()
        self._setup_layout()
        self._bind_events()
        
        self.logger.info("段落处理模块初始化完成")
    
    def _create_widgets(self):
        """创建界面组件"""
        # 移除章节信息区域（信息现在显示在工具栏中）
        
        # 移除段落加载区域（按钮将移动到角色筛选区域）
        
        # 筛选配置区域
        filter_frame = ttk.LabelFrame(self, text="角色筛选", padding=10)

        # 筛选选项
        filter_options_frame = ttk.Frame(filter_frame)

        # 角色筛选
        ttk.Label(filter_options_frame, text="选择角色:").grid(row=0, column=0, sticky=tk.W, padx=(0, 5))
        self.character_var = tk.StringVar(value="全部角色")
        self.character_combo = ttk.Combobox(
            filter_options_frame,
            textvariable=self.character_var,
            values=["全部角色"],
            state="readonly",
            width=25
        )
        self.character_combo.grid(row=0, column=1, padx=(0, 10))

        # 加载角色按钮
        self.reload_characters_button = ttk.Button(
            filter_options_frame,
            text="加载角色",
            command=self._load_chapter_characters
        )
        self.reload_characters_button.grid(row=0, column=2, padx=(10, 5))

        # 自动筛选选项（移动到重新加载角色和加载章节段落按钮之间）
        self.auto_filter_var = tk.BooleanVar(value=True)
        self.auto_filter_check = ttk.Checkbutton(
            filter_options_frame,
            text="自动筛选",
            variable=self.auto_filter_var
        )
        self.auto_filter_check.grid(row=0, column=3, padx=(5, 5))

        # 加载段落按钮
        self.load_paragraphs_button = ttk.Button(
            filter_options_frame,
            text="加载段落",
            command=self._load_paragraphs
        )
        self.load_paragraphs_button.grid(row=0, column=4, padx=(5, 5))

        # 预览筛选结果按钮（移动到加载章节段落按钮右侧）
        self.preview_button = ttk.Button(
            filter_options_frame,
            text="预览筛选结果",
            command=self._preview_filtered,
            state=tk.DISABLED
        )
        self.preview_button.grid(row=0, column=5, padx=(5, 5))

        # 确认并继续按钮（移动到预览筛选结果按钮右侧）
        self.confirm_button = ttk.Button(
            filter_options_frame,
            text="确认并继续",
            command=self._confirm_selection,
            state=tk.DISABLED
        )
        self.confirm_button.grid(row=0, column=6, padx=(5, 0))
        
        # 段落列表区域
        paragraphs_frame = ttk.LabelFrame(self, text="段落列表", padding=10)
        
        # 创建Notebook显示原始和筛选后的段落
        self.paragraphs_notebook = ttk.Notebook(paragraphs_frame)
        
        # 原始段落标签页
        self.all_paragraphs_frame = ttk.Frame(self.paragraphs_notebook)
        self.paragraphs_notebook.add(self.all_paragraphs_frame, text="所有段落")
        
        # 筛选后段落标签页
        self.filtered_paragraphs_frame = ttk.Frame(self.paragraphs_notebook)
        self.paragraphs_notebook.add(self.filtered_paragraphs_frame, text="筛选结果")
        
        # 创建两个Treeview
        self._create_paragraph_tree(self.all_paragraphs_frame, "all")
        self._create_paragraph_tree(self.filtered_paragraphs_frame, "filtered")
        
        # 统计信息区域
        stats_frame = ttk.Frame(self)

        self.stats_label = ttk.Label(stats_frame, text="统计信息: 未加载")
        self.stats_label.pack(side=tk.LEFT)
        
        # 移除操作按钮区域（按钮已移动到角色筛选区域）
        
        # 保存组件引用
        self.filter_frame = filter_frame
        self.filter_options_frame = filter_options_frame
        self.paragraphs_frame = paragraphs_frame
        self.stats_frame = stats_frame
    
    def _create_paragraph_tree(self, parent, tree_type):
        """创建段落树形视图"""
        # 创建Treeview - 只显示3列：序号、角色名称、内容预览（移除段落ID列）
        columns = ("seq_num", "character", "text_preview")
        tree = ttk.Treeview(parent, columns=columns, show="headings", height=12)

        # 设置列标题
        tree.heading("seq_num", text="序号")
        tree.heading("character", text="角色名称")
        tree.heading("text_preview", text="内容预览")

        # 设置列宽（重新调整以提供更好的内容预览显示）
        tree.column("seq_num", width=80, minwidth=60)  # 增加序号列宽
        tree.column("character", width=150, minwidth=120)  # 增加角色名称列宽
        tree.column("text_preview", width=500, minwidth=300)  # 增加内容预览列宽
        
        # 添加滚动条
        scrollbar_y = ttk.Scrollbar(parent, orient=tk.VERTICAL, command=tree.yview)
        scrollbar_x = ttk.Scrollbar(parent, orient=tk.HORIZONTAL, command=tree.xview)
        tree.configure(yscrollcommand=scrollbar_y.set, xscrollcommand=scrollbar_x.set)
        
        # 布局
        tree.grid(row=0, column=0, sticky="nsew")
        scrollbar_y.grid(row=0, column=1, sticky="ns")
        scrollbar_x.grid(row=1, column=0, sticky="ew")

        parent.grid_rowconfigure(0, weight=1)
        parent.grid_columnconfigure(0, weight=1)

        # 应用条纹化样式美化表格
        setup_striped_table(tree, auto_update=True)

        # 保存引用
        if tree_type == "all":
            self.all_paragraphs_tree = tree
        else:
            self.filtered_paragraphs_tree = tree
    
    def _setup_layout(self):
        """设置布局"""
        self.filter_frame.pack(fill=tk.X, pady=(0, 10))
        self.filter_options_frame.pack(fill=tk.X)
        
        self.paragraphs_frame.pack(fill=tk.BOTH, expand=True, pady=(0, 10))
        self.paragraphs_notebook.pack(fill=tk.BOTH, expand=True)

        self.stats_frame.pack(fill=tk.X, pady=(0, 10))
    
    def _bind_events(self):
        """绑定事件"""
        # 标签页激活时更新章节信息
        self.bind("<Visibility>", self._on_visibility_changed)

        # 段落树选择事件
        self.all_paragraphs_tree.bind("<<TreeviewSelect>>", self._on_paragraph_select)
        self.filtered_paragraphs_tree.bind("<<TreeviewSelect>>", self._on_paragraph_select)

        # 角色选择变化事件
        self.character_combo.bind("<<ComboboxSelected>>", self._on_character_changed)
    
    def _on_visibility_changed(self, event):
        """标签页可见性改变事件"""
        if event.widget == self:
            self._update_chapter_info()
    
    def _on_paragraph_select(self, event):
        """段落选择事件处理"""
        # 这里可以添加段落选择的处理逻辑
        pass
    
    def _update_chapter_info(self):
        """更新章节信息显示"""
        workflow_state = self.main_window.get_workflow_state()
        selected_chapter = workflow_state.get('selected_chapter')

        if selected_chapter:
            # 启用加载按钮
            self.load_paragraphs_button.config(state=tk.NORMAL)
        else:
            # 禁用加载按钮
            self.load_paragraphs_button.config(state=tk.DISABLED)
    
    def _load_paragraphs(self):
        """加载章节段落"""
        workflow_state = self.main_window.get_workflow_state()
        selected_chapter = workflow_state.get('selected_chapter')
        
        if not selected_chapter:
            messagebox.showwarning("提示", "请先选择章节")
            return
        
        chapter_id = selected_chapter.get('chapterId')
        if not chapter_id:
            messagebox.showerror("错误", "章节ID无效")
            return
        
        self.main_window.update_status("正在加载章节段落...")
        self.load_paragraphs_button.config(state=tk.DISABLED)
        
        def load_thread():
            try:
                # 获取API客户端
                api_client = self._get_api_client()
                if not api_client:
                    return
                
                # 获取章节内容片
                params = ChapterCuesEditorParams(chapterId=int(chapter_id))
                response = api_client.get_chapter_cues(params)
                
                cues_data = response.data.get('cues', [])
                
                # 在主线程中更新UI
                self.after(0, lambda: self._update_paragraphs_list(cues_data))
                
            except Exception as e:
                self.logger.error(f"加载段落失败: {e}")
                self.after(0, lambda: self._handle_load_error(str(e)))
        
        threading.Thread(target=load_thread, daemon=True).start()

    def _load_chapter_characters(self):
        """加载章节角色列表"""
        workflow_state = self.main_window.get_workflow_state()
        selected_chapter = workflow_state.get('selected_chapter')

        if not selected_chapter:
            self._update_character_combo([])
            return

        chapter_id = selected_chapter.get('chapterId')
        if not chapter_id:
            self._update_character_combo([])
            return

        # 防止重复加载
        if self.character_loading:
            return

        self.character_loading = True
        self.main_window.update_status("正在加载角色...")
        self.reload_characters_button.config(state=tk.DISABLED)

        def load_characters_thread():
            try:
                # 获取API客户端
                api_client = self._get_api_client()
                if not api_client:
                    self.after(0, lambda: self._handle_character_load_error("无法获取API客户端"))
                    return

                # 获取章节角色列表
                params = CharacterListChapterParams(
                    chapter_id=int(chapter_id),
                    role_type="chapter",
                    skip_stat=True
                )
                response = api_client.get_chapter_characters(params)

                if response.success:
                    # response.data 直接是角色列表数组，不是包含'list'键的对象
                    characters_data = response.data if response.data else []
                    # 在主线程中更新UI
                    self.after(0, lambda: self._update_character_combo(characters_data))
                else:
                    error_msg = response.error or "获取角色列表失败"
                    self.after(0, lambda: self._handle_character_load_error(error_msg))

            except Exception as e:
                self.logger.error(f"加载角色列表失败: {e}")
                self.after(0, lambda: self._handle_character_load_error(str(e)))

        threading.Thread(target=load_characters_thread, daemon=True).start()

    def _update_character_combo(self, characters_data: List[Dict[str, Any]]):
        """更新角色下拉框"""
        self.character_loading = False
        self.reload_characters_button.config(state=tk.NORMAL)

        # 保存角色数据
        self.chapter_characters = characters_data

        # 构建角色选项列表
        character_options = ["全部角色"]

        if characters_data:
            # 按角色名称排序
            sorted_characters = sorted(characters_data, key=lambda x: x.get('name', ''))

            for character in sorted_characters:
                character_name = character.get('name', '未知角色')
                character_id = character.get('id', 0)
                # 格式：角色名称 (ID: xxx)
                option_text = f"{character_name} (ID: {character_id})"
                character_options.append(option_text)

            self.main_window.update_status(f"已加载 {len(characters_data)} 个角色")
        else:
            self.main_window.update_status("该章节暂无角色")

        # 更新下拉框选项
        self.character_combo['values'] = character_options

        # 重置选择为"全部角色"
        self.character_var.set("全部角色")

        self.logger.info(f"已加载 {len(characters_data)} 个角色")

    def _handle_character_load_error(self, error_message: str):
        """处理角色加载错误"""
        self.character_loading = False
        self.reload_characters_button.config(state=tk.NORMAL)
        self.main_window.update_status("角色加载失败")

        # 重置角色列表
        self.chapter_characters = []
        self.character_combo['values'] = ["全部角色"]
        self.character_var.set("全部角色")

        self.logger.error(f"加载角色列表失败: {error_message}")

        # 显示错误提示（可选，避免过于频繁的弹窗）
        if "网络" in error_message or "连接" in error_message:
            # 对于网络错误，只在状态栏显示，不弹窗
            self.main_window.update_status(f"角色加载失败: {error_message}")
        else:
            # 对于其他错误，可以考虑弹窗提示
            messagebox.showwarning("角色加载失败", f"无法加载角色列表:\n{error_message}")

    def _on_character_changed(self, event):
        """角色选择变化事件处理"""
        # 如果有段落数据，自动应用筛选
        if self.all_paragraphs:
            self._apply_filter()

        # 通知音频生成器更新角色配置
        try:
            if hasattr(self.main_window, 'audio_generator_frame'):
                self.main_window.audio_generator_frame.update_character_config()
        except Exception as e:
            self.logger.error(f"通知音频生成器更新角色配置失败: {e}")

    def _get_character_name_by_id(self, character_id: int) -> str:
        """根据角色ID获取角色名称"""
        if not character_id:
            return "未知角色"

        # 在已加载的角色列表中查找
        for character in self.chapter_characters:
            if character.get('id') == character_id:
                return character.get('name', '未知角色')

        # 如果没找到，返回ID信息
        return f"角色ID:{character_id}"

    def _get_selected_character_id(self) -> Optional[int]:
        """获取当前选择的角色ID"""
        selected_character = self.character_var.get()

        if selected_character == "全部角色":
            return None

        # 从选择文本中提取角色ID
        # 格式：角色名称 (ID: xxx)
        try:
            if "(ID: " in selected_character and ")" in selected_character:
                start_idx = selected_character.find("(ID: ") + 5
                end_idx = selected_character.find(")", start_idx)
                character_id_str = selected_character[start_idx:end_idx]
                return int(character_id_str)
        except (ValueError, IndexError):
            pass

        return None

    def get_selected_character_info(self) -> Optional[Dict[str, Any]]:
        """获取当前选择的角色完整信息"""
        selected_character_id = self._get_selected_character_id()

        if selected_character_id is None:
            return None

        # 在已加载的角色列表中查找完整信息
        for character in self.chapter_characters:
            if character.get('id') == selected_character_id:
                return character

        return None

    def _update_paragraph_character_names(self):
        """更新段落中的角色名称"""
        if not self.chapter_characters:
            return

        for paragraph in self.all_paragraphs:
            character_id = paragraph.get('character_id', 0)
            if character_id:
                character_name = self._get_character_name_by_id(character_id)
                paragraph['character_name'] = character_name
    
    def _update_paragraphs_list(self, cues_data: List[Dict[str, Any]]):
        """更新段落列表"""
        # 清空现有数据
        for item in self.all_paragraphs_tree.get_children():
            self.all_paragraphs_tree.delete(item)
        
        # 处理段落数据
        self.all_paragraphs = []
        for cue in cues_data:
            paragraph = self._process_cue_data(cue)
            if paragraph:
                self.all_paragraphs.append(paragraph)

        # 按序号排序
        self.all_paragraphs.sort(key=lambda x: x.get('seq_num', 0))

        # 重新分配连续序号（从1开始）
        for index, paragraph in enumerate(self.all_paragraphs, 1):
            paragraph['display_seq_num'] = index
        
        # 更新段落中的角色名称（如果角色列表已加载）
        self._update_paragraph_character_names()

        # 显示所有段落
        for paragraph in self.all_paragraphs:
            self._insert_paragraph_to_tree(self.all_paragraphs_tree, paragraph)

        # 更新统计信息
        self._update_stats()

        # 刷新所有段落表格的条纹化效果
        refresh_table_stripes(self.all_paragraphs_tree)

        # 如果启用了自动筛选，则应用筛选
        if self.auto_filter_var.get():
            self._apply_filter()

        # 恢复按钮状态
        self.load_paragraphs_button.config(state=tk.NORMAL)
        
        count = len(self.all_paragraphs)
        self.main_window.update_status(f"加载了 {count} 个段落")
        self.logger.info(f"段落加载完成，共 {count} 个段落")
    
    def _process_cue_data(self, cue: Dict[str, Any]) -> Optional[Dict[str, Any]]:
        """处理段落数据"""
        try:
            # 提取关键信息
            cue_id = cue.get('id', 0)
            seq_num = cue.get('seqNum', 0)
            character_id = cue.get('characterId', 0)
            cue_type = cue.get('type', 0)  # 获取TYPE字段

            # 根据TYPE字段值实现不同的显示逻辑
            character_name, text_preview = self._get_display_content_by_type(cue, cue_type, character_id)

            # 优先使用SSML字段，如果不存在则使用text字段
            ssml_content = cue.get('ssml', '').strip()
            text_content = cue.get('text', '').strip()
            display_text = ssml_content if ssml_content else text_content

            return {
                'cue_id': cue_id,
                'seq_num': seq_num,
                'display_seq_num': 0,  # 将在后续重新分配
                'text': display_text,
                'text_preview': text_preview,
                'character_id': character_id,
                'character_name': character_name,
                'type': cue_type,
                'raw_data': cue  # 保留原始数据，包含SSML和text字段
            }

        except Exception as e:
            self.logger.error(f"处理段落数据失败: {e}")
            return None

    def _get_display_content_by_type(self, cue: Dict[str, Any], cue_type: int, character_id: int) -> tuple:
        """根据TYPE字段值获取显示内容"""
        try:
            if cue_type == 0:
                # TYPE = 0：角色名称显示"参考"，内容预览显示text字段内容
                character_name = "参考"
                text = cue.get('text', '')
                text_preview = self._format_text_preview(text)

            elif cue_type == 1:
                # TYPE = 1：角色名称显示"静音"，内容预览显示durationMs字段转换为"X秒"格式
                character_name = "静音"
                duration_ms = cue.get('durationMs', 0)
                duration_seconds = duration_ms / 1000 if duration_ms else 0
                text_preview = f"{duration_seconds:.1f}秒"

            elif cue_type == 2:
                # TYPE = 2：角色名称显示实际角色名称，内容预览显示SSML字段内容
                character_name = self._get_character_name_by_id(character_id)
                ssml = cue.get('ssml', '') or cue.get('text', '')
                text_preview = self._format_text_preview(ssml)

            elif cue_type == 3:
                # TYPE = 3：根据action字段值显示音效状态
                action = cue.get('action', 0)
                if action == 0:
                    character_name = "音效开始"
                elif action == 1:
                    character_name = "音效结束"
                else:
                    character_name = "音效"

                material_name = cue.get('materialItemName', '')
                text_preview = self._format_text_preview(material_name)

            elif cue_type == 4:
                # TYPE = 4：根据action字段值显示音乐状态
                action = cue.get('action', 0)
                if action == 0:
                    character_name = "音乐开始"
                elif action == 1:
                    character_name = "音乐结束"
                else:
                    character_name = "音乐"

                material_name = cue.get('materialItemName', '')
                text_preview = self._format_text_preview(material_name)

            else:
                # 未知类型，使用默认逻辑
                character_name = self._get_character_name_by_id(character_id)
                text = cue.get('text', '') or cue.get('ssml', '')
                text_preview = self._format_text_preview(text)

            return character_name, text_preview

        except Exception as e:
            self.logger.error(f"处理TYPE={cue_type}的显示内容失败: {e}")
            # 返回默认值
            return self._get_character_name_by_id(character_id), "处理失败"

    def _format_text_preview(self, text: str) -> str:
        """格式化文本预览（限制50个字符）"""
        if not text:
            return ""

        text_preview = text.strip()
        if len(text_preview) > 50:
            text_preview = text_preview[:50] + "..."

        return text_preview
    

    
    def _insert_paragraph_to_tree(self, tree: ttk.Treeview, paragraph: Dict[str, Any]):
        """向树形视图插入段落"""
        # 新的列结构：序号、角色名称、内容预览（移除段落ID列）
        tree.insert("", tk.END, values=(
            paragraph.get('display_seq_num', paragraph.get('seq_num', 0)),
            paragraph['character_name'],
            paragraph['text_preview']
        ))
    
    def _apply_filter(self):
        """应用筛选条件"""
        if not self.all_paragraphs:
            return  # 如果没有段落数据，静默返回

        selected_character_id = self._get_selected_character_id()

        # 清空筛选结果树
        for item in self.filtered_paragraphs_tree.get_children():
            self.filtered_paragraphs_tree.delete(item)

        # 应用角色筛选条件
        self.filtered_paragraphs = []
        for paragraph in self.all_paragraphs:
            # 检查角色筛选
            character_match = True
            if selected_character_id is not None:
                character_match = paragraph['character_id'] == selected_character_id

            # 角色匹配则添加到筛选结果
            if character_match:
                self.filtered_paragraphs.append(paragraph)
                self._insert_paragraph_to_tree(self.filtered_paragraphs_tree, paragraph)
        
        # 更新统计信息
        self._update_stats()

        # 刷新筛选结果表格的条纹化效果
        refresh_table_stripes(self.filtered_paragraphs_tree)

        # 更新主窗口工具栏中的段落信息显示
        if self.filtered_paragraphs:
            total_chars = sum(len(p.get('text', '')) for p in self.filtered_paragraphs)
            self.main_window.update_paragraph_info_display(len(self.filtered_paragraphs), total_chars)
        else:
            self.main_window.update_paragraph_info_display(0, 0)

        # 启用操作按钮
        if self.filtered_paragraphs:
            self.preview_button.config(state=tk.NORMAL)
            self.confirm_button.config(state=tk.NORMAL)
        
        # 切换到筛选结果标签页
        self.paragraphs_notebook.select(1)
        
        count = len(self.filtered_paragraphs)

        # 构建筛选条件描述
        if selected_character_id is not None:
            character_name = self._get_character_name_by_id(selected_character_id)
            filter_desc = f"角色:{character_name}"
        else:
            filter_desc = "角色:全部"

        status_msg = f"筛选完成，找到 {count} 个匹配段落 ({filter_desc})"
        self.main_window.update_status(status_msg)
        self.logger.info(status_msg)
    
    def _update_stats(self):
        """更新统计信息"""
        total_count = len(self.all_paragraphs)
        filtered_count = len(self.filtered_paragraphs)

        stats_text = f"总段落: {total_count}, 筛选结果: {filtered_count}"
        if total_count > 0:
            percentage = (filtered_count / total_count) * 100
            stats_text += f" ({percentage:.1f}%)"

        self.stats_label.config(text=f"统计信息: {stats_text}")
    
    def _preview_filtered(self):
        """预览筛选结果"""
        if not self.filtered_paragraphs:
            messagebox.showwarning("提示", "没有筛选结果")
            return
        
        # 创建预览窗口
        preview_window = tk.Toplevel(self)
        preview_window.title("筛选结果预览")
        preview_window.geometry("800x600")
        
        # 创建文本框显示预览
        text_frame = ttk.Frame(preview_window)
        text_frame.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)
        
        preview_text = tk.Text(text_frame, wrap=tk.WORD)
        scrollbar = ttk.Scrollbar(text_frame, orient=tk.VERTICAL, command=preview_text.yview)
        preview_text.configure(yscrollcommand=scrollbar.set)
        
        preview_text.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        scrollbar.pack(side=tk.RIGHT, fill=tk.Y)
        
        # 填充预览内容
        preview_content = f"筛选结果预览 (共 {len(self.filtered_paragraphs)} 个段落)\n"
        preview_content += "=" * 50 + "\n\n"
        
        for i, paragraph in enumerate(self.filtered_paragraphs, 1):
            preview_content += f"{i}. 段落ID: {paragraph.get('cue_id', '未知')}\n"
            preview_content += f"   序号: {paragraph.get('seq_num', 0)}\n"
            preview_content += f"   角色: {paragraph.get('character_name', '未知角色')}\n"
            preview_content += f"   角色ID: {paragraph.get('character_id', 0)}\n"
            preview_content += f"   内容: {paragraph.get('text', '无内容')}\n"
            preview_content += "-" * 30 + "\n\n"
        
        preview_text.insert(1.0, preview_content)
        preview_text.config(state=tk.DISABLED)
        
        # 关闭按钮
        close_button = ttk.Button(preview_window, text="关闭", command=preview_window.destroy)
        close_button.pack(pady=10)
    
    def _confirm_selection(self):
        """确认选择并继续"""
        if not self.filtered_paragraphs:
            messagebox.showwarning("提示", "没有筛选结果")
            return
        
        # 更新工作流状态
        self.main_window.update_workflow_state('filtered_paragraphs', self.filtered_paragraphs)
        
        # 切换到下一步
        self.main_window.next_step()
        
        count = len(self.filtered_paragraphs)
        self.logger.info(f"已确认选择 {count} 个段落，进入音频生成步骤")
    
    def _handle_load_error(self, error_message: str):
        """处理加载错误"""
        messagebox.showerror("加载失败", f"加载段落时发生错误:\n{error_message}")
        self.main_window.update_status("加载失败")
        
        # 恢复按钮状态
        self.load_paragraphs_button.config(state=tk.NORMAL)
    
    def _get_api_client(self):
        """获取API客户端"""
        # 从书籍搜索模块获取API客户端
        book_search_frame = self.main_window.book_search_frame
        if hasattr(book_search_frame, 'api_client') and book_search_frame.api_client:
            return book_search_frame.api_client
        
        messagebox.showerror("错误", "API客户端未初始化，请先在书籍搜索页面进行搜索")
        return None
