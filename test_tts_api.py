#!/usr/bin/env python3
"""
测试TTS API更新的脚本
验证新添加的TTS相关功能是否正确
"""

import sys
import os

# 添加simple-gstudio-api目录到Python路径
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'simple-gstudio-api'))

try:
    from gstudio_api import (
        APIEndpoints,
        TTSTrialParams,
        GenMode,
        APIVersion
    )
    print("✓ 成功导入所有新增的类和枚举")
except ImportError as e:
    print(f"✗ 导入失败: {e}")
    sys.exit(1)

def test_tts_trial_params():
    """测试TTS试听参数类"""
    print("\n测试TTSTrialParams类...")
    
    # 测试基本参数创建
    try:
        params = TTSTrialParams(
            bookId="39726",
            cvRobotId=568,
            ssml="测试文本"
        )
        print("✓ 基本参数创建成功")
        print(f"  bookId: {params.bookId}")
        print(f"  cvRobotId: {params.cvRobotId}")
        print(f"  ssml: {params.ssml}")
        print(f"  genMode: {params.genMode}")
        print(f"  speedFactor: {params.speedFactor}")
        print(f"  durationFactorSilence: {params.durationFactorSilence}")
        print(f"  seed: {params.seed}")
    except Exception as e:
        print(f"✗ 基本参数创建失败: {e}")
        return False
    
    # 测试完整参数创建（与实际API调用记录一致）
    try:
        params_full = TTSTrialParams(
            bookId="39726",
            cvRobotId=568,
            ssml="在一道斥责声中，林止陌迷迷糊糊的抬起头，一道模糊的窈窕身影，慢慢的在他眼前变得清晰起来。",
            genMode=GenMode.DEFAULT,
            speedFactor=100,
            durationFactorSilence=100,
            seed=None
        )
        print("✓ 完整参数创建成功（与API调用记录一致）")
    except Exception as e:
        print(f"✗ 完整参数创建失败: {e}")
        return False
    
    return True

def test_gen_mode_enum():
    """测试GenMode枚举"""
    print("\n测试GenMode枚举...")
    
    try:
        assert GenMode.DEFAULT == 0
        print("✓ GenMode.DEFAULT = 0")
        
        # 测试枚举值与实际API调用记录一致
        assert GenMode.DEFAULT.value == 0
        print("✓ GenMode枚举值正确")
    except Exception as e:
        print(f"✗ GenMode枚举测试失败: {e}")
        return False
    
    return True

def test_api_endpoints():
    """测试API端点"""
    print("\n测试API端点...")

    try:
        # 测试TTS试听端点
        print("  测试TTS试听端点...")
        tts_url = APIEndpoints.get_url(APIEndpoints.Record.TTS_TRIAL)
        expected_url = "https://www.gstudios.com.cn/story_v2/api/record/tts/trial"
        print(f"  实际URL: {tts_url}")
        print(f"  期望URL: {expected_url}")
        assert tts_url == expected_url, f"TTS URL不匹配: 期望 {expected_url}, 实际 {tts_url}"
        print(f"✓ TTS试听端点正确: {tts_url}")

        # 测试其他Record端点
        print("  测试章节CV信息端点...")
        cv_info_url = APIEndpoints.get_url(APIEndpoints.Record.CHAPTER_CV_INFO)
        expected_cv_info = "https://www.gstudios.com.cn/story_v2/api/record/task/batch/cancel/chapter/cv/info"
        print(f"  实际URL: {cv_info_url}")
        print(f"  期望URL: {expected_cv_info}")
        assert cv_info_url == expected_cv_info, f"CV Info URL不匹配: 期望 {expected_cv_info}, 实际 {cv_info_url}"
        print(f"✓ 章节CV信息端点正确: {cv_info_url}")

        print("  测试章节CV取消端点...")
        cv_cancel_url = APIEndpoints.get_url(APIEndpoints.Record.CHAPTER_CV_CANCEL)
        expected_cv_cancel = "https://www.gstudios.com.cn/story_v2/api/record/task/batch/cancel/chapter/cv"
        print(f"  实际URL: {cv_cancel_url}")
        print(f"  期望URL: {expected_cv_cancel}")
        assert cv_cancel_url == expected_cv_cancel, f"CV Cancel URL不匹配: 期望 {expected_cv_cancel}, 实际 {cv_cancel_url}"
        print(f"✓ 章节CV取消端点正确: {cv_cancel_url}")

    except Exception as e:
        print(f"✗ API端点测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

    return True

def test_api_version_compatibility():
    """测试API版本兼容性"""
    print("\n测试API版本兼容性...")
    
    try:
        # 测试默认版本（V2）
        v2_url = APIEndpoints.get_url(APIEndpoints.Record.TTS_TRIAL)
        assert "story_v2" in v2_url
        print(f"✓ 默认V2版本正确: {v2_url}")
        
        # 测试V1版本
        v1_url = APIEndpoints.get_url(APIEndpoints.Record.TTS_TRIAL, APIVersion.V1)
        assert "story" in v1_url and "story_v2" not in v1_url
        print(f"✓ V1版本正确: {v1_url}")
        
    except Exception as e:
        print(f"✗ API版本兼容性测试失败: {e}")
        return False
    
    return True

def main():
    """主测试函数"""
    print("开始测试TTS API更新...")
    
    tests = [
        test_tts_trial_params,
        test_gen_mode_enum,
        test_api_endpoints,
        test_api_version_compatibility
    ]
    
    passed = 0
    total = len(tests)
    
    for test in tests:
        if test():
            passed += 1
        else:
            print(f"✗ 测试失败: {test.__name__}")
    
    print(f"\n测试结果: {passed}/{total} 通过")
    
    if passed == total:
        print("🎉 所有测试通过！TTS API更新成功。")
        return True
    else:
        print("❌ 部分测试失败，请检查代码。")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
