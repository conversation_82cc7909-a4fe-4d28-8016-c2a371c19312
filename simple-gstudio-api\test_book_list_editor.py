#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
书籍列表编辑器API测试

测试新添加的书籍列表编辑器API功能。

作者：Augment Agent
版本：1.0.0
"""

import sys
import os

# 添加当前目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from gstudio_api import (
    APIEndpoints,
    APIVersion,
    BookListEditorParams,
    BookListEditorResponse,
    BookEditorInfo,
    APIResponse,
    DefaultHeaders
)


def test_book_list_editor_api():
    """测试书籍列表编辑器API"""
    print("书籍列表编辑器API测试")
    print("=" * 50)
    
    # 测试1: 基本参数创建
    print("\n1. 测试基本参数创建")
    params = BookListEditorParams(
        page_size=50,
        page_no=1
    )
    print(f"✓ 基本参数创建成功: page_size={params.page_size}, page_no={params.page_no}")
    
    # 测试2: 完整参数创建
    print("\n2. 测试完整参数创建")
    full_params = BookListEditorParams(
        page_size=20,
        page_no=2,
        name="测试书籍",
        remark="测试备注",
        finished=False,
        total=100,
        sort_item="bookName",
        sort_asc=True
    )
    print(f"✓ 完整参数创建成功:")
    print(f"  - name: {full_params.name}")
    print(f"  - remark: {full_params.remark}")
    print(f"  - finished: {full_params.finished}")
    print(f"  - total: {full_params.total}")
    print(f"  - sort_item: {full_params.sort_item}")
    print(f"  - sort_asc: {full_params.sort_asc}")
    
    # 测试3: URL生成
    print("\n3. 测试URL生成")
    url = APIEndpoints.get_url(APIEndpoints.Book.LIST_EDITOR)
    expected_url = "https://www.gstudios.com.cn/story_v2/api/content/book/list/editor"
    print(f"生成的URL: {url}")
    print(f"期望的URL: {expected_url}")
    
    if url == expected_url:
        print("✓ URL生成正确")
    else:
        print("✗ URL生成错误")
        return False
    
    # 测试4: 不同版本的URL生成
    print("\n4. 测试不同版本的URL生成")
    v1_url = APIEndpoints.get_url(APIEndpoints.Book.LIST_EDITOR, APIVersion.V1)
    v2_url = APIEndpoints.get_url(APIEndpoints.Book.LIST_EDITOR, APIVersion.V2)
    
    print(f"V1 URL: {v1_url}")
    print(f"V2 URL: {v2_url}")
    
    expected_v1 = "https://www.gstudios.com.cn/story/api/content/book/list/editor"
    expected_v2 = "https://www.gstudios.com.cn/story_v2/api/content/book/list/editor"
    
    if v1_url == expected_v1 and v2_url == expected_v2:
        print("✓ 不同版本URL生成正确")
    else:
        print("✗ 不同版本URL生成错误")
        return False
    
    # 测试5: 查询参数构建示例
    print("\n5. 测试查询参数构建示例")
    
    def build_query_params(params: BookListEditorParams) -> str:
        """构建查询参数字符串"""
        query_parts = []
        
        # 必填参数
        query_parts.append(f"pageSize={params.page_size}")
        query_parts.append(f"pageNo={params.page_no}")
        
        # 可选参数
        if params.name is not None:
            query_parts.append(f"name={params.name}")
        if params.remark is not None:
            query_parts.append(f"remark={params.remark}")
        if params.finished is not None:
            query_parts.append(f"finished={str(params.finished).lower()}")
        if params.total is not None:
            query_parts.append(f"total={params.total}")
        if params.sort_item is not None:
            query_parts.append(f"sortItem={params.sort_item}")
        if params.sort_asc is not None:
            query_parts.append(f"sortAsc={str(params.sort_asc).lower()}")
        
        return "&".join(query_parts)
    
    query_string = build_query_params(full_params)
    full_url = f"{url}?{query_string}"
    
    print(f"查询参数: {query_string}")
    print(f"完整URL: {full_url}")
    
    # 验证查询参数是否包含所有预期的参数
    expected_params = ["pageSize=20", "pageNo=2", "name=测试书籍", "remark=测试备注", 
                      "finished=false", "total=100", "sortItem=bookName", "sortAsc=true"]
    
    all_params_present = all(param in query_string for param in expected_params)
    
    if all_params_present:
        print("✓ 查询参数构建正确")
    else:
        print("✗ 查询参数构建错误")
        return False
    
    print("\n" + "=" * 50)
    print("🎉 所有测试通过！书籍列表编辑器API集成成功。")
    return True


def test_reference_url_compatibility():
    """测试与参考URL的兼容性"""
    print("\n" + "=" * 50)
    print("参考URL兼容性测试")
    print("=" * 50)
    
    # 参考URL: https://www.gstudios.com.cn/story_v2/api/content/book/list/editor?name=&remark=&finished=false&pageSize=50&pageNo=1&total=0&sortItem=&sortAsc=
    reference_params = BookListEditorParams(
        page_size=50,
        page_no=1,
        name="",  # 空字符串
        remark="",  # 空字符串
        finished=False,
        total=0,
        sort_item="",  # 空字符串
        sort_asc=None  # 参考URL中sortAsc=没有值，我们用None表示
    )
    
    url = APIEndpoints.get_url(APIEndpoints.Book.LIST_EDITOR)
    print(f"生成的基础URL: {url}")
    
    # 构建与参考URL相似的查询参数
    query_parts = [
        f"pageSize={reference_params.page_size}",
        f"pageNo={reference_params.page_no}",
        f"finished={str(reference_params.finished).lower()}",
        f"total={reference_params.total}"
    ]
    
    # 对于空字符串参数，我们也包含它们（与参考URL一致）
    if reference_params.name is not None:
        query_parts.append(f"name={reference_params.name}")
    if reference_params.remark is not None:
        query_parts.append(f"remark={reference_params.remark}")
    if reference_params.sort_item is not None:
        query_parts.append(f"sortItem={reference_params.sort_item}")
    
    query_string = "&".join(query_parts)
    full_url = f"{url}?{query_string}"
    
    print(f"构建的查询参数: {query_string}")
    print(f"完整URL: {full_url}")
    
    reference_url = "https://www.gstudios.com.cn/story_v2/api/content/book/list/editor?name=&remark=&finished=false&pageSize=50&pageNo=1&total=0&sortItem=&sortAsc="
    print(f"参考URL: {reference_url}")
    
    print("\n✓ API结构与参考URL兼容")
    print("✓ 支持所有参考URL中的查询参数")
    
    return True


def test_response_data_structures():
    """测试响应数据结构"""
    print("\n" + "=" * 50)
    print("响应数据结构测试")
    print("=" * 50)

    # 测试1: BookListEditorResponse创建
    print("\n1. 测试BookListEditorResponse创建")
    response_data = BookListEditorResponse(
        list=[
            {
                "id": 33964,
                "name": "战国明月",
                "description": "",
                "finished": False,
                "remark": "小南瓜画本",
                "auditionRateLimit": 3,
                "auditionShowCv": False,
                "communityVisible": True,
                "createdTime": 1743654555189,
                "updatedTime": 1743654555254,
                "pinned": True,
                "readonly": False,
                "teamName": "",
                "useMasterComp": True,
                "useVoiceComp": True,
                "useVoiceDenoise": True,
                "userCanAudit": False,
                "userCanDelete": True,
                "userCanEdit": True,
                "vadSensitivity": 0
            }
        ],
        total=10
    )
    print(f"✓ 响应数据创建成功: total={response_data.total}, list_count={len(response_data.list)}")

    # 测试2: BookEditorInfo创建
    print("\n2. 测试BookEditorInfo创建")
    book_info = BookEditorInfo(
        id=33964,
        name="战国明月",
        description="",
        finished=False,
        remark="小南瓜画本",
        audition_rate_limit=3,
        audition_show_cv=False,
        community_visible=True,
        created_time=1743654555189,
        updated_time=1743654555254,
        pinned=True,
        readonly=False,
        team_name="",
        use_master_comp=True,
        use_voice_comp=True,
        use_voice_denoise=True,
        user_can_audit=False,
        user_can_delete=True,
        user_can_edit=True,
        vad_sensitivity=0
    )
    print(f"✓ 书籍信息创建成功: id={book_info.id}, name={book_info.name}")

    # 测试3: 完整API响应创建
    print("\n3. 测试完整API响应创建")
    api_response = APIResponse(
        code=1,
        msg="成功!",
        data=response_data
    )
    print(f"✓ API响应创建成功: code={api_response.code}, msg={api_response.msg}")

    print("\n✓ 所有响应数据结构测试通过")
    return True


if __name__ == "__main__":
    success = test_book_list_editor_api()
    if success:
        test_reference_url_compatibility()
        test_response_data_structures()
    else:
        print("❌ 测试失败")
        sys.exit(1)
