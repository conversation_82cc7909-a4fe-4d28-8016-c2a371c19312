#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
书籍选择功能诊断脚本

诊断书籍选择和章节管理功能的问题。

作者：Augment Agent
版本：1.0.0
"""

import sys
import os

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from api.client import GStudioAPIClient
from api.models import BookListEditorParams


def diagnose_field_mapping():
    """诊断字段映射问题"""
    print("=" * 60)
    print("诊断字段映射问题")
    print("=" * 60)
    
    # 模拟新API返回的书籍数据
    new_api_book = {
        "id": 39726,
        "name": "我真的不想当皇帝",
        "description": "一部精彩的小说",
        "finished": False,
        "remark": "小南瓜画本",
        "auditionRateLimit": 3,
        "auditionShowCv": False,
        "communityVisible": True,
        "createdTime": 1743654555189
    }
    
    # 模拟旧API返回的书籍数据
    old_api_book = {
        "bookId": "12345",
        "bookName": "经典小说",
        "description": "一部经典的文学作品",
        "price": 2999,
        "author": "著名作家"
    }
    
    print("1. 新API格式书籍数据:")
    print(f"   ID字段: {new_api_book.get('id')}")
    print(f"   名称字段: {new_api_book.get('name')}")
    print(f"   描述字段: {new_api_book.get('description')}")
    print(f"   价格字段: {new_api_book.get('price', '无')}")
    
    print("\n2. 旧API格式书籍数据:")
    print(f"   ID字段: {old_api_book.get('bookId')}")
    print(f"   名称字段: {old_api_book.get('bookName')}")
    print(f"   描述字段: {old_api_book.get('description')}")
    print(f"   价格字段: {old_api_book.get('price')}")
    
    print("\n3. 当前代码中的字段访问问题:")
    
    # 模拟书籍搜索模块的字段访问
    print("\n   书籍搜索模块 (_select_book 方法):")
    print(f"     查找条件: book.get('bookId') == book_id")
    print(f"     新API数据匹配: {new_api_book.get('bookId')} (None - 无法匹配!)")
    print(f"     旧API数据匹配: {old_api_book.get('bookId')} (正常)")
    
    print("\n   书籍搜索模块 (_display_selected_book 方法):")
    print(f"     显示ID: book.get('bookId', '')")
    print(f"     新API: {new_api_book.get('bookId', '')} (空字符串)")
    print(f"     旧API: {old_api_book.get('bookId', '')} (正常)")
    
    print(f"     显示名称: book.get('bookName', '')")
    print(f"     新API: {new_api_book.get('bookName', '')} (空字符串)")
    print(f"     旧API: {old_api_book.get('bookName', '')} (正常)")
    
    # 模拟章节管理模块的字段访问
    print("\n   章节管理模块 (_update_book_info 方法):")
    print(f"     显示ID: selected_book.get('bookId', '')")
    print(f"     新API: {new_api_book.get('bookId', '')} (空字符串)")
    print(f"     旧API: {old_api_book.get('bookId', '')} (正常)")
    
    print(f"     显示名称: selected_book.get('bookName', '')")
    print(f"     新API: {new_api_book.get('bookName', '')} (空字符串)")
    print(f"     旧API: {old_api_book.get('bookName', '')} (正常)")
    
    print("\n   章节管理模块 (_load_chapters 方法):")
    print(f"     获取ID: selected_book.get('bookId')")
    print(f"     新API: {new_api_book.get('bookId')} (None - 无法加载章节!)")
    print(f"     旧API: {old_api_book.get('bookId')} (正常)")


def diagnose_selection_flow():
    """诊断选择流程问题"""
    print("\n" + "=" * 60)
    print("诊断选择流程问题")
    print("=" * 60)
    
    print("1. 书籍搜索结果显示:")
    print("   - 使用兼容的字段映射显示书籍列表 ✓")
    print("   - 新API字段: id, name, description")
    print("   - 旧API字段: bookId, bookName, description")
    print("   - 显示逻辑: 优先使用新字段，回退到旧字段")
    
    print("\n2. 书籍选择过程:")
    print("   - 用户点击书籍列表项")
    print("   - 触发 _on_tree_select 事件 ✓")
    print("   - 启用选择按钮 ✓")
    print("   - 用户点击选择按钮或双击")
    print("   - 触发 _select_book 方法")
    print("   - ❌ 问题: 使用 book.get('bookId') 查找书籍")
    print("   - ❌ 结果: 新API数据无法匹配，selected_book = None")
    
    print("\n3. 数据传递过程:")
    print("   - 调用 main_window.update_workflow_state('selected_book', selected_book)")
    print("   - ❌ 问题: selected_book 为 None")
    print("   - 调用 main_window.next_step() 切换到章节管理")
    print("   - ❌ 结果: 章节管理收到 None 数据")
    
    print("\n4. 章节管理接收:")
    print("   - 调用 _update_book_info 方法")
    print("   - 获取 workflow_state.get('selected_book')")
    print("   - ❌ 问题: selected_book 为 None")
    print("   - ❌ 结果: 显示'请先选择书籍'，无法加载章节")


def diagnose_visual_feedback():
    """诊断视觉反馈问题"""
    print("\n" + "=" * 60)
    print("诊断视觉反馈问题")
    print("=" * 60)
    
    print("1. 树形视图选择反馈:")
    print("   - TreeviewSelect 事件绑定 ✓")
    print("   - 选中时启用按钮 ✓")
    print("   - 系统默认高亮显示 ✓")
    
    print("\n2. 选中书籍信息显示:")
    print("   - _display_selected_book 方法存在 ✓")
    print("   - ❌ 问题: 使用错误的字段名显示信息")
    print("   - ❌ 结果: 显示空白或错误信息")
    
    print("\n3. 章节管理界面反馈:")
    print("   - 书籍信息显示区域存在 ✓")
    print("   - ❌ 问题: 接收到 None 数据")
    print("   - ❌ 结果: 显示'请先选择书籍'")
    print("   - ❌ 问题: 加载章节按钮保持禁用状态")


def suggest_fixes():
    """建议修复方案"""
    print("\n" + "=" * 60)
    print("修复方案建议")
    print("=" * 60)
    
    print("1. 字段映射兼容性修复:")
    print("   - 修复 _select_book 方法中的书籍查找逻辑")
    print("   - 修复 _display_selected_book 方法中的字段访问")
    print("   - 修复章节管理模块中的字段访问")
    print("   - 使用兼容函数: get_book_field(book, 'id') 和 get_book_field(book, 'name')")
    
    print("\n2. 数据传递修复:")
    print("   - 确保选中的书籍数据正确传递到工作流状态")
    print("   - 验证章节管理模块能正确接收书籍数据")
    
    print("\n3. 视觉反馈修复:")
    print("   - 确保选中书籍信息正确显示")
    print("   - 确保章节管理界面正确显示书籍信息")
    print("   - 确保加载章节按钮正确启用")
    
    print("\n4. 测试验证:")
    print("   - 测试新API格式书籍的选择流程")
    print("   - 测试旧API格式书籍的兼容性")
    print("   - 测试章节管理功能的启用")


def main():
    """主诊断函数"""
    print("GStudio 书籍选择功能诊断")
    print("=" * 60)
    print("诊断书籍选择和章节管理功能的问题")
    print()
    
    try:
        diagnose_field_mapping()
        diagnose_selection_flow()
        diagnose_visual_feedback()
        suggest_fixes()
        
        print("\n" + "=" * 60)
        print("诊断完成")
        print("=" * 60)
        print("主要问题:")
        print("1. ❌ 字段名不匹配: 新API使用 id/name，代码使用 bookId/bookName")
        print("2. ❌ 书籍查找失败: 无法在新API数据中找到匹配的书籍")
        print("3. ❌ 数据传递中断: selected_book 为 None")
        print("4. ❌ 章节管理无法启用: 接收到空数据")
        
        print("\n解决方案:")
        print("1. ✅ 统一使用兼容的字段访问方法")
        print("2. ✅ 修复书籍查找和显示逻辑")
        print("3. ✅ 确保数据正确传递")
        print("4. ✅ 验证章节管理功能正常启用")
        
    except Exception as e:
        print(f"诊断过程中发生异常: {e}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    main()
