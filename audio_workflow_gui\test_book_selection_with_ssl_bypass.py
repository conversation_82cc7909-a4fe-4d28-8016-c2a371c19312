#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
使用SSL绕过模式测试书籍选择功能

测试修复后的书籍选择功能，使用SSL绕过模式解决证书问题。

作者：Augment Agent
版本：1.0.0
"""

import sys
import os
import logging

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from api.client import GStudioAPIClient
from utils.field_utils import (
    get_book_field, normalize_book_data, find_book_by_id,
    format_book_display_info, get_book_display_values
)


def setup_logging():
    """设置日志"""
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
        handlers=[
            logging.StreamHandler(sys.stdout),
            logging.FileHandler('book_selection_ssl_bypass_test.log', encoding='utf-8')
        ]
    )


def test_api_with_ssl_bypass():
    """使用SSL绕过模式测试API"""
    print("=" * 60)
    print("使用SSL绕过模式测试API连接")
    print("=" * 60)
    
    # 创建禁用SSL验证的客户端
    client = GStudioAPIClient(debug_mode=True, verify_ssl=False)
    
    # 设置Token
    token = "3dfb89119562456cb8818120139f6ae1"
    client.set_token(token)
    
    print("1. 测试基本API连接...")
    try:
        books = client.search_books("", page_size=5)
        print(f"   ✓ API连接成功，获取到 {len(books)} 本书籍")
        
        if books:
            print("\n2. 书籍数据分析:")
            for i, book in enumerate(books[:3], 1):
                print(f"\n   书籍 {i}:")
                print(f"     原始字段: {list(book.keys())}")
                
                # 标准化数据
                normalized = normalize_book_data(book)
                print(f"     标准化后字段: {list(normalized.keys())}")
                
                # 显示值
                values = get_book_display_values(normalized)
                print(f"     显示值: ID={values[0]}, 名称={values[1]}, 价格={values[2]}")
                print(f"     描述: {values[3]}")
        
        return books
        
    except Exception as e:
        print(f"   ✗ API连接失败: {e}")
        return []


def test_book_selection_logic(books):
    """测试书籍选择逻辑"""
    print("\n" + "=" * 60)
    print("测试书籍选择逻辑")
    print("=" * 60)
    
    if not books:
        print("⚠ 没有书籍数据，跳过选择逻辑测试")
        return False
    
    # 标准化数据
    normalized_books = [normalize_book_data(book) for book in books]
    
    print("1. 模拟书籍搜索结果显示:")
    for i, book in enumerate(normalized_books, 1):
        values = get_book_display_values(book)
        print(f"   {i}. ID: {values[0]}, 名称: {values[1]}, 价格: {values[2]}")
        print(f"      描述: {values[3]}")
    
    print("\n2. 模拟书籍选择过程:")
    
    # 模拟用户选择第一本书
    if normalized_books:
        first_book = normalized_books[0]
        selected_id = get_book_field(first_book, 'id')
        
        print(f"   用户选择书籍ID: {selected_id}")
        
        # 使用查找函数
        found_book = find_book_by_id(normalized_books, selected_id)
        
        if found_book:
            print(f"   ✓ 成功找到选中的书籍")
            book_name = get_book_field(found_book, 'name')
            print(f"   书籍名称: {book_name}")
            
            # 格式化显示信息
            display_info = format_book_display_info(found_book)
            print(f"   书籍信息:")
            print("   " + display_info.replace('\n', '\n   '))
            
            return True
        else:
            print(f"   ✗ 无法找到选中的书籍")
            return False
    
    return False


def test_workflow_state_simulation():
    """测试工作流状态模拟"""
    print("\n" + "=" * 60)
    print("测试工作流状态模拟")
    print("=" * 60)
    
    # 创建客户端并获取书籍
    client = GStudioAPIClient(debug_mode=False, verify_ssl=False)
    token = "3dfb89119562456cb8818120139f6ae1"
    client.set_token(token)
    
    try:
        books = client.search_books("", page_size=3)
        
        if books:
            # 选择第一本书
            selected_book = normalize_book_data(books[0])
            
            print("1. 模拟书籍选择:")
            book_name = get_book_field(selected_book, 'name')
            book_id = get_book_field(selected_book, 'id')
            print(f"   选中书籍: {book_name} (ID: {book_id})")
            
            print("\n2. 模拟工作流状态传递:")
            workflow_state = {'selected_book': selected_book}
            print(f"   工作流状态已设置")
            
            print("\n3. 模拟章节管理界面接收:")
            received_book = workflow_state.get('selected_book')
            
            if received_book:
                print("   ✓ 章节管理界面成功接收书籍数据")
                
                # 模拟章节管理界面的书籍信息显示
                display_info = format_book_display_info(received_book)
                print(f"   章节管理界面显示:")
                print("   " + display_info.replace('\n', '\n   '))
                
                # 模拟章节加载功能
                chapter_book_id = get_book_field(received_book, 'id')
                if chapter_book_id:
                    print(f"\n   ✓ 章节加载功能可以启用，书籍ID: {chapter_book_id}")
                    print(f"   🎉 书籍选择到章节管理的完整流程正常！")
                    return True
                else:
                    print(f"\n   ✗ 无法获取书籍ID，章节加载功能无法启用")
            else:
                print("   ✗ 章节管理界面未接收到书籍数据")
        else:
            print("⚠ 无法获取书籍数据")
            
    except Exception as e:
        print(f"✗ 工作流状态测试失败: {e}")
    
    return False


def test_field_compatibility():
    """测试字段兼容性"""
    print("\n" + "=" * 60)
    print("测试字段兼容性")
    print("=" * 60)
    
    # 模拟新API格式数据
    new_api_book = {
        "id": 39726,
        "name": "我真的不想当皇帝",
        "description": "一部精彩的小说",
        "finished": False,
        "remark": "小南瓜画本"
    }
    
    # 模拟旧API格式数据
    old_api_book = {
        "bookId": "12345",
        "bookName": "经典小说",
        "description": "一部经典的文学作品",
        "price": 2999
    }
    
    print("1. 测试新API格式兼容性:")
    normalized_new = normalize_book_data(new_api_book)
    print(f"   原始字段: {list(new_api_book.keys())}")
    print(f"   标准化后: {list(normalized_new.keys())}")
    print(f"   ID访问: {get_book_field(normalized_new, 'id')}")
    print(f"   名称访问: {get_book_field(normalized_new, 'name')}")
    
    print("\n2. 测试旧API格式兼容性:")
    normalized_old = normalize_book_data(old_api_book)
    print(f"   原始字段: {list(old_api_book.keys())}")
    print(f"   标准化后: {list(normalized_old.keys())}")
    print(f"   ID访问: {get_book_field(normalized_old, 'id')}")
    print(f"   名称访问: {get_book_field(normalized_old, 'name')}")
    
    print("\n3. 测试查找功能:")
    books = [normalized_new, normalized_old]
    
    # 测试新API格式查找
    found_new = find_book_by_id(books, 39726)
    print(f"   查找新API书籍(ID=39726): {'成功' if found_new else '失败'}")
    
    # 测试旧API格式查找
    found_old = find_book_by_id(books, "12345")
    print(f"   查找旧API书籍(ID=12345): {'成功' if found_old else '失败'}")
    
    return found_new and found_old


def main():
    """主测试函数"""
    print("GStudio 书籍选择功能测试（SSL绕过模式）")
    print("=" * 60)
    print("使用SSL绕过模式测试修复后的书籍选择功能")
    print("⚠ 注意：SSL绕过模式仅适用于测试环境")
    print()
    
    # 设置日志
    setup_logging()
    
    success_count = 0
    total_tests = 4
    
    try:
        # 测试API连接
        books = test_api_with_ssl_bypass()
        if books:
            success_count += 1
            print("✅ API连接测试通过")
        else:
            print("❌ API连接测试失败")
        
        # 测试书籍选择逻辑
        if test_book_selection_logic(books):
            success_count += 1
            print("✅ 书籍选择逻辑测试通过")
        else:
            print("❌ 书籍选择逻辑测试失败")
        
        # 测试工作流状态
        if test_workflow_state_simulation():
            success_count += 1
            print("✅ 工作流状态测试通过")
        else:
            print("❌ 工作流状态测试失败")
        
        # 测试字段兼容性
        if test_field_compatibility():
            success_count += 1
            print("✅ 字段兼容性测试通过")
        else:
            print("❌ 字段兼容性测试失败")
        
        print("\n" + "=" * 60)
        print("测试结果总结")
        print("=" * 60)
        
        print(f"通过测试: {success_count}/{total_tests}")
        
        if success_count == total_tests:
            print("🎉 所有测试通过！书籍选择功能修复成功！")
            print()
            print("修复内容总结:")
            print("1. ✅ 创建了字段兼容性工具函数")
            print("2. ✅ 修复了书籍搜索结果显示")
            print("3. ✅ 修复了书籍选择查找逻辑")
            print("4. ✅ 修复了选中书籍信息显示")
            print("5. ✅ 修复了章节管理界面数据接收")
            print("6. ✅ 添加了SSL问题诊断和修复功能")
            print()
            print("现在可以正常使用:")
            print("• 书籍搜索和选择功能")
            print("• 章节管理功能")
            print("• SSL问题自动诊断和修复")
        else:
            print("⚠ 部分测试未通过，请检查失败的测试项目")
        
    except Exception as e:
        print(f"测试过程中发生异常: {e}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    main()
