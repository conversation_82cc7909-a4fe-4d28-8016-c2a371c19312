#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
布局调整演示脚本

演示修改后的书籍搜索界面布局。

作者：Augment Agent
版本：1.0.0
"""

import sys
import os
import tkinter as tk
import logging

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from config.settings import AppConfig
from gui.main_window import MainWindow


def setup_logging():
    """设置日志"""
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
        handlers=[
            logging.StreamHandler(sys.stdout),
            logging.FileHandler('layout_demo.log', encoding='utf-8')
        ]
    )


def demo_layout_adjustment():
    """演示布局调整"""
    print("=" * 60)
    print("GStudio 书籍搜索界面布局调整演示")
    print("=" * 60)
    print()
    print("此演示将展示修改后的书籍搜索界面布局：")
    print()
    print("🎨 布局调整内容：")
    print("1. 状态筛选移动到第一行搜索按钮右侧")
    print("2. 第二行简化为只有刷新按钮")
    print("3. 界面更加紧凑和直观")
    print()
    print("📐 新的布局结构：")
    print("第一行：[书籍名称] [输入框] [精确匹配] [搜索] [状态:] [全部|制作中|已完成]")
    print("第二行：[刷新全部]")
    print()
    
    try:
        # 创建主窗口
        root = tk.Tk()
        root.title("GStudio 音频工作流 - 布局调整演示")
        
        # 创建配置对象
        config = AppConfig()
        
        # 创建主窗口实例
        main_window = MainWindow(root, config)
        
        print("✅ 应用程序已启动")
        print()
        print("📋 演示说明：")
        print()
        print("1️⃣ 观察新的界面布局：")
        print("   • 点击'书籍搜索'标签页")
        print("   • 观察第一行的控件排列")
        print("   • 注意状态筛选现在在搜索按钮右侧")
        print("   • 第二行只有刷新按钮")
        print()
        print("2️⃣ 测试界面紧凑性：")
        print("   • 所有搜索相关功能在同一行")
        print("   • 界面垂直空间利用更高效")
        print("   • 控件间距合理，不拥挤")
        print()
        print("3️⃣ 验证功能完整性：")
        print("   • 状态筛选功能完全正常")
        print("   • 搜索功能保持不变")
        print("   • 精确匹配功能正常")
        print("   • 刷新功能正常")
        print()
        print("4️⃣ 测试用户体验：")
        print("   • 尝试不同的状态筛选")
        print("   • 输入搜索词并搜索")
        print("   • 观察操作流程是否更直观")
        print("   • 检查界面响应性")
        print()
        
        # 显示当前配置
        ssl_status = "启用" if config.is_ssl_verification_enabled() else "禁用"
        token_status = "已设置" if config.get('api.token') else "未设置"
        
        print(f"📋 当前配置：")
        print(f"• SSL验证：{ssl_status}")
        print(f"• API Token：{token_status}")
        print(f"• API地址：{config.get('api.base_url')}")
        print()
        
        print("🚀 开始演示...")
        print("请在应用程序中体验新的界面布局")
        print()
        
        # 运行应用程序
        root.mainloop()
        
        print("演示结束")
        
    except Exception as e:
        print(f"演示过程中发生错误: {e}")
        import traceback
        traceback.print_exc()


def show_layout_details():
    """显示布局详情"""
    print("\n" + "=" * 60)
    print("布局调整详情")
    print("=" * 60)
    print()
    print("🔧 修改的代码：")
    print()
    print("📁 gui/book_search.py - _create_widgets() 方法")
    print()
    print("1. 第一行布局调整：")
    print("```python")
    print("# 原来的第一行（4个控件）")
    print("row=0: [书籍名称] [输入框] [精确匹配] [搜索]")
    print()
    print("# 调整后的第一行（6个控件）")
    print("row=0: [书籍名称] [输入框] [精确匹配] [搜索] [状态:] [状态筛选组]")
    print("```")
    print()
    print("2. 第二行布局简化：")
    print("```python")
    print("# 原来的第二行（4个控件）")
    print("row=1: [刷新全部] [状态筛选:] [状态筛选组]")
    print()
    print("# 调整后的第二行（1个控件）")
    print("row=1: [刷新全部]")
    print("```")
    print()
    print("3. 控件参数调整：")
    print("```python")
    print("# 搜索输入框宽度调整")
    print("width=30 → width=25  # 为状态筛选腾出空间")
    print()
    print("# 搜索按钮间距调整")
    print("padx=(5, 0) → padx=(5, 10)  # 增加右侧间距")
    print()
    print("# 状态标签简化")
    print("'状态筛选:' → '状态:'  # 减少文字长度")
    print()
    print("# 状态筛选按钮间距")
    print("padx=(0, 10) → padx=(0, 8)  # 紧凑间距")
    print("```")
    print()
    print("✨ 布局优势：")
    print()
    print("• 🎯 空间利用率提升")
    print("  - 第一行承载更多功能")
    print("  - 减少垂直空间占用")
    print("  - 界面更加紧凑")
    print()
    print("• 🎨 逻辑分组优化")
    print("  - 搜索相关功能集中在一行")
    print("  - 状态筛选紧邻搜索按钮")
    print("  - 操作流程更直观")
    print()
    print("• 📱 适配性增强")
    print("  - 适合不同屏幕尺寸")
    print("  - 控件间距合理")
    print("  - 整体美观性提升")
    print()
    print("• ⚡ 功能保持完整")
    print("  - 所有原有功能保留")
    print("  - 状态筛选逻辑不变")
    print("  - 搜索联动正常")
    print()
    print("📊 布局对比：")
    print()
    print("调整前：")
    print("┌─────────────────────────────────────────────────┐")
    print("│ [书籍名称] [输入框(30)] [精确匹配] [搜索]        │")
    print("│ [刷新全部] [状态筛选:] [全部|制作中|已完成]      │")
    print("└─────────────────────────────────────────────────┘")
    print()
    print("调整后：")
    print("┌─────────────────────────────────────────────────┐")
    print("│ [书籍名称] [输入框(25)] [精确匹配] [搜索] [状态:] [全部|制作中|已完成] │")
    print("│ [刷新全部]                                      │")
    print("└─────────────────────────────────────────────────┘")
    print()


def main():
    """主函数"""
    print("GStudio 书籍搜索界面布局调整演示")
    print("=" * 60)
    
    # 设置日志
    setup_logging()
    
    try:
        # 显示布局详情
        show_layout_details()
        
        # 询问是否开始演示
        response = input("是否要启动GUI演示？(y/n): ").lower().strip()
        
        if response in ['y', 'yes', '是', '1']:
            demo_layout_adjustment()
        else:
            print("演示已取消")
        
    except KeyboardInterrupt:
        print("\n演示被用户中断")
    except Exception as e:
        print(f"演示过程中发生异常: {e}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    main()
