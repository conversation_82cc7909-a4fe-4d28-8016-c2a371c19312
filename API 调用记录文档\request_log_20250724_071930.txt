
==================================================
=== 请求信息 ===
时间: 2025-07-24 07:23:29
方法: GET
URL: https://www.gstudios.com.cn/story_v2/api/content/character/list/book?bookId=29658&roleType=book&skipStat=true
备注标题: 获取书籍角色列表
备注内容: 
Headers: {
  "host": "www.gstudios.com.cn",
  "connection": "keep-alive",
  "sec-ch-ua-platform": "Windows",
  "x-requested-with": "XMLHttpRequest",
  "user-agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********",
  "accept": "application/json, text/plain, */*",
  "sec-ch-ua": "\"Microsoft Edge\";v=\"135\", \"Not-A.Brand\";v=\"8\", \"Chromium\";v=\"135\"",
  "sec-ch-ua-mobile": "?0",
  "sec-fetch-site": "same-origin",
  "sec-fetch-mode": "cors",
  "sec-fetch-dest": "empty",
  "accept-encoding": "gzip, deflate, br, zstd",
  "accept-language": "zh-CN,zh;q=0.9,en;q=0.8,en-GB;q=0.7,en-US;q=0.6",
  "referer": "https://www.gstudios.com.cn/",
  "authorization": "Bearer 3dfb89119562456cb8818120139f6ae1"
}
Cookie: 
=== 响应信息 ===
状态码: 200
响应头: {'Server': 'CLOUD ELB 1.0.0', 'Date': 'Wed, 23 Jul 2025 23:23:28 GMT', 'Content-Type': 'application/json;charset=UTF-8', 'Transfer-Encoding': 'chunked', 'Connection': 'close', 'Content-Encoding': 'gzip'}

=== 响应内容 ===
{
  "code": 1,
  "data": [
    {
      "ableToMerge": false,
      "ageType": null,
      "autoBind": true,
      "autoMatch": true,
      "autoMixMode": 0,
      "color": "#CFD5E9",
      "createdTime": 1737879987820,
      "cvHumanId": null,
      "cvHumanName": "",
      "cvHumanTask": true,
      "cvHumanTaskNum": null,
      "cvRobotId": 568,
      "cvRobotName": "曹先森MK-III",
      "cvRobotTask": true,
      "cvRobotTaskNum": null,
      "debutChapterId": "",
      "debutChapterName": "",
      "debutChapterSeqNum": null,
      "description": "",
      "envMode": 0,
      "envType": 0,
      "eqConfig": {
        "bp1": {
          "f": 200,
          "g": 0,
          "on": false,
          "q": 2
        },
        "bp2": {
          "f": 800,
          "g": 0,
          "on": false,
          "q": 2
        },
        "bp3": {
          "f": 3200,
          "g": 0,
          "on": false,
          "q": 2
        },
        "hs": {
          "f": 18000,
          "g": 0,
          "on": false,
          "s": 1
        },
        "ls": {
          "f": 40,
          "g": 0,
          "on": false,
          "s": 1
        }
      },
      "gainDb": 0,
      "gender": null,
      "id": 3940779,
      "mixMode": null,
      "name": "旁白",
      "nicknames": "",
      "numChapter": null,
      "numChar": null,
      "numCue": null,
      "presence": false,
      "robotDurationFactorSilence": 100,
      "robotGenMode": 0,
      "robotSpeedFactor": 100,
      "robotStyle": "",
      "robotStyleStandard": null,
      "selectedMixMode": 0,
      "type": 0
    },
    {
      "ableToMerge": true,
      "ageType": 2,
      "autoBind": false,
      "autoMatch": true,
      "autoMixMode": 0,
      "color": "#B0F7FF",
      "createdTime": 1737880040231,
      "cvHumanId": null,
      "cvHumanName": "",
      "cvHumanTask": true,
      "cvHumanTaskNum": null,
      "cvRobotId": null,
      "cvRobotName": "",
      "cvRobotTask": true,
      "cvRobotTaskNum": null,
      "debutChapterId": "",
      "debutChapterName": "",
      "debutChapterSeqNum": null,
      "description": "",
      "envMode": 0,
      "envType": 0,
      "eqConfig": {
        "bp1": {
          "f": 200,
          "g": 0,
          "on": false,
          "q": 2
        },
        "bp2": {
          "f": 800,
          "g": 0,
          "on": false,
          "q": 2
        },
        "bp3": {
          "f": 3200,
          "g": 0,
          "on": false,
          "q": 2
        },
        "hs": {
          "f": 18000,
          "g": 0,
          "on": false,
          "s": 1
        },
        "ls": {
          "f": 40,
          "g": 0,
          "on": false,
          "s": 1
        }
      },
      "gainDb": 0,
      "gender": 1,
      "id": 3940780,
      "mixMode": null,
      "name": "叶锋",
      "nicknames": "",
      "numChapter": null,
      "numChar": null,
      "numCue": null,
      "presence": false,
      "robotDurationFactorSilence": 100,
      "robotGenMode": 0,
      "robotSpeedFactor": 100,
      "robotStyle": "",
      "robotStyleStandard": null,
      "selectedMixMode": 1,
      "type": 1
    },
    {
      "ableToMerge": true,
      "ageType": 4,
      "autoBind": false,
      "autoMatch": true,
      "autoMixMode": 0,
      "color": "#A9DCFF",
      "createdTime": 1737880049430,
      "cvHumanId": null,
      "cvHumanName": "",
      "cvHumanTask": true,
      "cvHumanTaskNum": null,
      "cvRobotId": null,
      "cvRobotName": "",
      "cvRobotTask": true,
      "cvRobotTaskNum": null,
      "debutChapterId": "",
      "debutChapterName": "",
      "debutChapterSeqNum": null,
      "description": "收留主角的玉月湖边的渔民，大约五六十岁。身材非常高大，体态身高比得上叶锋。发须花白，慈眉善目，穿着一件怪模怪样",
      "envMode": 0,
      "envType": 0,
      "eqConfig": {
        "bp1": {
          "f": 200,
          "g": 0,
          "on": false,
          "q": 2
        },
        "bp2": {
          "f": 800,
          "g": 0,
          "on": false,
          "q": 2
        },
        "bp3": {
          "f": 3200,
          "g": 0,
          "on": false,
          "q": 2
        },
        "hs": {
          "f": 18000,
          "g": 0,
          "on": false,
          "s": 1
        },
        "ls": {
          "f": 40,
          "g": 0,
          "on": false,
          "s": 1
        }
      },
      "gainDb": 0,
      "gender": 1,
      "id": 3940785,
      "mixMode": null,
      "name": "李大爷",
      "nicknames": "",
      "numChapter": null,
      "numChar": null,
      "numCue": null,
      "presence": false,
      "robotDurationFactorSilence": 100,
      "robotGenMode": 0,
      "robotSpeedFactor": 100,
      "robotStyle": "",
      "robotStyleStandard": null,
      "selectedMixMode": 1,
      "type": 2
    },
    {
      "ableToMerge": true,
      "ageType": 2,
      "autoBind": false,
      "autoMatch": true,
      "autoMixMode": 0,
      "color": "#90c7ed",
      "createdTime": 1737880056772,
      "cvHumanId": null,
      "cvHumanName": "",
      "cvHumanTask": true,
      "cvHumanTaskNum": null,
      "cvRobotId": null,
      "cvRobotName": "",
      "cvRobotTask": true,
      "cvRobotTaskNum": null,
      "debutChapterId": "",
      "debutChapterName": "",
      "debutChapterSeqNum": null,
      "description": "玉月节度使李会伟的妹妹，乃是大月国远近闻名的艳女。这李音虽然生活放浪，但却身怀绝技，且极具谋略，手下更有无数的奇人异土，曾立下大功无数",
      "envMode": 0,
      "envType": 0,
      "eqConfig": {
        "bp1": {
          "f": 200,
          "g": 0,
          "on": false,
          "q": 2
        },
        "bp2": {
          "f": 800,
          "g": 0,
          "on": false,
          "q": 2
        },
        "bp3": {
          "f": 3200,
          "g": 0,
          "on": false,
          "q": 2
        },
        "hs": {
          "f": 18000,
          "g": 0,
          "on": false,
          "s": 1
        },
        "ls": {
          "f": 40,
          "g": 0,
          "on": false,
          "s": 1
        }
      },
      "gainDb": 0,
      "gender": 2,
      "id": 3940790,
      "mixMode": null,
      "name": "李音",
      "nicknames": "",
      "numChapter": null,
      "numChar": null,
      "numCue": null,
      "presence": false,
      "robotDurationFactorSilence": 100,
      "robotGenMode": 0,
      "robotSpeedFactor": 100,
      "robotStyle": "",
      "robotStyleStandard": null,
      "selectedMixMode": 1,
      "type": 2
    },
    {
      "ableToMerge": true,
      "ageType": 2,
      "autoBind": false,
      "autoMatch": true,
      "autoMixMode": 0,
      "color": "#00B4FF",
      "createdTime": 1737880066001,
      "cvHumanId": null,
      "cvHumanName": "",
      "cvHumanTask": true,
      "cvHumanTaskNum": null,
      "cvRobotId": null,
      "cvRobotName": "",
      "cvRobotTask": true,
      "cvRobotTaskNum": null,
      "debutChapterId": "",
      "debutChapterName": "",
      "debutChapterSeqNum": null,
      "description": "花怡原名花兰，原是兰花国的公主，",
      "envMode": 0,
      "envType": 0,
      "eqConfig": {
        "bp1": {
          "f": 200,
          "g": 0,
          "on": false,
          "q": 2
        },
        "bp2": {
          "f": 800,
          "g": 0,
          "on": false,
          "q": 2
        },
        "bp3": {
          "f": 3200,
          "g": 0,
          "on": false,
          "q": 2
        },
        "hs": {
          "f": 18000,
          "g": 0,
          "on": false,
          "s": 1
        },
        "ls": {
          "f": 40,
          "g": 0,
          "on": false,
          "s": 1
        }
      },
      "gainDb": 0,
      "gender": 2,
      "id": 3940793,
      "mixMode": null,
      "name": "花怡",
      "nicknames": "",
      "numChapter": null,
      "numChar": null,
      "numCue": null,
      "presence": false,
      "robotDurationFactorSilence": 100,
      "robotGenMode": 0,
      "robotSpeedFactor": 100,
      "robotStyle": "",
      "robotStyleStandard": null,
      "selectedMixMode": 1,
      "type": 2
    },
    {
      "ableToMerge": true,
      "ageType": 3,
      "autoBind": false,
      "autoMatch": true,
      "autoMixMode": 0,
      "color": "#C9C6FF",
      "createdTime": 1739245057529,
      "cvHumanId": null,
      "cvHumanName": "",
      "cvHumanTask": true,
      "cvHumanTaskNum": null,
      "cvRobotId": null,
      "cvRobotName": "",
      "cvRobotTask": true,
      "cvRobotTaskNum": null,
      "debutChapterId": "",
      "debutChapterName": "",
      "debutChapterSeqNum": null,
      "description": "满脸横肉，眼睛细小，年约四十左右，一身蓝色长袍的肥胖大汉",
      "envMode": 0,
      "envType": 0,
      "eqConfig": {
        "bp1": {
          "f": 200,
          "g": 0,
          "on": false,
          "q": 2
        },
        "bp2": {
          "f": 800,
          "g": 0,
          "on": false,
          "q": 2
        },
        "bp3": {
          "f": 3200,
          "g": 0,
          "on": false,
          "q": 2
        },
        "hs": {
          "f": 18000,
          "g": 0,
          "on": false,
          "s": 1
        },
        "ls": {
          "f": 40,
          "g": 0,
          "on": false,
          "s": 1
        }
      },
      "gainDb": 0,
      "gender": 1,
      "id": 4037287,
      "mixMode": null,
      "name": "李虎",
      "nicknames": "",
      "numChapter": null,
      "numChar": null,
      "numCue": null,
      "presence": false,
      "robotDurationFactorSilence": 100,
      "robotGenMode": 0,
      "robotSpeedFactor": 100,
      "robotStyle": "",
      "robotStyleStandard": null,
      "selectedMixMode": 1,
      "type": 2
    },
    {
      "ableToMerge": true,
      "ageType": 3,
      "autoBind": false,
      "autoMatch": true,
      "autoMixMode": 0,
      "color": "#FFE617",
      "createdTime": 1737880051343,
      "cvHumanId": null,
      "cvHumanName": "",
      "cvHumanTask": true,
      "cvHumanTaskNum": null,
      "cvRobotId": null,
      "cvRobotName": "",
      "cvRobotTask": true,
      "cvRobotTaskNum": null,
      "debutChapterId": "",
      "debutChapterName": "",
      "debutChapterSeqNum": null,
      "description": "",
      "envMode": 0,
      "envType": 0,
      "eqConfig": {
        "bp1": {
          "f": 200,
          "g": 0,
          "on": false,
          "q": 2
        },
        "bp2": {
          "f": 800,
          "g": 0,
          "on": false,
          "q": 2
        },
        "bp3": {
          "f": 3200,
          "g": 0,
          "on": false,
          "q": 2
        },
        "hs": {
          "f": 18000,
          "g": 0,
          "on": false,
          "s": 1
        },
        "ls": {
          "f": 40,
          "g": 0,
          "on": false,
          "s": 1
        }
      },
      "gainDb": 0,
      "gender": 1,
      "id": 3940786,
      "mixMode": null,
      "name": "卖奴大汉",
      "nicknames": "槐梧大汉",
      "numChapter": null,
      "numChar": null,
      "numCue": null,
      "presence": false,
      "robotDurationFactorSilence": 100,
      "robotGenMode": 0,
      "robotSpeedFactor": 100,
      "robotStyle": "",
      "robotStyleStandard": null,
      "selectedMixMode": 1,
      "type": 3
    },
    {
      "ableToMerge": true,
      "ageType": 2,
      "autoBind": false,
      "autoMatch": true,
      "autoMixMode": 0,
      "color": "#FDA58E",
      "createdTime": 1737880056767,
      "cvHumanId": null,
      "cvHumanName": "",
      "cvHumanTask": true,
      "cvHumanTaskNum": null,
      "cvRobotId": null,
      "cvRobotName": "",
      "cvRobotTask": true,
      "cvRobotTaskNum": null,
      "debutChapterId": "",
      "debutChapterName": "",
      "debutChapterSeqNum": null,
      "description": "李音随从之一",
      "envMode": 0,
      "envType": 0,
      "eqConfig": {
        "bp1": {
          "f": 200,
          "g": 0,
          "on": false,
          "q": 2
        },
        "bp2": {
          "f": 800,
          "g": 0,
          "on": false,
          "q": 2
        },
        "bp3": {
          "f": 3200,
          "g": 0,
          "on": false,
          "q": 2
        },
        "hs": {
          "f": 18000,
          "g": 0,
          "on": false,
          "s": 1
        },
        "ls": {
          "f": 40,
          "g": 0,
          "on": false,
          "s": 1
        }
      },
      "gainDb": 0,
      "gender": 1,
      "id": 3940789,
      "mixMode": null,
      "name": "李木",
      "nicknames": "",
      "numChapter": null,
      "numChar": null,
      "numCue": null,
      "presence": false,
      "robotDurationFactorSilence": 100,
      "robotGenMode": 0,
      "robotSpeedFactor": 100,
      "robotStyle": "",
      "robotStyleStandard": null,
      "selectedMixMode": 1,
      "type": 3
    },
    {
      "ableToMerge": true,
      "ageType": 2,
      "autoBind": false,
      "autoMatch": true,
      "autoMixMode": 0,
      "color": "#AC88FF",
      "createdTime": 1739245749674,
      "cvHumanId": null,
      "cvHumanName": "",
      "cvHumanTask": true,
      "cvHumanTaskNum": null,
      "cvRobotId": null,
      "cvRobotName": "",
      "cvRobotTask": true,
      "cvRobotTaskNum": null,
      "debutChapterId": "",
      "debutChapterName": "",
      "debutChapterSeqNum": null,
      "description": "",
      "envMode": 0,
      "envType": 0,
      "eqConfig": {
        "bp1": {
          "f": 200,
          "g": 0,
          "on": false,
          "q": 2
        },
        "bp2": {
          "f": 800,
          "g": 0,
          "on": false,
          "q": 2
        },
        "bp3": {
          "f": 3200,
          "g": 0,
          "on": false,
          "q": 2
        },
        "hs": {
          "f": 18000,
          "g": 0,
          "on": false,
          "s": 1
        },
        "ls": {
          "f": 40,
          "g": 0,
          "on": false,
          "s": 1
        }
      },
      "gainDb": 0,
      "gender": 1,
      "id": 4037327,
      "mixMode": null,
      "name": "007路人1",
      "nicknames": "",
      "numChapter": null,
      "numChar": null,
      "numCue": null,
      "presence": false,
      "robotDurationFactorSilence": 100,
      "robotGenMode": 0,
      "robotSpeedFactor": 100,
      "robotStyle": "",
      "robotStyleStandard": null,
      "selectedMixMode": 1,
      "type": 3
    },
    {
      "ableToMerge": true,
      "ageType": 2,
      "autoBind": false,
      "autoMatch": true,
      "autoMixMode": 0,
      "color": "#FFF1EE",
      "createdTime": 1739245763075,
      "cvHumanId": null,
      "cvHumanName": "",
      "cvHumanTask": true,
      "cvHumanTaskNum": null,
      "cvRobotId": null,
      "cvRobotName": "",
      "cvRobotTask": true,
      "cvRobotTaskNum": null,
      "debutChapterId": "",
      "debutChapterName": "",
      "debutChapterSeqNum": null,
      "description": "",
      "envMode": 0,
      "envType": 0,
      "eqConfig": {
        "bp1": {
          "f": 200,
          "g": 0,
          "on": false,
          "q": 2
        },
        "bp2": {
          "f": 800,
          "g": 0,
          "on": false,
          "q": 2
        },
        "bp3": {
          "f": 3200,
          "g": 0,
          "on": false,
          "q": 2
        },
        "hs": {
          "f": 18000,
          "g": 0,
          "on": false,
          "s": 1
        },
        "ls": {
          "f": 40,
          "g": 0,
          "on": false,
          "s": 1
        }
      },
      "gainDb": 0,
      "gender": 1,
      "id": 4037328,
      "mixMode": null,
      "name": "007路人2",
      "nicknames": "",
      "numChapter": null,
      "numChar": null,
      "numCue": null,
      "presence": false,
      "robotDurationFactorSilence": 100,
      "robotGenMode": 0,
      "robotSpeedFactor": 100,
      "robotStyle": "",
      "robotStyleStandard": null,
      "selectedMixMode": 1,
      "type": 3
    },
    {
      "ableToMerge": true,
      "ageType": 2,
      "autoBind": false,
      "autoMatch": true,
      "autoMixMode": 0,
      "color": "#8FA8FE",
      "createdTime": 1739245858771,
      "cvHumanId": null,
      "cvHumanName": "",
      "cvHumanTask": true,
      "cvHumanTaskNum": null,
      "cvRobotId": null,
      "cvRobotName": "",
      "cvRobotTask": true,
      "cvRobotTaskNum": null,
      "debutChapterId": "",
      "debutChapterName": "",
      "debutChapterSeqNum": null,
      "description": "",
      "envMode": 0,
      "envType": 0,
      "eqConfig": {
        "bp1": {
          "f": 200,
          "g": 0,
          "on": false,
          "q": 2
        },
        "bp2": {
          "f": 800,
          "g": 0,
          "on": false,
          "q": 2
        },
        "bp3": {
          "f": 3200,
          "g": 0,
          "on": false,
          "q": 2
        },
        "hs": {
          "f": 18000,
          "g": 0,
          "on": false,
          "s": 1
        },
        "ls": {
          "f": 40,
          "g": 0,
          "on": false,
          "s": 1
        }
      },
      "gainDb": 0,
      "gender": 1,
      "id": 4037329,
      "mixMode": null,
      "name": "007路人3",
      "nicknames": "",
      "numChapter": null,
      "numChar": null,
      "numCue": null,
      "presence": false,
      "robotDurationFactorSilence": 100,
      "robotGenMode": 0,
      "robotSpeedFactor": 100,
      "robotStyle": "",
      "robotStyleStandard": null,
      "selectedMixMode": 1,
      "type": 3
    },
    {
      "ableToMerge": true,
      "ageType": 2,
      "autoBind": false,
      "autoMatch": true,
      "autoMixMode": 0,
      "color": "#8FA8FE",
      "createdTime": 1737880040235,
      "cvHumanId": null,
      "cvHumanName": "",
      "cvHumanTask": true,
      "cvHumanTaskNum": null,
      "cvRobotId": null,
      "cvRobotName": "",
      "cvRobotTask": true,
      "cvRobotTaskNum": null,
      "debutChapterId": "",
      "debutChapterName": "",
      "debutChapterSeqNum": null,
      "description": "",
      "envMode": 0,
      "envType": 0,
      "eqConfig": null,
      "gainDb": 0,
      "gender": 2,
      "id": 3940781,
      "mixMode": null,
      "name": "刘烟",
      "nicknames": "",
      "numChapter": null,
      "numChar": null,
      "numCue": null,
      "presence": false,
      "robotDurationFactorSilence": 100,
      "robotGenMode": 0,
      "robotSpeedFactor": 100,
      "robotStyle": "",
      "robotStyleStandard": null,
      "selectedMixMode": 1,
      "type": null
    },
    {
      "ableToMerge": true,
      "ageType": 2,
      "autoBind": false,
      "autoMatch": true,
      "autoMixMode": 0,
      "color": "#ff87a7",
      "createdTime": 1737880053998,
      "cvHumanId": null,
      "cvHumanName": "",
      "cvHumanTask": true,
      "cvHumanTaskNum": null,
      "cvRobotId": null,
      "cvRobotName": "",
      "cvRobotTask": true,
      "cvRobotTaskNum": null,
      "debutChapterId": "",
      "debutChapterName": "",
      "debutChapterSeqNum": null,
      "description": "",
      "envMode": 0,
      "envType": 0,
      "eqConfig": null,
      "gainDb": 0,
      "gender": 1,
      "id": 3940787,
      "mixMode": null,
      "name": "男性青年",
      "nicknames": "",
      "numChapter": null,
      "numChar": null,
      "numCue": null,
      "presence": false,
      "robotDurationFactorSilence": 100,
      "robotGenMode": 0,
      "robotSpeedFactor": 100,
      "robotStyle": "",
      "robotStyleStandard": null,
      "selectedMixMode": 1,
      "type": null
    },
    {
      "ableToMerge": true,
      "ageType": 2,
      "autoBind": false,
      "autoMatch": true,
      "autoMixMode": 0,
      "color": "#B4EDAD",
      "createdTime": 1737880054003,
      "cvHumanId": null,
      "cvHumanName": "",
      "cvHumanTask": true,
      "cvHumanTaskNum": null,
      "cvRobotId": null,
      "cvRobotName": "",
      "cvRobotTask": true,
      "cvRobotTaskNum": null,
      "debutChapterId": "",
      "debutChapterName": "",
      "debutChapterSeqNum": null,
      "description": "",
      "envMode": 0,
      "envType": 0,
      "eqConfig": null,
      "gainDb": 0,
      "gender": 1,
      "id": 3940788,
      "mixMode": null,
      "name": "汉子",
      "nicknames": "",
      "numChapter": null,
      "numChar": null,
      "numCue": null,
      "presence": false,
      "robotDurationFactorSilence": 100,
      "robotGenMode": 0,
      "robotSpeedFactor": 100,
      "robotStyle": "",
      "robotStyleStandard": null,
      "selectedMixMode": 1,
      "type": null
    },
    {
      "ableToMerge": true,
      "ageType": 2,
      "autoBind": false,
      "autoMatch": true,
      "autoMixMode": 0,
      "color": "#D5FE84",
      "createdTime": 1737880060084,
      "cvHumanId": null,
      "cvHumanName": "",
      "cvHumanTask": true,
      "cvHumanTaskNum": null,
      "cvRobotId": null,
      "cvRobotName": "",
      "cvRobotTask": true,
      "cvRobotTaskNum": null,
      "debutChapterId": "",
      "debutChapterName": "",
      "debutChapterSeqNum": null,
      "description": "",
      "envMode": 0,
      "envType": 0,
      "eqConfig": null,
      "gainDb": 0,
      "gender": 2,
      "id": 3940791,
      "mixMode": null,
      "name": "杨依",
      "nicknames": "",
      "numChapter": null,
      "numChar": null,
      "numCue": null,
      "presence": false,
      "robotDurationFactorSilence": 100,
      "robotGenMode": 0,
      "robotSpeedFactor": 100,
      "robotStyle": "",
      "robotStyleStandard": null,
      "selectedMixMode": 1,
      "type": null
    },
    {
      "ableToMerge": true,
      "ageType": null,
      "autoBind": false,
      "autoMatch": true,
      "autoMixMode": 0,
      "color": "#D9B3F9",
      "createdTime": 1737880070311,
      "cvHumanId": null,
      "cvHumanName": "",
      "cvHumanTask": true,
      "cvHumanTaskNum": null,
      "cvRobotId": null,
      "cvRobotName": "",
      "cvRobotTask": true,
      "cvRobotTaskNum": null,
      "debutChapterId": "",
      "debutChapterName": "",
      "debutChapterSeqNum": null,
      "description": "",
      "envMode": 0,
      "envType": 0,
      "eqConfig": null,
      "gainDb": 0,
      "gender": 1,
      "id": 3940794,
      "mixMode": null,
      "name": "秋寒枫",
      "nicknames": "",
      "numChapter": null,
      "numChar": null,
      "numCue": null,
      "presence": false,
      "robotDurationFactorSilence": 100,
      "robotGenMode": 0,
      "robotSpeedFactor": 100,
      "robotStyle": "",
      "robotStyleStandard": null,
      "selectedMixMode": 1,
      "type": null
    },
    {
      "ableToMerge": true,
      "ageType": 2,
      "autoBind": false,
      "autoMatch": true,
      "autoMixMode": 0,
      "color": "#FFDDA7",
      "createdTime": 1737880070315,
      "cvHumanId": null,
      "cvHumanName": "",
      "cvHumanTask": true,
      "cvHumanTaskNum": null,
      "cvRobotId": null,
      "cvRobotName": "",
      "cvRobotTask": true,
      "cvRobotTaskNum": null,
      "debutChapterId": "",
      "debutChapterName": "",
      "debutChapterSeqNum": null,
      "description": "",
      "envMode": 0,
      "envType": 0,
      "eqConfig": null,
      "gainDb": 0,
      "gender": 1,
      "id": 3940795,
      "mixMode": null,
      "name": "手下",
      "nicknames": "",
      "numChapter": null,
      "numChar": null,
      "numCue": null,
      "presence": false,
      "robotDurationFactorSilence": 100,
      "robotGenMode": 0,
      "robotSpeedFactor": 100,
      "robotStyle": "",
      "robotStyleStandard": null,
      "selectedMixMode": 1,
      "type": null
    },
    {
      "ableToMerge": true,
      "ageType": 4,
      "autoBind": false,
      "autoMatch": true,
      "autoMixMode": 0,
      "color": "#F3F4A3",
      "createdTime": 1737880076809,
      "cvHumanId": null,
      "cvHumanName": "",
      "cvHumanTask": true,
      "cvHumanTaskNum": null,
      "cvRobotId": null,
      "cvRobotName": "",
      "cvRobotTask": true,
      "cvRobotTaskNum": null,
      "debutChapterId": "",
      "debutChapterName": "",
      "debutChapterSeqNum": null,
      "description": "",
      "envMode": 0,
      "envType": 0,
      "eqConfig": null,
      "gainDb": 0,
      "gender": 1,
      "id": 3940797,
      "mixMode": null,
      "name": "男性老年",
      "nicknames": "",
      "numChapter": null,
      "numChar": null,
      "numCue": null,
      "presence": false,
      "robotDurationFactorSilence": 100,
      "robotGenMode": 0,
      "robotSpeedFactor": 100,
      "robotStyle": "",
      "robotStyleStandard": null,
      "selectedMixMode": 1,
      "type": null
    },
    {
      "ableToMerge": true,
      "ageType": 4,
      "autoBind": false,
      "autoMatch": true,
      "autoMixMode": 0,
      "color": "#88D7EB",
      "createdTime": 1737880076813,
      "cvHumanId": null,
      "cvHumanName": "",
      "cvHumanTask": true,
      "cvHumanTaskNum": null,
      "cvRobotId": null,
      "cvRobotName": "",
      "cvRobotTask": true,
      "cvRobotTaskNum": null,
      "debutChapterId": "",
      "debutChapterName": "",
      "debutChapterSeqNum": null,
      "description": "",
      "envMode": 0,
      "envType": 0,
      "eqConfig": null,
      "gainDb": 0,
      "gender": 1,
      "id": 3940798,
      "mixMode": null,
      "name": "王大爷",
      "nicknames": "",
      "numChapter": null,
      "numChar": null,
      "numCue": null,
      "presence": false,
      "robotDurationFactorSilence": 100,
      "robotGenMode": 0,
      "robotSpeedFactor": 100,
      "robotStyle": "",
      "robotStyleStandard": null,
      "selectedMixMode": 1,
      "type": null
    },
    {
      "ableToMerge": true,
      "ageType": 4,
      "autoBind": false,
      "autoMatch": true,
      "autoMixMode": 0,
      "color": "#F1E0FF",
      "createdTime": 1737880076818,
      "cvHumanId": null,
      "cvHumanName": "",
      "cvHumanTask": true,
      "cvHumanTaskNum": null,
      "cvRobotId": null,
      "cvRobotName": "",
      "cvRobotTask": true,
      "cvRobotTaskNum": null,
      "debutChapterId": "",
      "debutChapterName": "",
      "debutChapterSeqNum": null,
      "description": "",
      "envMode": 0,
      "envType": 0,
      "eqConfig": null,
      "gainDb": 0,
      "gender": 1,
      "id": 3940799,
      "mixMode": null,
      "name": "老大爷",
      "nicknames": "",
      "numChapter": null,
      "numChar": null,
      "numCue": null,
      "presence": false,
      "robotDurationFactorSilence": 100,
      "robotGenMode": 0,
      "robotSpeedFactor": 100,
      "robotStyle": "",
      "robotStyleStandard": null,
      "selectedMixMode": 1,
      "type": null
    },
    {
      "ableToMerge": true,
      "ageType": 3,
      "autoBind": false,
      "autoMatch": true,
      "autoMixMode": 0,
      "color": "#E3F3C3",
      "createdTime": 1737880079108,
      "cvHumanId": null,
      "cvHumanName": "",
      "cvHumanTask": true,
      "cvHumanTaskNum": null,
      "cvRobotId": null,
      "cvRobotName": "",
      "cvRobotTask": true,
      "cvRobotTaskNum": null,
      "debutChapterId": "",
      "debutChapterName": "",
      "debutChapterSeqNum": null,
      "description": "",
      "envMode": 0,
      "envType": 0,
      "eqConfig": null,
      "gainDb": 0,
      "gender": 2,
      "id": 3940800,
      "mixMode": null,
      "name": "顾大嫂",
      "nicknames": "",
      "numChapter": null,
      "numChar": null,
      "numCue": null,
      "presence": false,
      "robotDurationFactorSilence": 100,
      "robotGenMode": 0,
      "robotSpeedFactor": 100,
      "robotStyle": "",
      "robotStyleStandard": null,
      "selectedMixMode": 1,
      "type": null
    },
    {
      "ableToMerge": true,
      "ageType": 3,
      "autoBind": false,
      "autoMatch": true,
      "autoMixMode": 0,
      "color": "#F19AC9",
      "createdTime": 1737880083636,
      "cvHumanId": null,
      "cvHumanName": "",
      "cvHumanTask": true,
      "cvHumanTaskNum": null,
      "cvRobotId": null,
      "cvRobotName": "",
      "cvRobotTask": true,
      "cvRobotTaskNum": null,
      "debutChapterId": "",
      "debutChapterName": "",
      "debutChapterSeqNum": null,
      "description": "",
      "envMode": 0,
      "envType": 0,
      "eqConfig": null,
      "gainDb": 0,
      "gender": 2,
      "id": 3940801,
      "mixMode": null,
      "name": "女性中年",
      "nicknames": "",
      "numChapter": null,
      "numChar": null,
      "numCue": null,
      "presence": false,
      "robotDurationFactorSilence": 100,
      "robotGenMode": 0,
      "robotSpeedFactor": 100,
      "robotStyle": "",
      "robotStyleStandard": null,
      "selectedMixMode": 1,
      "type": null
    },
    {
      "ableToMerge": true,
      "ageType": 2,
      "autoBind": false,
      "autoMatch": true,
      "autoMixMode": 0,
      "color": "#FCB038",
      "createdTime": 1737880090976,
      "cvHumanId": null,
      "cvHumanName": "",
      "cvHumanTask": true,
      "cvHumanTaskNum": null,
      "cvRobotId": null,
      "cvRobotName": "",
      "cvRobotTask": true,
      "cvRobotTaskNum": null,
      "debutChapterId": "",
      "debutChapterName": "",
      "debutChapterSeqNum": null,
      "description": "",
      "envMode": 0,
      "envType": 0,
      "eqConfig": null,
      "gainDb": 0,
      "gender": 1,
      "id": 3940802,
      "mixMode": null,
      "name": "李谈",
      "nicknames": "",
      "numChapter": null,
      "numChar": null,
      "numCue": null,
      "presence": false,
      "robotDurationFactorSilence": 100,
      "robotGenMode": 0,
      "robotSpeedFactor": 100,
      "robotStyle": "",
      "robotStyleStandard": null,
      "selectedMixMode": 1,
      "type": null
    },
    {
      "ableToMerge": true,
      "ageType": 2,
      "autoBind": false,
      "autoMatch": true,
      "autoMixMode": 0,
      "color": "#C9C6FF",
      "createdTime": 1737880090986,
      "cvHumanId": null,
      "cvHumanName": "",
      "cvHumanTask": true,
      "cvHumanTaskNum": null,
      "cvRobotId": null,
      "cvRobotName": "",
      "cvRobotTask": true,
      "cvRobotTaskNum": null,
      "debutChapterId": "",
      "debutChapterName": "",
      "debutChapterSeqNum": null,
      "description": "",
      "envMode": 0,
      "envType": 0,
      "eqConfig": null,
      "gainDb": 0,
      "gender": 2,
      "id": 3940803,
      "mixMode": null,
      "name": "女性青年",
      "nicknames": "",
      "numChapter": null,
      "numChar": null,
      "numCue": null,
      "presence": false,
      "robotDurationFactorSilence": 100,
      "robotGenMode": 0,
      "robotSpeedFactor": 100,
      "robotStyle": "",
      "robotStyleStandard": null,
      "selectedMixMode": 1,
      "type": null
    },
    {
      "ableToMerge": true,
      "ageType": 3,
      "autoBind": false,
      "autoMatch": true,
      "autoMixMode": 0,
      "color": "#8FFED8",
      "createdTime": 1737880094855,
      "cvHumanId": null,
      "cvHumanName": "",
      "cvHumanTask": true,
      "cvHumanTaskNum": null,
      "cvRobotId": null,
      "cvRobotName": "",
      "cvRobotTask": true,
      "cvRobotTaskNum": null,
      "debutChapterId": "",
      "debutChapterName": "",
      "debutChapterSeqNum": null,
      "description": "",
      "envMode": 0,
      "envType": 0,
      "eqConfig": null,
      "gainDb": 0,
      "gender": 1,
      "id": 3940804,
      "mixMode": null,
      "name": "男性中年",
      "nicknames": "",
      "numChapter": null,
      "numChar": null,
      "numCue": null,
      "presence": false,
      "robotDurationFactorSilence": 100,
      "robotGenMode": 0,
      "robotSpeedFactor": 100,
      "robotStyle": "",
      "robotStyleStandard": null,
      "selectedMixMode": 1,
      "type": null
    },
    {
      "ableToMerge": true,
      "ageType": 3,
      "autoBind": false,
      "autoMatch": true,
      "autoMixMode": 0,
      "color": "#AC88FF",
      "createdTime": 1737880094860,
      "cvHumanId": null,
      "cvHumanName": "",
      "cvHumanTask": true,
      "cvHumanTaskNum": null,
      "cvRobotId": null,
      "cvRobotName": "",
      "cvRobotTask": true,
      "cvRobotTaskNum": null,
      "debutChapterId": "",
      "debutChapterName": "",
      "debutChapterSeqNum": null,
      "description": "",
      "envMode": 0,
      "envType": 0,
      "eqConfig": null,
      "gainDb": 0,
      "gender": 1,
      "id": 3940805,
      "mixMode": null,
      "name": "胡老板",
      "nicknames": "",
      "numChapter": null,
      "numChar": null,
      "numCue": null,
      "presence": false,
      "robotDurationFactorSilence": 100,
      "robotGenMode": 0,
      "robotSpeedFactor": 100,
      "robotStyle": "",
      "robotStyleStandard": null,
      "selectedMixMode": 1,
      "type": null
    },
    {
      "ableToMerge": true,
      "ageType": 2,
      "autoBind": false,
      "autoMatch": true,
      "autoMixMode": 0,
      "color": "#FFF1EE",
      "createdTime": 1737880094865,
      "cvHumanId": null,
      "cvHumanName": "",
      "cvHumanTask": true,
      "cvHumanTaskNum": null,
      "cvRobotId": null,
      "cvRobotName": "",
      "cvRobotTask": true,
      "cvRobotTaskNum": null,
      "debutChapterId": "",
      "debutChapterName": "",
      "debutChapterSeqNum": null,
      "description": "",
      "envMode": 0,
      "envType": 0,
      "eqConfig": null,
      "gainDb": 0,
      "gender": 1,
      "id": 3940806,
      "mixMode": null,
      "name": "赵白",
      "nicknames": "",
      "numChapter": null,
      "numChar": null,
      "numCue": null,
      "presence": false,
      "robotDurationFactorSilence": 100,
      "robotGenMode": 0,
      "robotSpeedFactor": 100,
      "robotStyle": "",
      "robotStyleStandard": null,
      "selectedMixMode": 1,
      "type": null
    },
    {
      "ableToMerge": true,
      "ageType": 2,
      "autoBind": false,
      "autoMatch": true,
      "autoMixMode": 0,
      "color": "#8FA8FE",
      "createdTime": 1737880094870,
      "cvHumanId": null,
      "cvHumanName": "",
      "cvHumanTask": true,
      "cvHumanTaskNum": null,
      "cvRobotId": null,
      "cvRobotName": "",
      "cvRobotTask": true,
      "cvRobotTaskNum": null,
      "debutChapterId": "",
      "debutChapterName": "",
      "debutChapterSeqNum": null,
      "description": "",
      "envMode": 0,
      "envType": 0,
      "eqConfig": null,
      "gainDb": 0,
      "gender": 1,
      "id": 3940807,
      "mixMode": null,
      "name": "食客",
      "nicknames": "",
      "numChapter": null,
      "numChar": null,
      "numCue": null,
      "presence": false,
      "robotDurationFactorSilence": 100,
      "robotGenMode": 0,
      "robotSpeedFactor": 100,
      "robotStyle": "",
      "robotStyleStandard": null,
      "selectedMixMode": 1,
      "type": null
    },
    {
      "ableToMerge": true,
      "ageType": 2,
      "autoBind": false,
      "autoMatch": true,
      "autoMixMode": 0,
      "color": "#B7DDE4",
      "createdTime": 1737880099395,
      "cvHumanId": null,
      "cvHumanName": "",
      "cvHumanTask": true,
      "cvHumanTaskNum": null,
      "cvRobotId": null,
      "cvRobotName": "",
      "cvRobotTask": true,
      "cvRobotTaskNum": null,
      "debutChapterId": "",
      "debutChapterName": "",
      "debutChapterSeqNum": null,
      "description": "",
      "envMode": 0,
      "envType": 0,
      "eqConfig": null,
      "gainDb": 0,
      "gender": 1,
      "id": 3940808,
      "mixMode": null,
      "name": "军官",
      "nicknames": "",
      "numChapter": null,
      "numChar": null,
      "numCue": null,
      "presence": false,
      "robotDurationFactorSilence": 100,
      "robotGenMode": 0,
      "robotSpeedFactor": 100,
      "robotStyle": "",
      "robotStyleStandard": null,
      "selectedMixMode": 1,
      "type": null
    },
    {
      "ableToMerge": true,
      "ageType": null,
      "autoBind": false,
      "autoMatch": true,
      "autoMixMode": 0,
      "color": "#F3B6B6",
      "createdTime": 1737880105471,
      "cvHumanId": null,
      "cvHumanName": "",
      "cvHumanTask": true,
      "cvHumanTaskNum": null,
      "cvRobotId": null,
      "cvRobotName": "",
      "cvRobotTask": true,
      "cvRobotTaskNum": null,
      "debutChapterId": "",
      "debutChapterName": "",
      "debutChapterSeqNum": null,
      "description": "",
      "envMode": 0,
      "envType": 0,
      "eqConfig": null,
      "gainDb": 0,
      "gender": 1,
      "id": 3940809,
      "mixMode": null,
      "name": "王献臣",
      "nicknames": "",
      "numChapter": null,
      "numChar": null,
      "numCue": null,
      "presence": false,
      "robotDurationFactorSilence": 100,
      "robotGenMode": 0,
      "robotSpeedFactor": 100,
      "robotStyle": "",
      "robotStyleStandard": null,
      "selectedMixMode": 1,
      "type": null
    },
    {
      "ableToMerge": true,
      "ageType": null,
      "autoBind": false,
      "autoMatch": true,
      "autoMixMode": 0,
      "color": "#79EF6B",
      "createdTime": 1737880110336,
      "cvHumanId": null,
      "cvHumanName": "",
      "cvHumanTask": true,
      "cvHumanTaskNum": null,
      "cvRobotId": null,
      "cvRobotName": "",
      "cvRobotTask": true,
      "cvRobotTaskNum": null,
      "debutChapterId": "",
      "debutChapterName": "",
      "debutChapterSeqNum": null,
      "description": "",
      "envMode": 0,
      "envType": 0,
      "eqConfig": null,
      "gainDb": 0,
      "gender": 1,
      "id": 3940810,
      "mixMode": null,
      "name": "赵大爷",
      "nicknames": "",
      "numChapter": null,
      "numChar": null,
      "numCue": null,
      "presence": false,
      "robotDurationFactorSilence": 100,
      "robotGenMode": 0,
      "robotSpeedFactor": 100,
      "robotStyle": "",
      "robotStyleStandard": null,
      "selectedMixMode": 1,
      "type": null
    },
    {
      "ableToMerge": true,
      "ageType": null,
      "autoBind": false,
      "autoMatch": true,
      "autoMixMode": 0,
      "color": "#A9DCFF",
      "createdTime": 1737880110340,
      "cvHumanId": null,
      "cvHumanName": "",
      "cvHumanTask": true,
      "cvHumanTaskNum": null,
      "cvRobotId": null,
      "cvRobotName": "",
      "cvRobotTask": true,
      "cvRobotTaskNum": null,
      "debutChapterId": "",
      "debutChapterName": "",
      "debutChapterSeqNum": null,
      "description": "",
      "envMode": 0,
      "envType": 0,
      "eqConfig": null,
      "gainDb": 0,
      "gender": null,
      "id": 3940811,
      "mixMode": null,
      "name": "赵白发",
      "nicknames": "",
      "numChapter": null,
      "numChar": null,
      "numCue": null,
      "presence": false,
      "robotDurationFactorSilence": 100,
      "robotGenMode": 0,
      "robotSpeedFactor": 100,
      "robotStyle": "",
      "robotStyleStandard": null,
      "selectedMixMode": 1,
      "type": null
    },
    {
      "ableToMerge": true,
      "ageType": 2,
      "autoBind": false,
      "autoMatch": true,
      "autoMixMode": 0,
      "color": "#FFE617",
      "createdTime": 1737880110346,
      "cvHumanId": null,
      "cvHumanName": "",
      "cvHumanTask": true,
      "cvHumanTaskNum": null,
      "cvRobotId": null,
      "cvRobotName": "",
      "cvRobotTask": true,
      "cvRobotTaskNum": null,
      "debutChapterId": "",
      "debutChapterName": "",
      "debutChapterSeqNum": null,
      "description": "",
      "envMode": 0,
      "envType": 0,
      "eqConfig": null,
      "gainDb": 0,
      "gender": 1,
      "id": 3940812,
      "mixMode": null,
      "name": "寒舟",
      "nicknames": "",
      "numChapter": null,
      "numChar": null,
      "numCue": null,
      "presence": false,
      "robotDurationFactorSilence": 100,
      "robotGenMode": 0,
      "robotSpeedFactor": 100,
      "robotStyle": "",
      "robotStyleStandard": null,
      "selectedMixMode": 1,
      "type": null
    },
    {
      "ableToMerge": true,
      "ageType": null,
      "autoBind": false,
      "autoMatch": true,
      "autoMixMode": 0,
      "color": "#ff87a7",
      "createdTime": 1737880112842,
      "cvHumanId": null,
      "cvHumanName": "",
      "cvHumanTask": true,
      "cvHumanTaskNum": null,
      "cvRobotId": null,
      "cvRobotName": "",
      "cvRobotTask": true,
      "cvRobotTaskNum": null,
      "debutChapterId": "",
      "debutChapterName": "",
      "debutChapterSeqNum": null,
      "description": "",
      "envMode": 0,
      "envType": 0,
      "eqConfig": null,
      "gainDb": 0,
      "gender": 1,
      "id": 3940813,
      "mixMode": null,
      "name": "关吕",
      "nicknames": "",
      "numChapter": null,
      "numChar": null,
      "numCue": null,
      "presence": false,
      "robotDurationFactorSilence": 100,
      "robotGenMode": 0,
      "robotSpeedFactor": 100,
      "robotStyle": "",
      "robotStyleStandard": null,
      "selectedMixMode": 1,
      "type": null
    },
    {
      "ableToMerge": true,
      "ageType": 2,
      "autoBind": false,
      "autoMatch": true,
      "autoMixMode": 0,
      "color": "#B4EDAD",
      "createdTime": 1737880112848,
      "cvHumanId": null,
      "cvHumanName": "",
      "cvHumanTask": true,
      "cvHumanTaskNum": null,
      "cvRobotId": null,
      "cvRobotName": "",
      "cvRobotTask": true,
      "cvRobotTaskNum": null,
      "debutChapterId": "",
      "debutChapterName": "",
      "debutChapterSeqNum": null,
      "description": "",
      "envMode": 0,
      "envType": 0,
      "eqConfig": null,
      "gainDb": 0,
      "gender": 2,
      "id": 3940814,
      "mixMode": null,
      "name": "林素",
      "nicknames": "",
      "numChapter": null,
      "numChar": null,
      "numCue": null,
      "presence": false,
      "robotDurationFactorSilence": 100,
      "robotGenMode": 0,
      "robotSpeedFactor": 100,
      "robotStyle": "",
      "robotStyleStandard": null,
      "selectedMixMode": 1,
      "type": null
    },
    {
      "ableToMerge": true,
      "ageType": null,
      "autoBind": false,
      "autoMatch": true,
      "autoMixMode": 0,
      "color": "#FDA58E",
      "createdTime": 1737880112853,
      "cvHumanId": null,
      "cvHumanName": "",
      "cvHumanTask": true,
      "cvHumanTaskNum": null,
      "cvRobotId": null,
      "cvRobotName": "",
      "cvRobotTask": true,
      "cvRobotTaskNum": null,
      "debutChapterId": "",
      "debutChapterName": "",
      "debutChapterSeqNum": null,
      "description": "",
      "envMode": 0,
      "envType": 0,
      "eqConfig": null,
      "gainDb": 0,
      "gender": null,
      "id": 3940815,
      "mixMode": null,
      "name": "少女",
      "nicknames": "",
      "numChapter": null,
      "numChar": null,
      "numCue": null,
      "presence": false,
      "robotDurationFactorSilence": 100,
      "robotGenMode": 0,
      "robotSpeedFactor": 100,
      "robotStyle": "",
      "robotStyleStandard": null,
      "selectedMixMode": 1,
      "type": null
    },
    {
      "ableToMerge": true,
      "ageType": 2,
      "autoBind": false,
      "autoMatch": true,
      "autoMixMode": 0,
      "color": "#90c7ed",
      "createdTime": 1737880118927,
      "cvHumanId": null,
      "cvHumanName": "",
      "cvHumanTask": true,
      "cvHumanTaskNum": null,
      "cvRobotId": null,
      "cvRobotName": "",
      "cvRobotTask": true,
      "cvRobotTaskNum": null,
      "debutChapterId": "",
      "debutChapterName": "",
      "debutChapterSeqNum": null,
      "description": "",
      "envMode": 0,
      "envType": 0,
      "eqConfig": null,
      "gainDb": 0,
      "gender": null,
      "id": 3940816,
      "mixMode": null,
      "name": "孙眉",
      "nicknames": "",
      "numChapter": null,
      "numChar": null,
      "numCue": null,
      "presence": false,
      "robotDurationFactorSilence": 100,
      "robotGenMode": 0,
      "robotSpeedFactor": 100,
      "robotStyle": "",
      "robotStyleStandard": null,
      "selectedMixMode": 1,
      "type": null
    },
    {
      "ableToMerge": true,
      "ageType": 2,
      "autoBind": false,
      "autoMatch": true,
      "autoMixMode": 0,
      "color": "#D5FE84",
      "createdTime": 1737880122259,
      "cvHumanId": null,
      "cvHumanName": "",
      "cvHumanTask": true,
      "cvHumanTaskNum": null,
      "cvRobotId": null,
      "cvRobotName": "",
      "cvRobotTask": true,
      "cvRobotTaskNum": null,
      "debutChapterId": "",
      "debutChapterName": "",
      "debutChapterSeqNum": null,
      "description": "",
      "envMode": 0,
      "envType": 0,
      "eqConfig": null,
      "gainDb": 0,
      "gender": 2,
      "id": 3940817,
      "mixMode": null,
      "name": "侍女",
      "nicknames": "",
      "numChapter": null,
      "numChar": null,
      "numCue": null,
      "presence": false,
      "robotDurationFactorSilence": 100,
      "robotGenMode": 0,
      "robotSpeedFactor": 100,
      "robotStyle": "",
      "robotStyleStandard": null,
      "selectedMixMode": 1,
      "type": null
    },
    {
      "ableToMerge": true,
      "ageType": 4,
      "autoBind": false,
      "autoMatch": true,
      "autoMixMode": 0,
      "color": "#FF8686",
      "createdTime": 1737880126237,
      "cvHumanId": null,
      "cvHumanName": "",
      "cvHumanTask": true,
      "cvHumanTaskNum": null,
      "cvRobotId": null,
      "cvRobotName": "",
      "cvRobotTask": true,
      "cvRobotTaskNum": null,
      "debutChapterId": "",
      "debutChapterName": "",
      "debutChapterSeqNum": null,
      "description": "",
      "envMode": 0,
      "envType": 0,
      "eqConfig": null,
      "gainDb": 0,
      "gender": 1,
      "id": 3940818,
      "mixMode": null,
      "name": "白管家",
      "nicknames": "",
      "numChapter": null,
      "numChar": null,
      "numCue": null,
      "presence": false,
      "robotDurationFactorSilence": 100,
      "robotGenMode": 0,
      "robotSpeedFactor": 100,
      "robotStyle": "",
      "robotStyleStandard": null,
      "selectedMixMode": 1,
      "type": null
    },
    {
      "ableToMerge": true,
      "ageType": 2,
      "autoBind": false,
      "autoMatch": true,
      "autoMixMode": 0,
      "color": "#00B4FF",
      "createdTime": 1737880129866,
      "cvHumanId": null,
      "cvHumanName": "",
      "cvHumanTask": true,
      "cvHumanTaskNum": null,
      "cvRobotId": null,
      "cvRobotName": "",
      "cvRobotTask": true,
      "cvRobotTaskNum": null,
      "debutChapterId": "",
      "debutChapterName": "",
      "debutChapterSeqNum": null,
      "description": "",
      "envMode": 0,
      "envType": 0,
      "eqConfig": null,
      "gainDb": 0,
      "gender": 2,
      "id": 3940819,
      "mixMode": null,
      "name": "如青",
      "nicknames": "",
      "numChapter": null,
      "numChar": null,
      "numCue": null,
      "presence": false,
      "robotDurationFactorSilence": 100,
      "robotGenMode": 0,
      "robotSpeedFactor": 100,
      "robotStyle": "",
      "robotStyleStandard": null,
      "selectedMixMode": 1,
      "type": null
    },
    {
      "ableToMerge": true,
      "ageType": 4,
      "autoBind": false,
      "autoMatch": true,
      "autoMixMode": 0,
      "color": "#D9B3F9",
      "createdTime": 1737880133680,
      "cvHumanId": null,
      "cvHumanName": "",
      "cvHumanTask": true,
      "cvHumanTaskNum": null,
      "cvRobotId": null,
      "cvRobotName": "",
      "cvRobotTask": true,
      "cvRobotTaskNum": null,
      "debutChapterId": "",
      "debutChapterName": "",
      "debutChapterSeqNum": null,
      "description": "",
      "envMode": 0,
      "envType": 0,
      "eqConfig": null,
      "gainDb": 0,
      "gender": 1,
      "id": 3940820,
      "mixMode": null,
      "name": "老掌柜",
      "nicknames": "",
      "numChapter": null,
      "numChar": null,
      "numCue": null,
      "presence": false,
      "robotDurationFactorSilence": 100,
      "robotGenMode": 0,
      "robotSpeedFactor": 100,
      "robotStyle": "",
      "robotStyleStandard": null,
      "selectedMixMode": 1,
      "type": null
    },
    {
      "ableToMerge": true,
      "ageType": 3,
      "autoBind": false,
      "autoMatch": true,
      "autoMixMode": 0,
      "color": "#FFDDA7",
      "createdTime": 1737880133686,
      "cvHumanId": null,
      "cvHumanName": "",
      "cvHumanTask": true,
      "cvHumanTaskNum": null,
      "cvRobotId": null,
      "cvRobotName": "",
      "cvRobotTask": true,
      "cvRobotTaskNum": null,
      "debutChapterId": "",
      "debutChapterName": "",
      "debutChapterSeqNum": null,
      "description": "",
      "envMode": 0,
      "envType": 0,
      "eqConfig": null,
      "gainDb": 0,
      "gender": 1,
      "id": 3940821,
      "mixMode": null,
      "name": "长脸大汉",
      "nicknames": "",
      "numChapter": null,
      "numChar": null,
      "numCue": null,
      "presence": false,
      "robotDurationFactorSilence": 100,
      "robotGenMode": 0,
      "robotSpeedFactor": 100,
      "robotStyle": "",
      "robotStyleStandard": null,
      "selectedMixMode": 1,
      "type": null
    },
    {
      "ableToMerge": true,
      "ageType": null,
      "autoBind": false,
      "autoMatch": true,
      "autoMixMode": 0,
      "color": "#E0F6F2",
      "createdTime": 1737880133690,
      "cvHumanId": null,
      "cvHumanName": "",
      "cvHumanTask": true,
      "cvHumanTaskNum": null,
      "cvRobotId": null,
      "cvRobotName": "",
      "cvRobotTask": true,
      "cvRobotTaskNum": null,
      "debutChapterId": "",
      "debutChapterName": "",
      "debutChapterSeqNum": null,
      "description": "",
      "envMode": 0,
      "envType": 0,
      "eqConfig": null,
      "gainDb": 0,
      "gender": 1,
      "id": 3940822,
      "mixMode": null,
      "name": "长脸一个",
      "nicknames": "",
      "numChapter": null,
      "numChar": null,
      "numCue": null,
      "presence": false,
      "robotDurationFactorSilence": 100,
      "robotGenMode": 0,
      "robotSpeedFactor": 100,
      "robotStyle": "",
      "robotStyleStandard": null,
      "selectedMixMode": 1,
      "type": null
    },
    {
      "ableToMerge": true,
      "ageType": 3,
      "autoBind": false,
      "autoMatch": true,
      "autoMixMode": 0,
      "color": "#F3F4A3",
      "createdTime": 1737880133695,
      "cvHumanId": null,
      "cvHumanName": "",
      "cvHumanTask": true,
      "cvHumanTaskNum": null,
      "cvRobotId": null,
      "cvRobotName": "",
      "cvRobotTask": true,
      "cvRobotTaskNum": null,
      "debutChapterId": "",
      "debutChapterName": "",
      "debutChapterSeqNum": null,
      "description": "",
      "envMode": 0,
      "envType": 0,
      "eqConfig": null,
      "gainDb": 0,
      "gender": 1,
      "id": 3940823,
      "mixMode": null,
      "name": "金吴",
      "nicknames": "",
      "numChapter": null,
      "numChar": null,
      "numCue": null,
      "presence": false,
      "robotDurationFactorSilence": 100,
      "robotGenMode": 0,
      "robotSpeedFactor": 100,
      "robotStyle": "",
      "robotStyleStandard": null,
      "selectedMixMode": 1,
      "type": null
    },
    {
      "ableToMerge": true,
      "ageType": null,
      "autoBind": false,
      "autoMatch": true,
      "autoMixMode": 0,
      "color": "#88D7EB",
      "createdTime": 1737880133699,
      "cvHumanId": null,
      "cvHumanName": "",
      "cvHumanTask": true,
      "cvHumanTaskNum": null,
      "cvRobotId": null,
      "cvRobotName": "",
      "cvRobotTask": true,
      "cvRobotTaskNum": null,
      "debutChapterId": "",
      "debutChapterName": "",
      "debutChapterSeqNum": null,
      "description": "",
      "envMode": 0,
      "envType": 0,
      "eqConfig": null,
      "gainDb": 0,
      "gender": 1,
      "id": 3940824,
      "mixMode": null,
      "name": "长脸汉子",
      "nicknames": "",
      "numChapter": null,
      "numChar": null,
      "numCue": null,
      "presence": false,
      "robotDurationFactorSilence": 100,
      "robotGenMode": 0,
      "robotSpeedFactor": 100,
      "robotStyle": "",
      "robotStyleStandard": null,
      "selectedMixMode": 1,
      "type": null
    },
    {
      "ableToMerge": true,
      "ageType": null,
      "autoBind": false,
      "autoMatch": true,
      "autoMixMode": 0,
      "color": "#F1E0FF",
      "createdTime": 1737880136589,
      "cvHumanId": null,
      "cvHumanName": "",
      "cvHumanTask": true,
      "cvHumanTaskNum": null,
      "cvRobotId": null,
      "cvRobotName": "",
      "cvRobotTask": true,
      "cvRobotTaskNum": null,
      "debutChapterId": "",
      "debutChapterName": "",
      "debutChapterSeqNum": null,
      "description": "",
      "envMode": 0,
      "envType": 0,
      "eqConfig": null,
      "gainDb": 0,
      "gender": 2,
      "id": 3940829,
      "mixMode": null,
      "name": "丘边黑",
      "nicknames": "",
      "numChapter": null,
      "numChar": null,
      "numCue": null,
      "presence": false,
      "robotDurationFactorSilence": 100,
      "robotGenMode": 0,
      "robotSpeedFactor": 100,
      "robotStyle": "",
      "robotStyleStandard": null,
      "selectedMixMode": 1,
      "type": null
    },
    {
      "ableToMerge": true,
      "ageType": 2,
      "autoBind": false,
      "autoMatch": true,
      "autoMixMode": 0,
      "color": "#E3F3C3",
      "createdTime": 1737880142093,
      "cvHumanId": null,
      "cvHumanName": "",
      "cvHumanTask": true,
      "cvHumanTaskNum": null,
      "cvRobotId": null,
      "cvRobotName": "",
      "cvRobotTask": true,
      "cvRobotTaskNum": null,
      "debutChapterId": "",
      "debutChapterName": "",
      "debutChapterSeqNum": null,
      "description": "",
      "envMode": 0,
      "envType": 0,
      "eqConfig": null,
      "gainDb": 0,
      "gender": 2,
      "id": 3940830,
      "mixMode": null,
      "name": "云儿",
      "nicknames": "",
      "numChapter": null,
      "numChar": null,
      "numCue": null,
      "presence": false,
      "robotDurationFactorSilence": 100,
      "robotGenMode": 0,
      "robotSpeedFactor": 100,
      "robotStyle": "",
      "robotStyleStandard": null,
      "selectedMixMode": 1,
      "type": null
    },
    {
      "ableToMerge": true,
      "ageType": 2,
      "autoBind": false,
      "autoMatch": true,
      "autoMixMode": 0,
      "color": "#F19AC9",
      "createdTime": 1737880142097,
      "cvHumanId": null,
      "cvHumanName": "",
      "cvHumanTask": true,
      "cvHumanTaskNum": null,
      "cvRobotId": null,
      "cvRobotName": "",
      "cvRobotTask": true,
      "cvRobotTaskNum": null,
      "debutChapterId": "",
      "debutChapterName": "",
      "debutChapterSeqNum": null,
      "description": "",
      "envMode": 0,
      "envType": 0,
      "eqConfig": null,
      "gainDb": 0,
      "gender": 1,
      "id": 3940831,
      "mixMode": null,
      "name": "拙荆",
      "nicknames": "",
      "numChapter": null,
      "numChar": null,
      "numCue": null,
      "presence": false,
      "robotDurationFactorSilence": 100,
      "robotGenMode": 0,
      "robotSpeedFactor": 100,
      "robotStyle": "",
      "robotStyleStandard": null,
      "selectedMixMode": 1,
      "type": null
    },
    {
      "ableToMerge": true,
      "ageType": 4,
      "autoBind": false,
      "autoMatch": true,
      "autoMixMode": 0,
      "color": "#FCB038",
      "createdTime": 1737880142100,
      "cvHumanId": null,
      "cvHumanName": "",
      "cvHumanTask": true,
      "cvHumanTaskNum": null,
      "cvRobotId": null,
      "cvRobotName": "",
      "cvRobotTask": true,
      "cvRobotTaskNum": null,
      "debutChapterId": "",
      "debutChapterName": "",
      "debutChapterSeqNum": null,
      "description": "",
      "envMode": 0,
      "envType": 0,
      "eqConfig": null,
      "gainDb": 0,
      "gender": 1,
      "id": 3940832,
      "mixMode": null,
      "name": "刘老爷",
      "nicknames": "",
      "numChapter": null,
      "numChar": null,
      "numCue": null,
      "presence": false,
      "robotDurationFactorSilence": 100,
      "robotGenMode": 0,
      "robotSpeedFactor": 100,
      "robotStyle": "",
      "robotStyleStandard": null,
      "selectedMixMode": 1,
      "type": null
    },
    {
      "ableToMerge": true,
      "ageType": 2,
      "autoBind": false,
      "autoMatch": true,
      "autoMixMode": 0,
      "color": "#C9C6FF",
      "createdTime": 1737880142104,
      "cvHumanId": null,
      "cvHumanName": "",
      "cvHumanTask": true,
      "cvHumanTaskNum": null,
      "cvRobotId": null,
      "cvRobotName": "",
      "cvRobotTask": true,
      "cvRobotTaskNum": null,
      "debutChapterId": "",
      "debutChapterName": "",
      "debutChapterSeqNum": null,
      "description": "",
      "envMode": 0,
      "envType": 0,
      "eqConfig": null,
      "gainDb": 0,
      "gender": 1,
      "id": 3940833,
      "mixMode": null,
      "name": "门客",
      "nicknames": "",
      "numChapter": null,
      "numChar": null,
      "numCue": null,
      "presence": false,
      "robotDurationFactorSilence": 100,
      "robotGenMode": 0,
      "robotSpeedFactor": 100,
      "robotStyle": "",
      "robotStyleStandard": null,
      "selectedMixMode": 1,
      "type": null
    },
    {
      "ableToMerge": true,
      "ageType": 2,
      "autoBind": false,
      "autoMatch": true,
      "autoMixMode": 0,
      "color": "#8FFED8",
      "createdTime": 1737880145794,
      "cvHumanId": null,
      "cvHumanName": "",
      "cvHumanTask": true,
      "cvHumanTaskNum": null,
      "cvRobotId": null,
      "cvRobotName": "",
      "cvRobotTask": true,
      "cvRobotTaskNum": null,
      "debutChapterId": "",
      "debutChapterName": "",
      "debutChapterSeqNum": null,
      "description": "",
      "envMode": 0,
      "envType": 0,
      "eqConfig": null,
      "gainDb": 0,
      "gender": 1,
      "id": 3940834,
      "mixMode": null,
      "name": "杨冲",
      "nicknames": "",
      "numChapter": null,
      "numChar": null,
      "numCue": null,
      "presence": false,
      "robotDurationFactorSilence": 100,
      "robotGenMode": 0,
      "robotSpeedFactor": 100,
      "robotStyle": "",
      "robotStyleStandard": null,
      "selectedMixMode": 1,
      "type": null
    },
    {
      "ableToMerge": true,
      "ageType": 2,
      "autoBind": false,
      "autoMatch": true,
      "autoMixMode": 0,
      "color": "#AC88FF",
      "createdTime": 1737880145799,
      "cvHumanId": null,
      "cvHumanName": "",
      "cvHumanTask": true,
      "cvHumanTaskNum": null,
      "cvRobotId": null,
      "cvRobotName": "",
      "cvRobotTask": true,
      "cvRobotTaskNum": null,
      "debutChapterId": "",
      "debutChapterName": "",
      "debutChapterSeqNum": null,
      "description": "",
      "envMode": 0,
      "envType": 0,
      "eqConfig": null,
      "gainDb": 0,
      "gender": null,
      "id": 3940835,
      "mixMode": null,
      "name": "随从",
      "nicknames": "",
      "numChapter": null,
      "numChar": null,
      "numCue": null,
      "presence": false,
      "robotDurationFactorSilence": 100,
      "robotGenMode": 0,
      "robotSpeedFactor": 100,
      "robotStyle": "",
      "robotStyleStandard": null,
      "selectedMixMode": 1,
      "type": null
    },
    {
      "ableToMerge": true,
      "ageType": null,
      "autoBind": false,
      "autoMatch": true,
      "autoMixMode": 0,
      "color": "#FFF1EE",
      "createdTime": 1737880151406,
      "cvHumanId": null,
      "cvHumanName": "",
      "cvHumanTask": true,
      "cvHumanTaskNum": null,
      "cvRobotId": null,
      "cvRobotName": "",
      "cvRobotTask": true,
      "cvRobotTaskNum": null,
      "debutChapterId": "",
      "debutChapterName": "",
      "debutChapterSeqNum": null,
      "description": "",
      "envMode": 0,
      "envType": 0,
      "eqConfig": null,
      "gainDb": 0,
      "gender": null,
      "id": 3940836,
      "mixMode": null,
      "name": "众人",
      "nicknames": "",
      "numChapter": null,
      "numChar": null,
      "numCue": null,
      "presence": false,
      "robotDurationFactorSilence": 100,
      "robotGenMode": 0,
      "robotSpeedFactor": 100,
      "robotStyle": "",
      "robotStyleStandard": null,
      "selectedMixMode": 1,
      "type": null
    },
    {
      "ableToMerge": true,
      "ageType": null,
      "autoBind": false,
      "autoMatch": true,
      "autoMixMode": 0,
      "color": "#8FA8FE",
      "createdTime": 1737880160815,
      "cvHumanId": null,
      "cvHumanName": "",
      "cvHumanTask": true,
      "cvHumanTaskNum": null,
      "cvRobotId": null,
      "cvRobotName": "",
      "cvRobotTask": true,
      "cvRobotTaskNum": null,
      "debutChapterId": "",
      "debutChapterName": "",
      "debutChapterSeqNum": null,
      "description": "",
      "envMode": 0,
      "envType": 0,
      "eqConfig": null,
      "gainDb": 0,
      "gender": 1,
      "id": 3940837,
      "mixMode": null,
      "name": "寒衣秀士",
      "nicknames": "",
      "numChapter": null,
      "numChar": null,
      "numCue": null,
      "presence": false,
      "robotDurationFactorSilence": 100,
      "robotGenMode": 0,
      "robotSpeedFactor": 100,
      "robotStyle": "",
      "robotStyleStandard": null,
      "selectedMixMode": 1,
      "type": null
    },
    {
      "ableToMerge": true,
      "ageType": 2,
      "autoBind": false,
      "autoMatch": true,
      "autoMixMode": 0,
      "color": "#B7DDE4",
      "createdTime": 1737880494557,
      "cvHumanId": null,
      "cvHumanName": "",
      "cvHumanTask": true,
      "cvHumanTaskNum": null,
      "cvRobotId": null,
      "cvRobotName": "",
      "cvRobotTask": true,
      "cvRobotTaskNum": null,
      "debutChapterId": "",
      "debutChapterName": "",
      "debutChapterSeqNum": null,
      "description": "",
      "envMode": 0,
      "envType": 0,
      "eqConfig": null,
      "gainDb": 0,
      "gender": 2,
      "id": 3940846,
      "mixMode": null,
      "name": "兰儿",
      "nicknames": "",
      "numChapter": null,
      "numChar": null,
      "numCue": null,
      "presence": false,
      "robotDurationFactorSilence": 100,
      "robotGenMode": 0,
      "robotSpeedFactor": 100,
      "robotStyle": "",
      "robotStyleStandard": null,
      "selectedMixMode": 1,
      "type": null
    },
    {
      "ableToMerge": true,
      "ageType": null,
      "autoBind": false,
      "autoMatch": true,
      "autoMixMode": 0,
      "color": "#F3B6B6",
      "createdTime": 1737880496740,
      "cvHumanId": null,
      "cvHumanName": "",
      "cvHumanTask": true,
      "cvHumanTaskNum": null,
      "cvRobotId": null,
      "cvRobotName": "",
      "cvRobotTask": true,
      "cvRobotTaskNum": null,
      "debutChapterId": "",
      "debutChapterName": "",
      "debutChapterSeqNum": null,
      "description": "",
      "envMode": 0,
      "envType": 0,
      "eqConfig": null,
      "gainDb": 0,
      "gender": 2,
      "id": 3940847,
      "mixMode": null,
      "name": "老婆婆",
      "nicknames": "",
      "numChapter": null,
      "numChar": null,
      "numCue": null,
      "presence": false,
      "robotDurationFactorSilence": 100,
      "robotGenMode": 0,
      "robotSpeedFactor": 100,
      "robotStyle": "",
      "robotStyleStandard": null,
      "selectedMixMode": 1,
      "type": null
    },
    {
      "ableToMerge": true,
      "ageType": 2,
      "autoBind": false,
      "autoMatch": true,
      "autoMixMode": 0,
      "color": "#79EF6B",
      "createdTime": 1737880503822,
      "cvHumanId": null,
      "cvHumanName": "",
      "cvHumanTask": true,
      "cvHumanTaskNum": null,
      "cvRobotId": null,
      "cvRobotName": "",
      "cvRobotTask": true,
      "cvRobotTaskNum": null,
      "debutChapterId": "",
      "debutChapterName": "",
      "debutChapterSeqNum": null,
      "description": "",
      "envMode": 0,
      "envType": 0,
      "eqConfig": null,
      "gainDb": 0,
      "gender": 1,
      "id": 3940849,
      "mixMode": null,
      "name": "杨军",
      "nicknames": "",
      "numChapter": null,
      "numChar": null,
      "numCue": null,
      "presence": false,
      "robotDurationFactorSilence": 100,
      "robotGenMode": 0,
      "robotSpeedFactor": 100,
      "robotStyle": "",
      "robotStyleStandard": null,
      "selectedMixMode": 1,
      "type": null
    },
    {
      "ableToMerge": true,
      "ageType": 2,
      "autoBind": false,
      "autoMatch": true,
      "autoMixMode": 0,
      "color": "#A9DCFF",
      "createdTime": 1737880505994,
      "cvHumanId": null,
      "cvHumanName": "",
      "cvHumanTask": true,
      "cvHumanTaskNum": null,
      "cvRobotId": null,
      "cvRobotName": "",
      "cvRobotTask": true,
      "cvRobotTaskNum": null,
      "debutChapterId": "",
      "debutChapterName": "",
      "debutChapterSeqNum": null,
      "description": "",
      "envMode": 0,
      "envType": 0,
      "eqConfig": null,
      "gainDb": 0,
      "gender": 1,
      "id": 3940850,
      "mixMode": null,
      "name": "黑衣男子",
      "nicknames": "",
      "numChapter": null,
      "numChar": null,
      "numCue": null,
      "presence": false,
      "robotDurationFactorSilence": 100,
      "robotGenMode": 0,
      "robotSpeedFactor": 100,
      "robotStyle": "",
      "robotStyleStandard": null,
      "selectedMixMode": 1,
      "type": null
    },
    {
      "ableToMerge": true,
      "ageType": null,
      "autoBind": false,
      "autoMatch": true,
      "autoMixMode": 0,
      "color": "#FFE617",
      "createdTime": 1737880506002,
      "cvHumanId": null,
      "cvHumanName": "",
      "cvHumanTask": true,
      "cvHumanTaskNum": null,
      "cvRobotId": null,
      "cvRobotName": "",
      "cvRobotTask": true,
      "cvRobotTaskNum": null,
      "debutChapterId": "",
      "debutChapterName": "",
      "debutChapterSeqNum": null,
      "description": "",
      "envMode": 0,
      "envType": 0,
      "eqConfig": null,
      "gainDb": 0,
      "gender": 1,
      "id": 3940851,
      "mixMode": null,
      "name": "兰花无敌",
      "nicknames": "",
      "numChapter": null,
      "numChar": null,
      "numCue": null,
      "presence": false,
      "robotDurationFactorSilence": 100,
      "robotGenMode": 0,
      "robotSpeedFactor": 100,
      "robotStyle": "",
      "robotStyleStandard": null,
      "selectedMixMode": 1,
      "type": null
    },
    {
      "ableToMerge": true,
      "ageType": 2,
      "autoBind": false,
      "autoMatch": true,
      "autoMixMode": 0,
      "color": "#ff87a7",
      "createdTime": 1737880506006,
      "cvHumanId": null,
      "cvHumanName": "",
      "cvHumanTask": true,
      "cvHumanTaskNum": null,
      "cvRobotId": null,
      "cvRobotName": "",
      "cvRobotTask": true,
      "cvRobotTaskNum": null,
      "debutChapterId": "",
      "debutChapterName": "",
      "debutChapterSeqNum": null,
      "description": "",
      "envMode": 0,
      "envType": 0,
      "eqConfig": null,
      "gainDb": 0,
      "gender": 1,
      "id": 3940852,
      "mixMode": null,
      "name": "男子",
      "nicknames": "",
      "numChapter": null,
      "numChar": null,
      "numCue": null,
      "presence": false,
      "robotDurationFactorSilence": 100,
      "robotGenMode": 0,
      "robotSpeedFactor": 100,
      "robotStyle": "",
      "robotStyleStandard": null,
      "selectedMixMode": 1,
      "type": null
    },
    {
      "ableToMerge": true,
      "ageType": null,
      "autoBind": false,
      "autoMatch": true,
      "autoMixMode": 0,
      "color": "#B4EDAD",
      "createdTime": 1737880506009,
      "cvHumanId": null,
      "cvHumanName": "",
      "cvHumanTask": true,
      "cvHumanTaskNum": null,
      "cvRobotId": null,
      "cvRobotName": "",
      "cvRobotTask": true,
      "cvRobotTaskNum": null,
      "debutChapterId": "",
      "debutChapterName": "",
      "debutChapterSeqNum": null,
      "description": "",
      "envMode": 0,
      "envType": 0,
      "eqConfig": null,
      "gainDb": 0,
      "gender": 1,
      "id": 3940853,
      "mixMode": null,
      "name": "大人",
      "nicknames": "",
      "numChapter": null,
      "numChar": null,
      "numCue": null,
      "presence": false,
      "robotDurationFactorSilence": 100,
      "robotGenMode": 0,
      "robotSpeedFactor": 100,
      "robotStyle": "",
      "robotStyleStandard": null,
      "selectedMixMode": 1,
      "type": null
    },
    {
      "ableToMerge": true,
      "ageType": null,
      "autoBind": false,
      "autoMatch": true,
      "autoMixMode": 0,
      "color": "#FDA58E",
      "createdTime": 1737880508484,
      "cvHumanId": null,
      "cvHumanName": "",
      "cvHumanTask": true,
      "cvHumanTaskNum": null,
      "cvRobotId": null,
      "cvRobotName": "",
      "cvRobotTask": true,
      "cvRobotTaskNum": null,
      "debutChapterId": "",
      "debutChapterName": "",
      "debutChapterSeqNum": null,
      "description": "",
      "envMode": 0,
      "envType": 0,
      "eqConfig": null,
      "gainDb": 0,
      "gender": 1,
      "id": 3940854,
      "mixMode": null,
      "name": "张大人",
      "nicknames": "",
      "numChapter": null,
      "numChar": null,
      "numCue": null,
      "presence": false,
      "robotDurationFactorSilence": 100,
      "robotGenMode": 0,
      "robotSpeedFactor": 100,
      "robotStyle": "",
      "robotStyleStandard": null,
      "selectedMixMode": 1,
      "type": null
    },
    {
      "ableToMerge": true,
      "ageType": 2,
      "autoBind": false,
      "autoMatch": true,
      "autoMixMode": 0,
      "color": "#90c7ed",
      "createdTime": 1737880508487,
      "cvHumanId": null,
      "cvHumanName": "",
      "cvHumanTask": true,
      "cvHumanTaskNum": null,
      "cvRobotId": null,
      "cvRobotName": "",
      "cvRobotTask": true,
      "cvRobotTaskNum": null,
      "debutChapterId": "",
      "debutChapterName": "",
      "debutChapterSeqNum": null,
      "description": "",
      "envMode": 0,
      "envType": 0,
      "eqConfig": null,
      "gainDb": 0,
      "gender": 1,
      "id": 3940855,
      "mixMode": null,
      "name": "张路",
      "nicknames": "",
      "numChapter": null,
      "numChar": null,
      "numCue": null,
      "presence": false,
      "robotDurationFactorSilence": 100,
      "robotGenMode": 0,
      "robotSpeedFactor": 100,
      "robotStyle": "",
      "robotStyleStandard": null,
      "selectedMixMode": 1,
      "type": null
    },
    {
      "ableToMerge": true,
      "ageType": null,
      "autoBind": false,
      "autoMatch": true,
      "autoMixMode": 0,
      "color": "#D5FE84",
      "createdTime": 1737880510878,
      "cvHumanId": null,
      "cvHumanName": "",
      "cvHumanTask": true,
      "cvHumanTaskNum": null,
      "cvRobotId": null,
      "cvRobotName": "",
      "cvRobotTask": true,
      "cvRobotTaskNum": null,
      "debutChapterId": "",
      "debutChapterName": "",
      "debutChapterSeqNum": null,
      "description": "",
      "envMode": 0,
      "envType": 0,
      "eqConfig": null,
      "gainDb": 0,
      "gender": 1,
      "id": 3940856,
      "mixMode": null,
      "name": "义父",
      "nicknames": "",
      "numChapter": null,
      "numChar": null,
      "numCue": null,
      "presence": false,
      "robotDurationFactorSilence": 100,
      "robotGenMode": 0,
      "robotSpeedFactor": 100,
      "robotStyle": "",
      "robotStyleStandard": null,
      "selectedMixMode": 1,
      "type": null
    },
    {
      "ableToMerge": true,
      "ageType": 2,
      "autoBind": false,
      "autoMatch": true,
      "autoMixMode": 0,
      "color": "#FF8686",
      "createdTime": 1737880510886,
      "cvHumanId": null,
      "cvHumanName": "",
      "cvHumanTask": true,
      "cvHumanTaskNum": null,
      "cvRobotId": null,
      "cvRobotName": "",
      "cvRobotTask": true,
      "cvRobotTaskNum": null,
      "debutChapterId": "",
      "debutChapterName": "",
      "debutChapterSeqNum": null,
      "description": "",
      "envMode": 0,
      "envType": 0,
      "eqConfig": null,
      "gainDb": 0,
      "gender": 2,
      "id": 3940857,
      "mixMode": null,
      "name": "青儿",
      "nicknames": "",
      "numChapter": null,
      "numChar": null,
      "numCue": null,
      "presence": false,
      "robotDurationFactorSilence": 100,
      "robotGenMode": 0,
      "robotSpeedFactor": 100,
      "robotStyle": "",
      "robotStyleStandard": null,
      "selectedMixMode": 1,
      "type": null
    },
    {
      "ableToMerge": true,
      "ageType": 2,
      "autoBind": false,
      "autoMatch": true,
      "autoMixMode": 0,
      "color": "#00B4FF",
      "createdTime": 1737880514020,
      "cvHumanId": null,
      "cvHumanName": "",
      "cvHumanTask": true,
      "cvHumanTaskNum": null,
      "cvRobotId": null,
      "cvRobotName": "",
      "cvRobotTask": true,
      "cvRobotTaskNum": null,
      "debutChapterId": "",
      "debutChapterName": "",
      "debutChapterSeqNum": null,
      "description": "",
      "envMode": 0,
      "envType": 0,
      "eqConfig": null,
      "gainDb": 0,
      "gender": 2,
      "id": 3940859,
      "mixMode": null,
      "name": "怡姐",
      "nicknames": "",
      "numChapter": null,
      "numChar": null,
      "numCue": null,
      "presence": false,
      "robotDurationFactorSilence": 100,
      "robotGenMode": 0,
      "robotSpeedFactor": 100,
      "robotStyle": "",
      "robotStyleStandard": null,
      "selectedMixMode": 1,
      "type": null
    },
    {
      "ableToMerge": true,
      "ageType": null,
      "autoBind": false,
      "autoMatch": true,
      "autoMixMode": 0,
      "color": "#D9B3F9",
      "createdTime": 1737880514025,
      "cvHumanId": null,
      "cvHumanName": "",
      "cvHumanTask": true,
      "cvHumanTaskNum": null,
      "cvRobotId": null,
      "cvRobotName": "",
      "cvRobotTask": true,
      "cvRobotTaskNum": null,
      "debutChapterId": "",
      "debutChapterName": "",
      "debutChapterSeqNum": null,
      "description": "",
      "envMode": 0,
      "envType": 0,
      "eqConfig": null,
      "gainDb": 0,
      "gender": 1,
      "id": 3940860,
      "mixMode": null,
      "name": "另一军官",
      "nicknames": "",
      "numChapter": null,
      "numChar": null,
      "numCue": null,
      "presence": false,
      "robotDurationFactorSilence": 100,
      "robotGenMode": 0,
      "robotSpeedFactor": 100,
      "robotStyle": "",
      "robotStyleStandard": null,
      "selectedMixMode": 1,
      "type": null
    },
    {
      "ableToMerge": true,
      "ageType": null,
      "autoBind": false,
      "autoMatch": true,
      "autoMixMode": 0,
      "color": "#FFDDA7",
      "createdTime": 1737880514029,
      "cvHumanId": null,
      "cvHumanName": "",
      "cvHumanTask": true,
      "cvHumanTaskNum": null,
      "cvRobotId": null,
      "cvRobotName": "",
      "cvRobotTask": true,
      "cvRobotTaskNum": null,
      "debutChapterId": "",
      "debutChapterName": "",
      "debutChapterSeqNum": null,
      "description": "",
      "envMode": 0,
      "envType": 0,
      "eqConfig": null,
      "gainDb": 0,
      "gender": 2,
      "id": 3940861,
      "mixMode": null,
      "name": "分中",
      "nicknames": "",
      "numChapter": null,
      "numChar": null,
      "numCue": null,
      "presence": false,
      "robotDurationFactorSilence": 100,
      "robotGenMode": 0,
      "robotSpeedFactor": 100,
      "robotStyle": "",
      "robotStyleStandard": null,
      "selectedMixMode": 1,
      "type": null
    },
    {
      "ableToMerge": true,
      "ageType": null,
      "autoBind": false,
      "autoMatch": true,
      "autoMixMode": 0,
      "color": "#E0F6F2",
      "createdTime": 1737880515440,
      "cvHumanId": null,
      "cvHumanName": "",
      "cvHumanTask": true,
      "cvHumanTaskNum": null,
      "cvRobotId": null,
      "cvRobotName": "",
      "cvRobotTask": true,
      "cvRobotTaskNum": null,
      "debutChapterId": "",
      "debutChapterName": "",
      "debutChapterSeqNum": null,
      "description": "",
      "envMode": 0,
      "envType": 0,
      "eqConfig": null,
      "gainDb": 0,
      "gender": 1,
      "id": 3940862,
      "mixMode": null,
      "name": "刑吏",
      "nicknames": "",
      "numChapter": null,
      "numChar": null,
      "numCue": null,
      "presence": false,
      "robotDurationFactorSilence": 100,
      "robotGenMode": 0,
      "robotSpeedFactor": 100,
      "robotStyle": "",
      "robotStyleStandard": null,
      "selectedMixMode": 1,
      "type": null
    },
    {
      "ableToMerge": true,
      "ageType": null,
      "autoBind": false,
      "autoMatch": true,
      "autoMixMode": 0,
      "color": "#F3F4A3",
      "createdTime": 1737880515444,
      "cvHumanId": null,
      "cvHumanName": "",
      "cvHumanTask": true,
      "cvHumanTaskNum": null,
      "cvRobotId": null,
      "cvRobotName": "",
      "cvRobotTask": true,
      "cvRobotTaskNum": null,
      "debutChapterId": "",
      "debutChapterName": "",
      "debutChapterSeqNum": null,
      "description": "",
      "envMode": 0,
      "envType": 0,
      "eqConfig": null,
      "gainDb": 0,
      "gender": 1,
      "id": 3940863,
      "mixMode": null,
      "name": "金兄",
      "nicknames": "",
      "numChapter": null,
      "numChar": null,
      "numCue": null,
      "presence": false,
      "robotDurationFactorSilence": 100,
      "robotGenMode": 0,
      "robotSpeedFactor": 100,
      "robotStyle": "",
      "robotStyleStandard": null,
      "selectedMixMode": 1,
      "type": null
    },
    {
      "ableToMerge": true,
      "ageType": null,
      "autoBind": false,
      "autoMatch": true,
      "autoMixMode": 0,
      "color": "#88D7EB",
      "createdTime": 1737880519112,
      "cvHumanId": null,
      "cvHumanName": "",
      "cvHumanTask": true,
      "cvHumanTaskNum": null,
      "cvRobotId": null,
      "cvRobotName": "",
      "cvRobotTask": true,
      "cvRobotTaskNum": null,
      "debutChapterId": "",
      "debutChapterName": "",
      "debutChapterSeqNum": null,
      "description": "",
      "envMode": 0,
      "envType": 0,
      "eqConfig": null,
      "gainDb": 0,
      "gender": 1,
      "id": 3940864,
      "mixMode": null,
      "name": "张路正",
      "nicknames": "",
      "numChapter": null,
      "numChar": null,
      "numCue": null,
      "presence": false,
      "robotDurationFactorSilence": 100,
      "robotGenMode": 0,
      "robotSpeedFactor": 100,
      "robotStyle": "",
      "robotStyleStandard": null,
      "selectedMixMode": 1,
      "type": null
    },
    {
      "ableToMerge": true,
      "ageType": null,
      "autoBind": false,
      "autoMatch": true,
      "autoMixMode": 0,
      "color": "#F1E0FF",
      "createdTime": 1737880519116,
      "cvHumanId": null,
      "cvHumanName": "",
      "cvHumanTask": true,
      "cvHumanTaskNum": null,
      "cvRobotId": null,
      "cvRobotName": "",
      "cvRobotTask": true,
      "cvRobotTaskNum": null,
      "debutChapterId": "",
      "debutChapterName": "",
      "debutChapterSeqNum": null,
      "description": "",
      "envMode": 0,
      "envType": 0,
      "eqConfig": null,
      "gainDb": 0,
      "gender": 1,
      "id": 3940865,
      "mixMode": null,
      "name": "张路虎",
      "nicknames": "",
      "numChapter": null,
      "numChar": null,
      "numCue": null,
      "presence": false,
      "robotDurationFactorSilence": 100,
      "robotGenMode": 0,
      "robotSpeedFactor": 100,
      "robotStyle": "",
      "robotStyleStandard": null,
      "selectedMixMode": 1,
      "type": null
    },
    {
      "ableToMerge": true,
      "ageType": null,
      "autoBind": false,
      "autoMatch": true,
      "autoMixMode": 0,
      "color": "#E3F3C3",
      "createdTime": 1737880520578,
      "cvHumanId": null,
      "cvHumanName": "",
      "cvHumanTask": true,
      "cvHumanTaskNum": null,
      "cvRobotId": null,
      "cvRobotName": "",
      "cvRobotTask": true,
      "cvRobotTaskNum": null,
      "debutChapterId": "",
      "debutChapterName": "",
      "debutChapterSeqNum": null,
      "description": "",
      "envMode": 0,
      "envType": 0,
      "eqConfig": null,
      "gainDb": 0,
      "gender": 1,
      "id": 3940866,
      "mixMode": null,
      "name": "张来",
      "nicknames": "",
      "numChapter": null,
      "numChar": null,
      "numCue": null,
      "presence": false,
      "robotDurationFactorSilence": 100,
      "robotGenMode": 0,
      "robotSpeedFactor": 100,
      "robotStyle": "",
      "robotStyleStandard": null,
      "selectedMixMode": 1,
      "type": null
    },
    {
      "ableToMerge": true,
      "ageType": 2,
      "autoBind": false,
      "autoMatch": true,
      "autoMixMode": 0,
      "color": "#F19AC9",
      "createdTime": 1737880529832,
      "cvHumanId": null,
      "cvHumanName": "",
      "cvHumanTask": true,
      "cvHumanTaskNum": null,
      "cvRobotId": null,
      "cvRobotName": "",
      "cvRobotTask": true,
      "cvRobotTaskNum": null,
      "debutChapterId": "",
      "debutChapterName": "",
      "debutChapterSeqNum": null,
      "description": "",
      "envMode": 0,
      "envType": 0,
      "eqConfig": null,
      "gainDb": 0,
      "gender": 2,
      "id": 3940867,
      "mixMode": null,
      "name": "俏婢",
      "nicknames": "",
      "numChapter": null,
      "numChar": null,
      "numCue": null,
      "presence": false,
      "robotDurationFactorSilence": 100,
      "robotGenMode": 0,
      "robotSpeedFactor": 100,
      "robotStyle": "",
      "robotStyleStandard": null,
      "selectedMixMode": 1,
      "type": null
    },
    {
      "ableToMerge": true,
      "ageType": 2,
      "autoBind": false,
      "autoMatch": true,
      "autoMixMode": 0,
      "color": "#FCB038",
      "createdTime": 1737880529837,
      "cvHumanId": null,
      "cvHumanName": "",
      "cvHumanTask": true,
      "cvHumanTaskNum": null,
      "cvRobotId": null,
      "cvRobotName": "",
      "cvRobotTask": true,
      "cvRobotTaskNum": null,
      "debutChapterId": "",
      "debutChapterName": "",
      "debutChapterSeqNum": null,
      "description": "",
      "envMode": 0,
      "envType": 0,
      "eqConfig": null,
      "gainDb": 0,
      "gender": null,
      "id": 3940868,
      "mixMode": null,
      "name": "美婢",
      "nicknames": "",
      "numChapter": null,
      "numChar": null,
      "numCue": null,
      "presence": false,
      "robotDurationFactorSilence": 100,
      "robotGenMode": 0,
      "robotSpeedFactor": 100,
      "robotStyle": "",
      "robotStyleStandard": null,
      "selectedMixMode": 1,
      "type": null
    }
  ],
  "msg": "成功!"
}


==================================================
=== 请求信息 ===
时间: 2025-07-24 07:23:45
方法: GET
URL: https://www.gstudios.com.cn/story_v2/api/content/character/list/book?bookId=29658&roleType=book&skipStat=true
备注标题: 获取书籍角色列表
备注内容: 
Headers: {
  "host": "www.gstudios.com.cn",
  "connection": "keep-alive",
  "sec-ch-ua-platform": "Windows",
  "x-requested-with": "XMLHttpRequest",
  "user-agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********",
  "accept": "application/json, text/plain, */*",
  "sec-ch-ua": "\"Microsoft Edge\";v=\"135\", \"Not-A.Brand\";v=\"8\", \"Chromium\";v=\"135\"",
  "sec-ch-ua-mobile": "?0",
  "sec-fetch-site": "same-origin",
  "sec-fetch-mode": "cors",
  "sec-fetch-dest": "empty",
  "accept-encoding": "gzip, deflate, br, zstd",
  "accept-language": "zh-CN,zh;q=0.9,en;q=0.8,en-GB;q=0.7,en-US;q=0.6",
  "referer": "https://www.gstudios.com.cn/",
  "authorization": "Bearer 3dfb89119562456cb8818120139f6ae1"
}
Cookie: 
=== 响应信息 ===
状态码: 200
响应头: {'Server': 'CLOUD ELB 1.0.0', 'Date': 'Wed, 23 Jul 2025 23:23:44 GMT', 'Content-Type': 'application/json;charset=UTF-8', 'Transfer-Encoding': 'chunked', 'Connection': 'close', 'Content-Encoding': 'gzip'}

=== 响应内容 ===
{
  "code": 1,
  "data": [
    {
      "ableToMerge": false,
      "ageType": null,
      "autoBind": true,
      "autoMatch": true,
      "autoMixMode": 0,
      "color": "#CFD5E9",
      "createdTime": 1737879987820,
      "cvHumanId": null,
      "cvHumanName": "",
      "cvHumanTask": true,
      "cvHumanTaskNum": null,
      "cvRobotId": 568,
      "cvRobotName": "曹先森MK-III",
      "cvRobotTask": true,
      "cvRobotTaskNum": null,
      "debutChapterId": "",
      "debutChapterName": "",
      "debutChapterSeqNum": null,
      "description": "",
      "envMode": 0,
      "envType": 0,
      "eqConfig": {
        "bp1": {
          "f": 200,
          "g": 0,
          "on": false,
          "q": 2
        },
        "bp2": {
          "f": 800,
          "g": 0,
          "on": false,
          "q": 2
        },
        "bp3": {
          "f": 3200,
          "g": 0,
          "on": false,
          "q": 2
        },
        "hs": {
          "f": 18000,
          "g": 0,
          "on": false,
          "s": 1
        },
        "ls": {
          "f": 40,
          "g": 0,
          "on": false,
          "s": 1
        }
      },
      "gainDb": 0,
      "gender": null,
      "id": 3940779,
      "mixMode": null,
      "name": "旁白",
      "nicknames": "",
      "numChapter": null,
      "numChar": null,
      "numCue": null,
      "presence": false,
      "robotDurationFactorSilence": 100,
      "robotGenMode": 0,
      "robotSpeedFactor": 100,
      "robotStyle": "",
      "robotStyleStandard": null,
      "selectedMixMode": 0,
      "type": 0
    },
    {
      "ableToMerge": true,
      "ageType": 2,
      "autoBind": false,
      "autoMatch": true,
      "autoMixMode": 0,
      "color": "#B0F7FF",
      "createdTime": 1737880040231,
      "cvHumanId": null,
      "cvHumanName": "",
      "cvHumanTask": true,
      "cvHumanTaskNum": null,
      "cvRobotId": null,
      "cvRobotName": "",
      "cvRobotTask": true,
      "cvRobotTaskNum": null,
      "debutChapterId": "",
      "debutChapterName": "",
      "debutChapterSeqNum": null,
      "description": "",
      "envMode": 0,
      "envType": 0,
      "eqConfig": {
        "bp1": {
          "f": 200,
          "g": 0,
          "on": false,
          "q": 2
        },
        "bp2": {
          "f": 800,
          "g": 0,
          "on": false,
          "q": 2
        },
        "bp3": {
          "f": 3200,
          "g": 0,
          "on": false,
          "q": 2
        },
        "hs": {
          "f": 18000,
          "g": 0,
          "on": false,
          "s": 1
        },
        "ls": {
          "f": 40,
          "g": 0,
          "on": false,
          "s": 1
        }
      },
      "gainDb": 0,
      "gender": 1,
      "id": 3940780,
      "mixMode": null,
      "name": "叶锋",
      "nicknames": "",
      "numChapter": null,
      "numChar": null,
      "numCue": null,
      "presence": false,
      "robotDurationFactorSilence": 100,
      "robotGenMode": 0,
      "robotSpeedFactor": 100,
      "robotStyle": "",
      "robotStyleStandard": null,
      "selectedMixMode": 1,
      "type": 1
    },
    {
      "ableToMerge": true,
      "ageType": 4,
      "autoBind": false,
      "autoMatch": true,
      "autoMixMode": 0,
      "color": "#A9DCFF",
      "createdTime": 1737880049430,
      "cvHumanId": null,
      "cvHumanName": "",
      "cvHumanTask": true,
      "cvHumanTaskNum": null,
      "cvRobotId": null,
      "cvRobotName": "",
      "cvRobotTask": true,
      "cvRobotTaskNum": null,
      "debutChapterId": "",
      "debutChapterName": "",
      "debutChapterSeqNum": null,
      "description": "收留主角的玉月湖边的渔民，大约五六十岁。身材非常高大，体态身高比得上叶锋。发须花白，慈眉善目，穿着一件怪模怪样",
      "envMode": 0,
      "envType": 0,
      "eqConfig": {
        "bp1": {
          "f": 200,
          "g": 0,
          "on": false,
          "q": 2
        },
        "bp2": {
          "f": 800,
          "g": 0,
          "on": false,
          "q": 2
        },
        "bp3": {
          "f": 3200,
          "g": 0,
          "on": false,
          "q": 2
        },
        "hs": {
          "f": 18000,
          "g": 0,
          "on": false,
          "s": 1
        },
        "ls": {
          "f": 40,
          "g": 0,
          "on": false,
          "s": 1
        }
      },
      "gainDb": 0,
      "gender": 1,
      "id": 3940785,
      "mixMode": null,
      "name": "李大爷",
      "nicknames": "",
      "numChapter": null,
      "numChar": null,
      "numCue": null,
      "presence": false,
      "robotDurationFactorSilence": 100,
      "robotGenMode": 0,
      "robotSpeedFactor": 100,
      "robotStyle": "",
      "robotStyleStandard": null,
      "selectedMixMode": 1,
      "type": 2
    },
    {
      "ableToMerge": true,
      "ageType": 2,
      "autoBind": false,
      "autoMatch": true,
      "autoMixMode": 0,
      "color": "#90c7ed",
      "createdTime": 1737880056772,
      "cvHumanId": null,
      "cvHumanName": "",
      "cvHumanTask": true,
      "cvHumanTaskNum": null,
      "cvRobotId": null,
      "cvRobotName": "",
      "cvRobotTask": true,
      "cvRobotTaskNum": null,
      "debutChapterId": "",
      "debutChapterName": "",
      "debutChapterSeqNum": null,
      "description": "玉月节度使李会伟的妹妹，乃是大月国远近闻名的艳女。这李音虽然生活放浪，但却身怀绝技，且极具谋略，手下更有无数的奇人异土，曾立下大功无数",
      "envMode": 0,
      "envType": 0,
      "eqConfig": {
        "bp1": {
          "f": 200,
          "g": 0,
          "on": false,
          "q": 2
        },
        "bp2": {
          "f": 800,
          "g": 0,
          "on": false,
          "q": 2
        },
        "bp3": {
          "f": 3200,
          "g": 0,
          "on": false,
          "q": 2
        },
        "hs": {
          "f": 18000,
          "g": 0,
          "on": false,
          "s": 1
        },
        "ls": {
          "f": 40,
          "g": 0,
          "on": false,
          "s": 1
        }
      },
      "gainDb": 0,
      "gender": 2,
      "id": 3940790,
      "mixMode": null,
      "name": "李音",
      "nicknames": "",
      "numChapter": null,
      "numChar": null,
      "numCue": null,
      "presence": false,
      "robotDurationFactorSilence": 100,
      "robotGenMode": 0,
      "robotSpeedFactor": 100,
      "robotStyle": "",
      "robotStyleStandard": null,
      "selectedMixMode": 1,
      "type": 2
    },
    {
      "ableToMerge": true,
      "ageType": 2,
      "autoBind": false,
      "autoMatch": true,
      "autoMixMode": 0,
      "color": "#00B4FF",
      "createdTime": 1737880066001,
      "cvHumanId": null,
      "cvHumanName": "",
      "cvHumanTask": true,
      "cvHumanTaskNum": null,
      "cvRobotId": null,
      "cvRobotName": "",
      "cvRobotTask": true,
      "cvRobotTaskNum": null,
      "debutChapterId": "",
      "debutChapterName": "",
      "debutChapterSeqNum": null,
      "description": "花怡原名花兰，原是兰花国的公主，",
      "envMode": 0,
      "envType": 0,
      "eqConfig": {
        "bp1": {
          "f": 200,
          "g": 0,
          "on": false,
          "q": 2
        },
        "bp2": {
          "f": 800,
          "g": 0,
          "on": false,
          "q": 2
        },
        "bp3": {
          "f": 3200,
          "g": 0,
          "on": false,
          "q": 2
        },
        "hs": {
          "f": 18000,
          "g": 0,
          "on": false,
          "s": 1
        },
        "ls": {
          "f": 40,
          "g": 0,
          "on": false,
          "s": 1
        }
      },
      "gainDb": 0,
      "gender": 2,
      "id": 3940793,
      "mixMode": null,
      "name": "花怡",
      "nicknames": "",
      "numChapter": null,
      "numChar": null,
      "numCue": null,
      "presence": false,
      "robotDurationFactorSilence": 100,
      "robotGenMode": 0,
      "robotSpeedFactor": 100,
      "robotStyle": "",
      "robotStyleStandard": null,
      "selectedMixMode": 1,
      "type": 2
    },
    {
      "ableToMerge": true,
      "ageType": 3,
      "autoBind": false,
      "autoMatch": true,
      "autoMixMode": 0,
      "color": "#C9C6FF",
      "createdTime": 1739245057529,
      "cvHumanId": null,
      "cvHumanName": "",
      "cvHumanTask": true,
      "cvHumanTaskNum": null,
      "cvRobotId": null,
      "cvRobotName": "",
      "cvRobotTask": true,
      "cvRobotTaskNum": null,
      "debutChapterId": "",
      "debutChapterName": "",
      "debutChapterSeqNum": null,
      "description": "满脸横肉，眼睛细小，年约四十左右，一身蓝色长袍的肥胖大汉",
      "envMode": 0,
      "envType": 0,
      "eqConfig": {
        "bp1": {
          "f": 200,
          "g": 0,
          "on": false,
          "q": 2
        },
        "bp2": {
          "f": 800,
          "g": 0,
          "on": false,
          "q": 2
        },
        "bp3": {
          "f": 3200,
          "g": 0,
          "on": false,
          "q": 2
        },
        "hs": {
          "f": 18000,
          "g": 0,
          "on": false,
          "s": 1
        },
        "ls": {
          "f": 40,
          "g": 0,
          "on": false,
          "s": 1
        }
      },
      "gainDb": 0,
      "gender": 1,
      "id": 4037287,
      "mixMode": null,
      "name": "李虎",
      "nicknames": "",
      "numChapter": null,
      "numChar": null,
      "numCue": null,
      "presence": false,
      "robotDurationFactorSilence": 100,
      "robotGenMode": 0,
      "robotSpeedFactor": 100,
      "robotStyle": "",
      "robotStyleStandard": null,
      "selectedMixMode": 1,
      "type": 2
    },
    {
      "ableToMerge": true,
      "ageType": 3,
      "autoBind": false,
      "autoMatch": true,
      "autoMixMode": 0,
      "color": "#FFE617",
      "createdTime": 1737880051343,
      "cvHumanId": null,
      "cvHumanName": "",
      "cvHumanTask": true,
      "cvHumanTaskNum": null,
      "cvRobotId": null,
      "cvRobotName": "",
      "cvRobotTask": true,
      "cvRobotTaskNum": null,
      "debutChapterId": "",
      "debutChapterName": "",
      "debutChapterSeqNum": null,
      "description": "",
      "envMode": 0,
      "envType": 0,
      "eqConfig": {
        "bp1": {
          "f": 200,
          "g": 0,
          "on": false,
          "q": 2
        },
        "bp2": {
          "f": 800,
          "g": 0,
          "on": false,
          "q": 2
        },
        "bp3": {
          "f": 3200,
          "g": 0,
          "on": false,
          "q": 2
        },
        "hs": {
          "f": 18000,
          "g": 0,
          "on": false,
          "s": 1
        },
        "ls": {
          "f": 40,
          "g": 0,
          "on": false,
          "s": 1
        }
      },
      "gainDb": 0,
      "gender": 1,
      "id": 3940786,
      "mixMode": null,
      "name": "卖奴大汉",
      "nicknames": "槐梧大汉",
      "numChapter": null,
      "numChar": null,
      "numCue": null,
      "presence": false,
      "robotDurationFactorSilence": 100,
      "robotGenMode": 0,
      "robotSpeedFactor": 100,
      "robotStyle": "",
      "robotStyleStandard": null,
      "selectedMixMode": 1,
      "type": 3
    },
    {
      "ableToMerge": true,
      "ageType": 2,
      "autoBind": false,
      "autoMatch": true,
      "autoMixMode": 0,
      "color": "#FDA58E",
      "createdTime": 1737880056767,
      "cvHumanId": null,
      "cvHumanName": "",
      "cvHumanTask": true,
      "cvHumanTaskNum": null,
      "cvRobotId": null,
      "cvRobotName": "",
      "cvRobotTask": true,
      "cvRobotTaskNum": null,
      "debutChapterId": "",
      "debutChapterName": "",
      "debutChapterSeqNum": null,
      "description": "李音随从之一",
      "envMode": 0,
      "envType": 0,
      "eqConfig": {
        "bp1": {
          "f": 200,
          "g": 0,
          "on": false,
          "q": 2
        },
        "bp2": {
          "f": 800,
          "g": 0,
          "on": false,
          "q": 2
        },
        "bp3": {
          "f": 3200,
          "g": 0,
          "on": false,
          "q": 2
        },
        "hs": {
          "f": 18000,
          "g": 0,
          "on": false,
          "s": 1
        },
        "ls": {
          "f": 40,
          "g": 0,
          "on": false,
          "s": 1
        }
      },
      "gainDb": 0,
      "gender": 1,
      "id": 3940789,
      "mixMode": null,
      "name": "李木",
      "nicknames": "",
      "numChapter": null,
      "numChar": null,
      "numCue": null,
      "presence": false,
      "robotDurationFactorSilence": 100,
      "robotGenMode": 0,
      "robotSpeedFactor": 100,
      "robotStyle": "",
      "robotStyleStandard": null,
      "selectedMixMode": 1,
      "type": 3
    },
    {
      "ableToMerge": true,
      "ageType": 2,
      "autoBind": false,
      "autoMatch": true,
      "autoMixMode": 0,
      "color": "#AC88FF",
      "createdTime": 1739245749674,
      "cvHumanId": null,
      "cvHumanName": "",
      "cvHumanTask": true,
      "cvHumanTaskNum": null,
      "cvRobotId": null,
      "cvRobotName": "",
      "cvRobotTask": true,
      "cvRobotTaskNum": null,
      "debutChapterId": "",
      "debutChapterName": "",
      "debutChapterSeqNum": null,
      "description": "",
      "envMode": 0,
      "envType": 0,
      "eqConfig": {
        "bp1": {
          "f": 200,
          "g": 0,
          "on": false,
          "q": 2
        },
        "bp2": {
          "f": 800,
          "g": 0,
          "on": false,
          "q": 2
        },
        "bp3": {
          "f": 3200,
          "g": 0,
          "on": false,
          "q": 2
        },
        "hs": {
          "f": 18000,
          "g": 0,
          "on": false,
          "s": 1
        },
        "ls": {
          "f": 40,
          "g": 0,
          "on": false,
          "s": 1
        }
      },
      "gainDb": 0,
      "gender": 1,
      "id": 4037327,
      "mixMode": null,
      "name": "007路人1",
      "nicknames": "",
      "numChapter": null,
      "numChar": null,
      "numCue": null,
      "presence": false,
      "robotDurationFactorSilence": 100,
      "robotGenMode": 0,
      "robotSpeedFactor": 100,
      "robotStyle": "",
      "robotStyleStandard": null,
      "selectedMixMode": 1,
      "type": 3
    },
    {
      "ableToMerge": true,
      "ageType": 2,
      "autoBind": false,
      "autoMatch": true,
      "autoMixMode": 0,
      "color": "#FFF1EE",
      "createdTime": 1739245763075,
      "cvHumanId": null,
      "cvHumanName": "",
      "cvHumanTask": true,
      "cvHumanTaskNum": null,
      "cvRobotId": null,
      "cvRobotName": "",
      "cvRobotTask": true,
      "cvRobotTaskNum": null,
      "debutChapterId": "",
      "debutChapterName": "",
      "debutChapterSeqNum": null,
      "description": "",
      "envMode": 0,
      "envType": 0,
      "eqConfig": {
        "bp1": {
          "f": 200,
          "g": 0,
          "on": false,
          "q": 2
        },
        "bp2": {
          "f": 800,
          "g": 0,
          "on": false,
          "q": 2
        },
        "bp3": {
          "f": 3200,
          "g": 0,
          "on": false,
          "q": 2
        },
        "hs": {
          "f": 18000,
          "g": 0,
          "on": false,
          "s": 1
        },
        "ls": {
          "f": 40,
          "g": 0,
          "on": false,
          "s": 1
        }
      },
      "gainDb": 0,
      "gender": 1,
      "id": 4037328,
      "mixMode": null,
      "name": "007路人2",
      "nicknames": "",
      "numChapter": null,
      "numChar": null,
      "numCue": null,
      "presence": false,
      "robotDurationFactorSilence": 100,
      "robotGenMode": 0,
      "robotSpeedFactor": 100,
      "robotStyle": "",
      "robotStyleStandard": null,
      "selectedMixMode": 1,
      "type": 3
    },
    {
      "ableToMerge": true,
      "ageType": 2,
      "autoBind": false,
      "autoMatch": true,
      "autoMixMode": 0,
      "color": "#8FA8FE",
      "createdTime": 1739245858771,
      "cvHumanId": null,
      "cvHumanName": "",
      "cvHumanTask": true,
      "cvHumanTaskNum": null,
      "cvRobotId": null,
      "cvRobotName": "",
      "cvRobotTask": true,
      "cvRobotTaskNum": null,
      "debutChapterId": "",
      "debutChapterName": "",
      "debutChapterSeqNum": null,
      "description": "",
      "envMode": 0,
      "envType": 0,
      "eqConfig": {
        "bp1": {
          "f": 200,
          "g": 0,
          "on": false,
          "q": 2
        },
        "bp2": {
          "f": 800,
          "g": 0,
          "on": false,
          "q": 2
        },
        "bp3": {
          "f": 3200,
          "g": 0,
          "on": false,
          "q": 2
        },
        "hs": {
          "f": 18000,
          "g": 0,
          "on": false,
          "s": 1
        },
        "ls": {
          "f": 40,
          "g": 0,
          "on": false,
          "s": 1
        }
      },
      "gainDb": 0,
      "gender": 1,
      "id": 4037329,
      "mixMode": null,
      "name": "007路人3",
      "nicknames": "",
      "numChapter": null,
      "numChar": null,
      "numCue": null,
      "presence": false,
      "robotDurationFactorSilence": 100,
      "robotGenMode": 0,
      "robotSpeedFactor": 100,
      "robotStyle": "",
      "robotStyleStandard": null,
      "selectedMixMode": 1,
      "type": 3
    },
    {
      "ableToMerge": true,
      "ageType": 2,
      "autoBind": false,
      "autoMatch": true,
      "autoMixMode": 0,
      "color": "#8FA8FE",
      "createdTime": 1737880040235,
      "cvHumanId": null,
      "cvHumanName": "",
      "cvHumanTask": true,
      "cvHumanTaskNum": null,
      "cvRobotId": null,
      "cvRobotName": "",
      "cvRobotTask": true,
      "cvRobotTaskNum": null,
      "debutChapterId": "",
      "debutChapterName": "",
      "debutChapterSeqNum": null,
      "description": "",
      "envMode": 0,
      "envType": 0,
      "eqConfig": null,
      "gainDb": 0,
      "gender": 2,
      "id": 3940781,
      "mixMode": null,
      "name": "刘烟",
      "nicknames": "",
      "numChapter": null,
      "numChar": null,
      "numCue": null,
      "presence": false,
      "robotDurationFactorSilence": 100,
      "robotGenMode": 0,
      "robotSpeedFactor": 100,
      "robotStyle": "",
      "robotStyleStandard": null,
      "selectedMixMode": 1,
      "type": null
    },
    {
      "ableToMerge": true,
      "ageType": 2,
      "autoBind": false,
      "autoMatch": true,
      "autoMixMode": 0,
      "color": "#ff87a7",
      "createdTime": 1737880053998,
      "cvHumanId": null,
      "cvHumanName": "",
      "cvHumanTask": true,
      "cvHumanTaskNum": null,
      "cvRobotId": null,
      "cvRobotName": "",
      "cvRobotTask": true,
      "cvRobotTaskNum": null,
      "debutChapterId": "",
      "debutChapterName": "",
      "debutChapterSeqNum": null,
      "description": "",
      "envMode": 0,
      "envType": 0,
      "eqConfig": null,
      "gainDb": 0,
      "gender": 1,
      "id": 3940787,
      "mixMode": null,
      "name": "男性青年",
      "nicknames": "",
      "numChapter": null,
      "numChar": null,
      "numCue": null,
      "presence": false,
      "robotDurationFactorSilence": 100,
      "robotGenMode": 0,
      "robotSpeedFactor": 100,
      "robotStyle": "",
      "robotStyleStandard": null,
      "selectedMixMode": 1,
      "type": null
    },
    {
      "ableToMerge": true,
      "ageType": 2,
      "autoBind": false,
      "autoMatch": true,
      "autoMixMode": 0,
      "color": "#B4EDAD",
      "createdTime": 1737880054003,
      "cvHumanId": null,
      "cvHumanName": "",
      "cvHumanTask": true,
      "cvHumanTaskNum": null,
      "cvRobotId": null,
      "cvRobotName": "",
      "cvRobotTask": true,
      "cvRobotTaskNum": null,
      "debutChapterId": "",
      "debutChapterName": "",
      "debutChapterSeqNum": null,
      "description": "",
      "envMode": 0,
      "envType": 0,
      "eqConfig": null,
      "gainDb": 0,
      "gender": 1,
      "id": 3940788,
      "mixMode": null,
      "name": "汉子",
      "nicknames": "",
      "numChapter": null,
      "numChar": null,
      "numCue": null,
      "presence": false,
      "robotDurationFactorSilence": 100,
      "robotGenMode": 0,
      "robotSpeedFactor": 100,
      "robotStyle": "",
      "robotStyleStandard": null,
      "selectedMixMode": 1,
      "type": null
    },
    {
      "ableToMerge": true,
      "ageType": 2,
      "autoBind": false,
      "autoMatch": true,
      "autoMixMode": 0,
      "color": "#D5FE84",
      "createdTime": 1737880060084,
      "cvHumanId": null,
      "cvHumanName": "",
      "cvHumanTask": true,
      "cvHumanTaskNum": null,
      "cvRobotId": null,
      "cvRobotName": "",
      "cvRobotTask": true,
      "cvRobotTaskNum": null,
      "debutChapterId": "",
      "debutChapterName": "",
      "debutChapterSeqNum": null,
      "description": "",
      "envMode": 0,
      "envType": 0,
      "eqConfig": null,
      "gainDb": 0,
      "gender": 2,
      "id": 3940791,
      "mixMode": null,
      "name": "杨依",
      "nicknames": "",
      "numChapter": null,
      "numChar": null,
      "numCue": null,
      "presence": false,
      "robotDurationFactorSilence": 100,
      "robotGenMode": 0,
      "robotSpeedFactor": 100,
      "robotStyle": "",
      "robotStyleStandard": null,
      "selectedMixMode": 1,
      "type": null
    },
    {
      "ableToMerge": true,
      "ageType": null,
      "autoBind": false,
      "autoMatch": true,
      "autoMixMode": 0,
      "color": "#D9B3F9",
      "createdTime": 1737880070311,
      "cvHumanId": null,
      "cvHumanName": "",
      "cvHumanTask": true,
      "cvHumanTaskNum": null,
      "cvRobotId": null,
      "cvRobotName": "",
      "cvRobotTask": true,
      "cvRobotTaskNum": null,
      "debutChapterId": "",
      "debutChapterName": "",
      "debutChapterSeqNum": null,
      "description": "",
      "envMode": 0,
      "envType": 0,
      "eqConfig": null,
      "gainDb": 0,
      "gender": 1,
      "id": 3940794,
      "mixMode": null,
      "name": "秋寒枫",
      "nicknames": "",
      "numChapter": null,
      "numChar": null,
      "numCue": null,
      "presence": false,
      "robotDurationFactorSilence": 100,
      "robotGenMode": 0,
      "robotSpeedFactor": 100,
      "robotStyle": "",
      "robotStyleStandard": null,
      "selectedMixMode": 1,
      "type": null
    },
    {
      "ableToMerge": true,
      "ageType": 2,
      "autoBind": false,
      "autoMatch": true,
      "autoMixMode": 0,
      "color": "#FFDDA7",
      "createdTime": 1737880070315,
      "cvHumanId": null,
      "cvHumanName": "",
      "cvHumanTask": true,
      "cvHumanTaskNum": null,
      "cvRobotId": null,
      "cvRobotName": "",
      "cvRobotTask": true,
      "cvRobotTaskNum": null,
      "debutChapterId": "",
      "debutChapterName": "",
      "debutChapterSeqNum": null,
      "description": "",
      "envMode": 0,
      "envType": 0,
      "eqConfig": null,
      "gainDb": 0,
      "gender": 1,
      "id": 3940795,
      "mixMode": null,
      "name": "手下",
      "nicknames": "",
      "numChapter": null,
      "numChar": null,
      "numCue": null,
      "presence": false,
      "robotDurationFactorSilence": 100,
      "robotGenMode": 0,
      "robotSpeedFactor": 100,
      "robotStyle": "",
      "robotStyleStandard": null,
      "selectedMixMode": 1,
      "type": null
    },
    {
      "ableToMerge": true,
      "ageType": 4,
      "autoBind": false,
      "autoMatch": true,
      "autoMixMode": 0,
      "color": "#F3F4A3",
      "createdTime": 1737880076809,
      "cvHumanId": null,
      "cvHumanName": "",
      "cvHumanTask": true,
      "cvHumanTaskNum": null,
      "cvRobotId": null,
      "cvRobotName": "",
      "cvRobotTask": true,
      "cvRobotTaskNum": null,
      "debutChapterId": "",
      "debutChapterName": "",
      "debutChapterSeqNum": null,
      "description": "",
      "envMode": 0,
      "envType": 0,
      "eqConfig": null,
      "gainDb": 0,
      "gender": 1,
      "id": 3940797,
      "mixMode": null,
      "name": "男性老年",
      "nicknames": "",
      "numChapter": null,
      "numChar": null,
      "numCue": null,
      "presence": false,
      "robotDurationFactorSilence": 100,
      "robotGenMode": 0,
      "robotSpeedFactor": 100,
      "robotStyle": "",
      "robotStyleStandard": null,
      "selectedMixMode": 1,
      "type": null
    },
    {
      "ableToMerge": true,
      "ageType": 4,
      "autoBind": false,
      "autoMatch": true,
      "autoMixMode": 0,
      "color": "#88D7EB",
      "createdTime": 1737880076813,
      "cvHumanId": null,
      "cvHumanName": "",
      "cvHumanTask": true,
      "cvHumanTaskNum": null,
      "cvRobotId": null,
      "cvRobotName": "",
      "cvRobotTask": true,
      "cvRobotTaskNum": null,
      "debutChapterId": "",
      "debutChapterName": "",
      "debutChapterSeqNum": null,
      "description": "",
      "envMode": 0,
      "envType": 0,
      "eqConfig": null,
      "gainDb": 0,
      "gender": 1,
      "id": 3940798,
      "mixMode": null,
      "name": "王大爷",
      "nicknames": "",
      "numChapter": null,
      "numChar": null,
      "numCue": null,
      "presence": false,
      "robotDurationFactorSilence": 100,
      "robotGenMode": 0,
      "robotSpeedFactor": 100,
      "robotStyle": "",
      "robotStyleStandard": null,
      "selectedMixMode": 1,
      "type": null
    },
    {
      "ableToMerge": true,
      "ageType": 4,
      "autoBind": false,
      "autoMatch": true,
      "autoMixMode": 0,
      "color": "#F1E0FF",
      "createdTime": 1737880076818,
      "cvHumanId": null,
      "cvHumanName": "",
      "cvHumanTask": true,
      "cvHumanTaskNum": null,
      "cvRobotId": null,
      "cvRobotName": "",
      "cvRobotTask": true,
      "cvRobotTaskNum": null,
      "debutChapterId": "",
      "debutChapterName": "",
      "debutChapterSeqNum": null,
      "description": "",
      "envMode": 0,
      "envType": 0,
      "eqConfig": null,
      "gainDb": 0,
      "gender": 1,
      "id": 3940799,
      "mixMode": null,
      "name": "老大爷",
      "nicknames": "",
      "numChapter": null,
      "numChar": null,
      "numCue": null,
      "presence": false,
      "robotDurationFactorSilence": 100,
      "robotGenMode": 0,
      "robotSpeedFactor": 100,
      "robotStyle": "",
      "robotStyleStandard": null,
      "selectedMixMode": 1,
      "type": null
    },
    {
      "ableToMerge": true,
      "ageType": 3,
      "autoBind": false,
      "autoMatch": true,
      "autoMixMode": 0,
      "color": "#E3F3C3",
      "createdTime": 1737880079108,
      "cvHumanId": null,
      "cvHumanName": "",
      "cvHumanTask": true,
      "cvHumanTaskNum": null,
      "cvRobotId": null,
      "cvRobotName": "",
      "cvRobotTask": true,
      "cvRobotTaskNum": null,
      "debutChapterId": "",
      "debutChapterName": "",
      "debutChapterSeqNum": null,
      "description": "",
      "envMode": 0,
      "envType": 0,
      "eqConfig": null,
      "gainDb": 0,
      "gender": 2,
      "id": 3940800,
      "mixMode": null,
      "name": "顾大嫂",
      "nicknames": "",
      "numChapter": null,
      "numChar": null,
      "numCue": null,
      "presence": false,
      "robotDurationFactorSilence": 100,
      "robotGenMode": 0,
      "robotSpeedFactor": 100,
      "robotStyle": "",
      "robotStyleStandard": null,
      "selectedMixMode": 1,
      "type": null
    },
    {
      "ableToMerge": true,
      "ageType": 3,
      "autoBind": false,
      "autoMatch": true,
      "autoMixMode": 0,
      "color": "#F19AC9",
      "createdTime": 1737880083636,
      "cvHumanId": null,
      "cvHumanName": "",
      "cvHumanTask": true,
      "cvHumanTaskNum": null,
      "cvRobotId": null,
      "cvRobotName": "",
      "cvRobotTask": true,
      "cvRobotTaskNum": null,
      "debutChapterId": "",
      "debutChapterName": "",
      "debutChapterSeqNum": null,
      "description": "",
      "envMode": 0,
      "envType": 0,
      "eqConfig": null,
      "gainDb": 0,
      "gender": 2,
      "id": 3940801,
      "mixMode": null,
      "name": "女性中年",
      "nicknames": "",
      "numChapter": null,
      "numChar": null,
      "numCue": null,
      "presence": false,
      "robotDurationFactorSilence": 100,
      "robotGenMode": 0,
      "robotSpeedFactor": 100,
      "robotStyle": "",
      "robotStyleStandard": null,
      "selectedMixMode": 1,
      "type": null
    },
    {
      "ableToMerge": true,
      "ageType": 2,
      "autoBind": false,
      "autoMatch": true,
      "autoMixMode": 0,
      "color": "#FCB038",
      "createdTime": 1737880090976,
      "cvHumanId": null,
      "cvHumanName": "",
      "cvHumanTask": true,
      "cvHumanTaskNum": null,
      "cvRobotId": null,
      "cvRobotName": "",
      "cvRobotTask": true,
      "cvRobotTaskNum": null,
      "debutChapterId": "",
      "debutChapterName": "",
      "debutChapterSeqNum": null,
      "description": "",
      "envMode": 0,
      "envType": 0,
      "eqConfig": null,
      "gainDb": 0,
      "gender": 1,
      "id": 3940802,
      "mixMode": null,
      "name": "李谈",
      "nicknames": "",
      "numChapter": null,
      "numChar": null,
      "numCue": null,
      "presence": false,
      "robotDurationFactorSilence": 100,
      "robotGenMode": 0,
      "robotSpeedFactor": 100,
      "robotStyle": "",
      "robotStyleStandard": null,
      "selectedMixMode": 1,
      "type": null
    },
    {
      "ableToMerge": true,
      "ageType": 2,
      "autoBind": false,
      "autoMatch": true,
      "autoMixMode": 0,
      "color": "#C9C6FF",
      "createdTime": 1737880090986,
      "cvHumanId": null,
      "cvHumanName": "",
      "cvHumanTask": true,
      "cvHumanTaskNum": null,
      "cvRobotId": null,
      "cvRobotName": "",
      "cvRobotTask": true,
      "cvRobotTaskNum": null,
      "debutChapterId": "",
      "debutChapterName": "",
      "debutChapterSeqNum": null,
      "description": "",
      "envMode": 0,
      "envType": 0,
      "eqConfig": null,
      "gainDb": 0,
      "gender": 2,
      "id": 3940803,
      "mixMode": null,
      "name": "女性青年",
      "nicknames": "",
      "numChapter": null,
      "numChar": null,
      "numCue": null,
      "presence": false,
      "robotDurationFactorSilence": 100,
      "robotGenMode": 0,
      "robotSpeedFactor": 100,
      "robotStyle": "",
      "robotStyleStandard": null,
      "selectedMixMode": 1,
      "type": null
    },
    {
      "ableToMerge": true,
      "ageType": 3,
      "autoBind": false,
      "autoMatch": true,
      "autoMixMode": 0,
      "color": "#8FFED8",
      "createdTime": 1737880094855,
      "cvHumanId": null,
      "cvHumanName": "",
      "cvHumanTask": true,
      "cvHumanTaskNum": null,
      "cvRobotId": null,
      "cvRobotName": "",
      "cvRobotTask": true,
      "cvRobotTaskNum": null,
      "debutChapterId": "",
      "debutChapterName": "",
      "debutChapterSeqNum": null,
      "description": "",
      "envMode": 0,
      "envType": 0,
      "eqConfig": null,
      "gainDb": 0,
      "gender": 1,
      "id": 3940804,
      "mixMode": null,
      "name": "男性中年",
      "nicknames": "",
      "numChapter": null,
      "numChar": null,
      "numCue": null,
      "presence": false,
      "robotDurationFactorSilence": 100,
      "robotGenMode": 0,
      "robotSpeedFactor": 100,
      "robotStyle": "",
      "robotStyleStandard": null,
      "selectedMixMode": 1,
      "type": null
    },
    {
      "ableToMerge": true,
      "ageType": 3,
      "autoBind": false,
      "autoMatch": true,
      "autoMixMode": 0,
      "color": "#AC88FF",
      "createdTime": 1737880094860,
      "cvHumanId": null,
      "cvHumanName": "",
      "cvHumanTask": true,
      "cvHumanTaskNum": null,
      "cvRobotId": null,
      "cvRobotName": "",
      "cvRobotTask": true,
      "cvRobotTaskNum": null,
      "debutChapterId": "",
      "debutChapterName": "",
      "debutChapterSeqNum": null,
      "description": "",
      "envMode": 0,
      "envType": 0,
      "eqConfig": null,
      "gainDb": 0,
      "gender": 1,
      "id": 3940805,
      "mixMode": null,
      "name": "胡老板",
      "nicknames": "",
      "numChapter": null,
      "numChar": null,
      "numCue": null,
      "presence": false,
      "robotDurationFactorSilence": 100,
      "robotGenMode": 0,
      "robotSpeedFactor": 100,
      "robotStyle": "",
      "robotStyleStandard": null,
      "selectedMixMode": 1,
      "type": null
    },
    {
      "ableToMerge": true,
      "ageType": 2,
      "autoBind": false,
      "autoMatch": true,
      "autoMixMode": 0,
      "color": "#FFF1EE",
      "createdTime": 1737880094865,
      "cvHumanId": null,
      "cvHumanName": "",
      "cvHumanTask": true,
      "cvHumanTaskNum": null,
      "cvRobotId": null,
      "cvRobotName": "",
      "cvRobotTask": true,
      "cvRobotTaskNum": null,
      "debutChapterId": "",
      "debutChapterName": "",
      "debutChapterSeqNum": null,
      "description": "",
      "envMode": 0,
      "envType": 0,
      "eqConfig": null,
      "gainDb": 0,
      "gender": 1,
      "id": 3940806,
      "mixMode": null,
      "name": "赵白",
      "nicknames": "",
      "numChapter": null,
      "numChar": null,
      "numCue": null,
      "presence": false,
      "robotDurationFactorSilence": 100,
      "robotGenMode": 0,
      "robotSpeedFactor": 100,
      "robotStyle": "",
      "robotStyleStandard": null,
      "selectedMixMode": 1,
      "type": null
    },
    {
      "ableToMerge": true,
      "ageType": 2,
      "autoBind": false,
      "autoMatch": true,
      "autoMixMode": 0,
      "color": "#8FA8FE",
      "createdTime": 1737880094870,
      "cvHumanId": null,
      "cvHumanName": "",
      "cvHumanTask": true,
      "cvHumanTaskNum": null,
      "cvRobotId": null,
      "cvRobotName": "",
      "cvRobotTask": true,
      "cvRobotTaskNum": null,
      "debutChapterId": "",
      "debutChapterName": "",
      "debutChapterSeqNum": null,
      "description": "",
      "envMode": 0,
      "envType": 0,
      "eqConfig": null,
      "gainDb": 0,
      "gender": 1,
      "id": 3940807,
      "mixMode": null,
      "name": "食客",
      "nicknames": "",
      "numChapter": null,
      "numChar": null,
      "numCue": null,
      "presence": false,
      "robotDurationFactorSilence": 100,
      "robotGenMode": 0,
      "robotSpeedFactor": 100,
      "robotStyle": "",
      "robotStyleStandard": null,
      "selectedMixMode": 1,
      "type": null
    },
    {
      "ableToMerge": true,
      "ageType": 2,
      "autoBind": false,
      "autoMatch": true,
      "autoMixMode": 0,
      "color": "#B7DDE4",
      "createdTime": 1737880099395,
      "cvHumanId": null,
      "cvHumanName": "",
      "cvHumanTask": true,
      "cvHumanTaskNum": null,
      "cvRobotId": null,
      "cvRobotName": "",
      "cvRobotTask": true,
      "cvRobotTaskNum": null,
      "debutChapterId": "",
      "debutChapterName": "",
      "debutChapterSeqNum": null,
      "description": "",
      "envMode": 0,
      "envType": 0,
      "eqConfig": null,
      "gainDb": 0,
      "gender": 1,
      "id": 3940808,
      "mixMode": null,
      "name": "军官",
      "nicknames": "",
      "numChapter": null,
      "numChar": null,
      "numCue": null,
      "presence": false,
      "robotDurationFactorSilence": 100,
      "robotGenMode": 0,
      "robotSpeedFactor": 100,
      "robotStyle": "",
      "robotStyleStandard": null,
      "selectedMixMode": 1,
      "type": null
    },
    {
      "ableToMerge": true,
      "ageType": null,
      "autoBind": false,
      "autoMatch": true,
      "autoMixMode": 0,
      "color": "#F3B6B6",
      "createdTime": 1737880105471,
      "cvHumanId": null,
      "cvHumanName": "",
      "cvHumanTask": true,
      "cvHumanTaskNum": null,
      "cvRobotId": null,
      "cvRobotName": "",
      "cvRobotTask": true,
      "cvRobotTaskNum": null,
      "debutChapterId": "",
      "debutChapterName": "",
      "debutChapterSeqNum": null,
      "description": "",
      "envMode": 0,
      "envType": 0,
      "eqConfig": null,
      "gainDb": 0,
      "gender": 1,
      "id": 3940809,
      "mixMode": null,
      "name": "王献臣",
      "nicknames": "",
      "numChapter": null,
      "numChar": null,
      "numCue": null,
      "presence": false,
      "robotDurationFactorSilence": 100,
      "robotGenMode": 0,
      "robotSpeedFactor": 100,
      "robotStyle": "",
      "robotStyleStandard": null,
      "selectedMixMode": 1,
      "type": null
    },
    {
      "ableToMerge": true,
      "ageType": null,
      "autoBind": false,
      "autoMatch": true,
      "autoMixMode": 0,
      "color": "#79EF6B",
      "createdTime": 1737880110336,
      "cvHumanId": null,
      "cvHumanName": "",
      "cvHumanTask": true,
      "cvHumanTaskNum": null,
      "cvRobotId": null,
      "cvRobotName": "",
      "cvRobotTask": true,
      "cvRobotTaskNum": null,
      "debutChapterId": "",
      "debutChapterName": "",
      "debutChapterSeqNum": null,
      "description": "",
      "envMode": 0,
      "envType": 0,
      "eqConfig": null,
      "gainDb": 0,
      "gender": 1,
      "id": 3940810,
      "mixMode": null,
      "name": "赵大爷",
      "nicknames": "",
      "numChapter": null,
      "numChar": null,
      "numCue": null,
      "presence": false,
      "robotDurationFactorSilence": 100,
      "robotGenMode": 0,
      "robotSpeedFactor": 100,
      "robotStyle": "",
      "robotStyleStandard": null,
      "selectedMixMode": 1,
      "type": null
    },
    {
      "ableToMerge": true,
      "ageType": null,
      "autoBind": false,
      "autoMatch": true,
      "autoMixMode": 0,
      "color": "#A9DCFF",
      "createdTime": 1737880110340,
      "cvHumanId": null,
      "cvHumanName": "",
      "cvHumanTask": true,
      "cvHumanTaskNum": null,
      "cvRobotId": null,
      "cvRobotName": "",
      "cvRobotTask": true,
      "cvRobotTaskNum": null,
      "debutChapterId": "",
      "debutChapterName": "",
      "debutChapterSeqNum": null,
      "description": "",
      "envMode": 0,
      "envType": 0,
      "eqConfig": null,
      "gainDb": 0,
      "gender": null,
      "id": 3940811,
      "mixMode": null,
      "name": "赵白发",
      "nicknames": "",
      "numChapter": null,
      "numChar": null,
      "numCue": null,
      "presence": false,
      "robotDurationFactorSilence": 100,
      "robotGenMode": 0,
      "robotSpeedFactor": 100,
      "robotStyle": "",
      "robotStyleStandard": null,
      "selectedMixMode": 1,
      "type": null
    },
    {
      "ableToMerge": true,
      "ageType": 2,
      "autoBind": false,
      "autoMatch": true,
      "autoMixMode": 0,
      "color": "#FFE617",
      "createdTime": 1737880110346,
      "cvHumanId": null,
      "cvHumanName": "",
      "cvHumanTask": true,
      "cvHumanTaskNum": null,
      "cvRobotId": null,
      "cvRobotName": "",
      "cvRobotTask": true,
      "cvRobotTaskNum": null,
      "debutChapterId": "",
      "debutChapterName": "",
      "debutChapterSeqNum": null,
      "description": "",
      "envMode": 0,
      "envType": 0,
      "eqConfig": null,
      "gainDb": 0,
      "gender": 1,
      "id": 3940812,
      "mixMode": null,
      "name": "寒舟",
      "nicknames": "",
      "numChapter": null,
      "numChar": null,
      "numCue": null,
      "presence": false,
      "robotDurationFactorSilence": 100,
      "robotGenMode": 0,
      "robotSpeedFactor": 100,
      "robotStyle": "",
      "robotStyleStandard": null,
      "selectedMixMode": 1,
      "type": null
    },
    {
      "ableToMerge": true,
      "ageType": null,
      "autoBind": false,
      "autoMatch": true,
      "autoMixMode": 0,
      "color": "#ff87a7",
      "createdTime": 1737880112842,
      "cvHumanId": null,
      "cvHumanName": "",
      "cvHumanTask": true,
      "cvHumanTaskNum": null,
      "cvRobotId": null,
      "cvRobotName": "",
      "cvRobotTask": true,
      "cvRobotTaskNum": null,
      "debutChapterId": "",
      "debutChapterName": "",
      "debutChapterSeqNum": null,
      "description": "",
      "envMode": 0,
      "envType": 0,
      "eqConfig": null,
      "gainDb": 0,
      "gender": 1,
      "id": 3940813,
      "mixMode": null,
      "name": "关吕",
      "nicknames": "",
      "numChapter": null,
      "numChar": null,
      "numCue": null,
      "presence": false,
      "robotDurationFactorSilence": 100,
      "robotGenMode": 0,
      "robotSpeedFactor": 100,
      "robotStyle": "",
      "robotStyleStandard": null,
      "selectedMixMode": 1,
      "type": null
    },
    {
      "ableToMerge": true,
      "ageType": 2,
      "autoBind": false,
      "autoMatch": true,
      "autoMixMode": 0,
      "color": "#B4EDAD",
      "createdTime": 1737880112848,
      "cvHumanId": null,
      "cvHumanName": "",
      "cvHumanTask": true,
      "cvHumanTaskNum": null,
      "cvRobotId": null,
      "cvRobotName": "",
      "cvRobotTask": true,
      "cvRobotTaskNum": null,
      "debutChapterId": "",
      "debutChapterName": "",
      "debutChapterSeqNum": null,
      "description": "",
      "envMode": 0,
      "envType": 0,
      "eqConfig": null,
      "gainDb": 0,
      "gender": 2,
      "id": 3940814,
      "mixMode": null,
      "name": "林素",
      "nicknames": "",
      "numChapter": null,
      "numChar": null,
      "numCue": null,
      "presence": false,
      "robotDurationFactorSilence": 100,
      "robotGenMode": 0,
      "robotSpeedFactor": 100,
      "robotStyle": "",
      "robotStyleStandard": null,
      "selectedMixMode": 1,
      "type": null
    },
    {
      "ableToMerge": true,
      "ageType": null,
      "autoBind": false,
      "autoMatch": true,
      "autoMixMode": 0,
      "color": "#FDA58E",
      "createdTime": 1737880112853,
      "cvHumanId": null,
      "cvHumanName": "",
      "cvHumanTask": true,
      "cvHumanTaskNum": null,
      "cvRobotId": null,
      "cvRobotName": "",
      "cvRobotTask": true,
      "cvRobotTaskNum": null,
      "debutChapterId": "",
      "debutChapterName": "",
      "debutChapterSeqNum": null,
      "description": "",
      "envMode": 0,
      "envType": 0,
      "eqConfig": null,
      "gainDb": 0,
      "gender": null,
      "id": 3940815,
      "mixMode": null,
      "name": "少女",
      "nicknames": "",
      "numChapter": null,
      "numChar": null,
      "numCue": null,
      "presence": false,
      "robotDurationFactorSilence": 100,
      "robotGenMode": 0,
      "robotSpeedFactor": 100,
      "robotStyle": "",
      "robotStyleStandard": null,
      "selectedMixMode": 1,
      "type": null
    },
    {
      "ableToMerge": true,
      "ageType": 2,
      "autoBind": false,
      "autoMatch": true,
      "autoMixMode": 0,
      "color": "#90c7ed",
      "createdTime": 1737880118927,
      "cvHumanId": null,
      "cvHumanName": "",
      "cvHumanTask": true,
      "cvHumanTaskNum": null,
      "cvRobotId": null,
      "cvRobotName": "",
      "cvRobotTask": true,
      "cvRobotTaskNum": null,
      "debutChapterId": "",
      "debutChapterName": "",
      "debutChapterSeqNum": null,
      "description": "",
      "envMode": 0,
      "envType": 0,
      "eqConfig": null,
      "gainDb": 0,
      "gender": null,
      "id": 3940816,
      "mixMode": null,
      "name": "孙眉",
      "nicknames": "",
      "numChapter": null,
      "numChar": null,
      "numCue": null,
      "presence": false,
      "robotDurationFactorSilence": 100,
      "robotGenMode": 0,
      "robotSpeedFactor": 100,
      "robotStyle": "",
      "robotStyleStandard": null,
      "selectedMixMode": 1,
      "type": null
    },
    {
      "ableToMerge": true,
      "ageType": 2,
      "autoBind": false,
      "autoMatch": true,
      "autoMixMode": 0,
      "color": "#D5FE84",
      "createdTime": 1737880122259,
      "cvHumanId": null,
      "cvHumanName": "",
      "cvHumanTask": true,
      "cvHumanTaskNum": null,
      "cvRobotId": null,
      "cvRobotName": "",
      "cvRobotTask": true,
      "cvRobotTaskNum": null,
      "debutChapterId": "",
      "debutChapterName": "",
      "debutChapterSeqNum": null,
      "description": "",
      "envMode": 0,
      "envType": 0,
      "eqConfig": null,
      "gainDb": 0,
      "gender": 2,
      "id": 3940817,
      "mixMode": null,
      "name": "侍女",
      "nicknames": "",
      "numChapter": null,
      "numChar": null,
      "numCue": null,
      "presence": false,
      "robotDurationFactorSilence": 100,
      "robotGenMode": 0,
      "robotSpeedFactor": 100,
      "robotStyle": "",
      "robotStyleStandard": null,
      "selectedMixMode": 1,
      "type": null
    },
    {
      "ableToMerge": true,
      "ageType": 4,
      "autoBind": false,
      "autoMatch": true,
      "autoMixMode": 0,
      "color": "#FF8686",
      "createdTime": 1737880126237,
      "cvHumanId": null,
      "cvHumanName": "",
      "cvHumanTask": true,
      "cvHumanTaskNum": null,
      "cvRobotId": null,
      "cvRobotName": "",
      "cvRobotTask": true,
      "cvRobotTaskNum": null,
      "debutChapterId": "",
      "debutChapterName": "",
      "debutChapterSeqNum": null,
      "description": "",
      "envMode": 0,
      "envType": 0,
      "eqConfig": null,
      "gainDb": 0,
      "gender": 1,
      "id": 3940818,
      "mixMode": null,
      "name": "白管家",
      "nicknames": "",
      "numChapter": null,
      "numChar": null,
      "numCue": null,
      "presence": false,
      "robotDurationFactorSilence": 100,
      "robotGenMode": 0,
      "robotSpeedFactor": 100,
      "robotStyle": "",
      "robotStyleStandard": null,
      "selectedMixMode": 1,
      "type": null
    },
    {
      "ableToMerge": true,
      "ageType": 2,
      "autoBind": false,
      "autoMatch": true,
      "autoMixMode": 0,
      "color": "#00B4FF",
      "createdTime": 1737880129866,
      "cvHumanId": null,
      "cvHumanName": "",
      "cvHumanTask": true,
      "cvHumanTaskNum": null,
      "cvRobotId": null,
      "cvRobotName": "",
      "cvRobotTask": true,
      "cvRobotTaskNum": null,
      "debutChapterId": "",
      "debutChapterName": "",
      "debutChapterSeqNum": null,
      "description": "",
      "envMode": 0,
      "envType": 0,
      "eqConfig": null,
      "gainDb": 0,
      "gender": 2,
      "id": 3940819,
      "mixMode": null,
      "name": "如青",
      "nicknames": "",
      "numChapter": null,
      "numChar": null,
      "numCue": null,
      "presence": false,
      "robotDurationFactorSilence": 100,
      "robotGenMode": 0,
      "robotSpeedFactor": 100,
      "robotStyle": "",
      "robotStyleStandard": null,
      "selectedMixMode": 1,
      "type": null
    },
    {
      "ableToMerge": true,
      "ageType": 4,
      "autoBind": false,
      "autoMatch": true,
      "autoMixMode": 0,
      "color": "#D9B3F9",
      "createdTime": 1737880133680,
      "cvHumanId": null,
      "cvHumanName": "",
      "cvHumanTask": true,
      "cvHumanTaskNum": null,
      "cvRobotId": null,
      "cvRobotName": "",
      "cvRobotTask": true,
      "cvRobotTaskNum": null,
      "debutChapterId": "",
      "debutChapterName": "",
      "debutChapterSeqNum": null,
      "description": "",
      "envMode": 0,
      "envType": 0,
      "eqConfig": null,
      "gainDb": 0,
      "gender": 1,
      "id": 3940820,
      "mixMode": null,
      "name": "老掌柜",
      "nicknames": "",
      "numChapter": null,
      "numChar": null,
      "numCue": null,
      "presence": false,
      "robotDurationFactorSilence": 100,
      "robotGenMode": 0,
      "robotSpeedFactor": 100,
      "robotStyle": "",
      "robotStyleStandard": null,
      "selectedMixMode": 1,
      "type": null
    },
    {
      "ableToMerge": true,
      "ageType": 3,
      "autoBind": false,
      "autoMatch": true,
      "autoMixMode": 0,
      "color": "#FFDDA7",
      "createdTime": 1737880133686,
      "cvHumanId": null,
      "cvHumanName": "",
      "cvHumanTask": true,
      "cvHumanTaskNum": null,
      "cvRobotId": null,
      "cvRobotName": "",
      "cvRobotTask": true,
      "cvRobotTaskNum": null,
      "debutChapterId": "",
      "debutChapterName": "",
      "debutChapterSeqNum": null,
      "description": "",
      "envMode": 0,
      "envType": 0,
      "eqConfig": null,
      "gainDb": 0,
      "gender": 1,
      "id": 3940821,
      "mixMode": null,
      "name": "长脸大汉",
      "nicknames": "",
      "numChapter": null,
      "numChar": null,
      "numCue": null,
      "presence": false,
      "robotDurationFactorSilence": 100,
      "robotGenMode": 0,
      "robotSpeedFactor": 100,
      "robotStyle": "",
      "robotStyleStandard": null,
      "selectedMixMode": 1,
      "type": null
    },
    {
      "ableToMerge": true,
      "ageType": null,
      "autoBind": false,
      "autoMatch": true,
      "autoMixMode": 0,
      "color": "#E0F6F2",
      "createdTime": 1737880133690,
      "cvHumanId": null,
      "cvHumanName": "",
      "cvHumanTask": true,
      "cvHumanTaskNum": null,
      "cvRobotId": null,
      "cvRobotName": "",
      "cvRobotTask": true,
      "cvRobotTaskNum": null,
      "debutChapterId": "",
      "debutChapterName": "",
      "debutChapterSeqNum": null,
      "description": "",
      "envMode": 0,
      "envType": 0,
      "eqConfig": null,
      "gainDb": 0,
      "gender": 1,
      "id": 3940822,
      "mixMode": null,
      "name": "长脸一个",
      "nicknames": "",
      "numChapter": null,
      "numChar": null,
      "numCue": null,
      "presence": false,
      "robotDurationFactorSilence": 100,
      "robotGenMode": 0,
      "robotSpeedFactor": 100,
      "robotStyle": "",
      "robotStyleStandard": null,
      "selectedMixMode": 1,
      "type": null
    },
    {
      "ableToMerge": true,
      "ageType": 3,
      "autoBind": false,
      "autoMatch": true,
      "autoMixMode": 0,
      "color": "#F3F4A3",
      "createdTime": 1737880133695,
      "cvHumanId": null,
      "cvHumanName": "",
      "cvHumanTask": true,
      "cvHumanTaskNum": null,
      "cvRobotId": null,
      "cvRobotName": "",
      "cvRobotTask": true,
      "cvRobotTaskNum": null,
      "debutChapterId": "",
      "debutChapterName": "",
      "debutChapterSeqNum": null,
      "description": "",
      "envMode": 0,
      "envType": 0,
      "eqConfig": null,
      "gainDb": 0,
      "gender": 1,
      "id": 3940823,
      "mixMode": null,
      "name": "金吴",
      "nicknames": "",
      "numChapter": null,
      "numChar": null,
      "numCue": null,
      "presence": false,
      "robotDurationFactorSilence": 100,
      "robotGenMode": 0,
      "robotSpeedFactor": 100,
      "robotStyle": "",
      "robotStyleStandard": null,
      "selectedMixMode": 1,
      "type": null
    },
    {
      "ableToMerge": true,
      "ageType": null,
      "autoBind": false,
      "autoMatch": true,
      "autoMixMode": 0,
      "color": "#88D7EB",
      "createdTime": 1737880133699,
      "cvHumanId": null,
      "cvHumanName": "",
      "cvHumanTask": true,
      "cvHumanTaskNum": null,
      "cvRobotId": null,
      "cvRobotName": "",
      "cvRobotTask": true,
      "cvRobotTaskNum": null,
      "debutChapterId": "",
      "debutChapterName": "",
      "debutChapterSeqNum": null,
      "description": "",
      "envMode": 0,
      "envType": 0,
      "eqConfig": null,
      "gainDb": 0,
      "gender": 1,
      "id": 3940824,
      "mixMode": null,
      "name": "长脸汉子",
      "nicknames": "",
      "numChapter": null,
      "numChar": null,
      "numCue": null,
      "presence": false,
      "robotDurationFactorSilence": 100,
      "robotGenMode": 0,
      "robotSpeedFactor": 100,
      "robotStyle": "",
      "robotStyleStandard": null,
      "selectedMixMode": 1,
      "type": null
    },
    {
      "ableToMerge": true,
      "ageType": null,
      "autoBind": false,
      "autoMatch": true,
      "autoMixMode": 0,
      "color": "#F1E0FF",
      "createdTime": 1737880136589,
      "cvHumanId": null,
      "cvHumanName": "",
      "cvHumanTask": true,
      "cvHumanTaskNum": null,
      "cvRobotId": null,
      "cvRobotName": "",
      "cvRobotTask": true,
      "cvRobotTaskNum": null,
      "debutChapterId": "",
      "debutChapterName": "",
      "debutChapterSeqNum": null,
      "description": "",
      "envMode": 0,
      "envType": 0,
      "eqConfig": null,
      "gainDb": 0,
      "gender": 2,
      "id": 3940829,
      "mixMode": null,
      "name": "丘边黑",
      "nicknames": "",
      "numChapter": null,
      "numChar": null,
      "numCue": null,
      "presence": false,
      "robotDurationFactorSilence": 100,
      "robotGenMode": 0,
      "robotSpeedFactor": 100,
      "robotStyle": "",
      "robotStyleStandard": null,
      "selectedMixMode": 1,
      "type": null
    },
    {
      "ableToMerge": true,
      "ageType": 2,
      "autoBind": false,
      "autoMatch": true,
      "autoMixMode": 0,
      "color": "#E3F3C3",
      "createdTime": 1737880142093,
      "cvHumanId": null,
      "cvHumanName": "",
      "cvHumanTask": true,
      "cvHumanTaskNum": null,
      "cvRobotId": null,
      "cvRobotName": "",
      "cvRobotTask": true,
      "cvRobotTaskNum": null,
      "debutChapterId": "",
      "debutChapterName": "",
      "debutChapterSeqNum": null,
      "description": "",
      "envMode": 0,
      "envType": 0,
      "eqConfig": null,
      "gainDb": 0,
      "gender": 2,
      "id": 3940830,
      "mixMode": null,
      "name": "云儿",
      "nicknames": "",
      "numChapter": null,
      "numChar": null,
      "numCue": null,
      "presence": false,
      "robotDurationFactorSilence": 100,
      "robotGenMode": 0,
      "robotSpeedFactor": 100,
      "robotStyle": "",
      "robotStyleStandard": null,
      "selectedMixMode": 1,
      "type": null
    },
    {
      "ableToMerge": true,
      "ageType": 2,
      "autoBind": false,
      "autoMatch": true,
      "autoMixMode": 0,
      "color": "#F19AC9",
      "createdTime": 1737880142097,
      "cvHumanId": null,
      "cvHumanName": "",
      "cvHumanTask": true,
      "cvHumanTaskNum": null,
      "cvRobotId": null,
      "cvRobotName": "",
      "cvRobotTask": true,
      "cvRobotTaskNum": null,
      "debutChapterId": "",
      "debutChapterName": "",
      "debutChapterSeqNum": null,
      "description": "",
      "envMode": 0,
      "envType": 0,
      "eqConfig": null,
      "gainDb": 0,
      "gender": 1,
      "id": 3940831,
      "mixMode": null,
      "name": "拙荆",
      "nicknames": "",
      "numChapter": null,
      "numChar": null,
      "numCue": null,
      "presence": false,
      "robotDurationFactorSilence": 100,
      "robotGenMode": 0,
      "robotSpeedFactor": 100,
      "robotStyle": "",
      "robotStyleStandard": null,
      "selectedMixMode": 1,
      "type": null
    },
    {
      "ableToMerge": true,
      "ageType": 4,
      "autoBind": false,
      "autoMatch": true,
      "autoMixMode": 0,
      "color": "#FCB038",
      "createdTime": 1737880142100,
      "cvHumanId": null,
      "cvHumanName": "",
      "cvHumanTask": true,
      "cvHumanTaskNum": null,
      "cvRobotId": null,
      "cvRobotName": "",
      "cvRobotTask": true,
      "cvRobotTaskNum": null,
      "debutChapterId": "",
      "debutChapterName": "",
      "debutChapterSeqNum": null,
      "description": "",
      "envMode": 0,
      "envType": 0,
      "eqConfig": null,
      "gainDb": 0,
      "gender": 1,
      "id": 3940832,
      "mixMode": null,
      "name": "刘老爷",
      "nicknames": "",
      "numChapter": null,
      "numChar": null,
      "numCue": null,
      "presence": false,
      "robotDurationFactorSilence": 100,
      "robotGenMode": 0,
      "robotSpeedFactor": 100,
      "robotStyle": "",
      "robotStyleStandard": null,
      "selectedMixMode": 1,
      "type": null
    },
    {
      "ableToMerge": true,
      "ageType": 2,
      "autoBind": false,
      "autoMatch": true,
      "autoMixMode": 0,
      "color": "#C9C6FF",
      "createdTime": 1737880142104,
      "cvHumanId": null,
      "cvHumanName": "",
      "cvHumanTask": true,
      "cvHumanTaskNum": null,
      "cvRobotId": null,
      "cvRobotName": "",
      "cvRobotTask": true,
      "cvRobotTaskNum": null,
      "debutChapterId": "",
      "debutChapterName": "",
      "debutChapterSeqNum": null,
      "description": "",
      "envMode": 0,
      "envType": 0,
      "eqConfig": null,
      "gainDb": 0,
      "gender": 1,
      "id": 3940833,
      "mixMode": null,
      "name": "门客",
      "nicknames": "",
      "numChapter": null,
      "numChar": null,
      "numCue": null,
      "presence": false,
      "robotDurationFactorSilence": 100,
      "robotGenMode": 0,
      "robotSpeedFactor": 100,
      "robotStyle": "",
      "robotStyleStandard": null,
      "selectedMixMode": 1,
      "type": null
    },
    {
      "ableToMerge": true,
      "ageType": 2,
      "autoBind": false,
      "autoMatch": true,
      "autoMixMode": 0,
      "color": "#8FFED8",
      "createdTime": 1737880145794,
      "cvHumanId": null,
      "cvHumanName": "",
      "cvHumanTask": true,
      "cvHumanTaskNum": null,
      "cvRobotId": null,
      "cvRobotName": "",
      "cvRobotTask": true,
      "cvRobotTaskNum": null,
      "debutChapterId": "",
      "debutChapterName": "",
      "debutChapterSeqNum": null,
      "description": "",
      "envMode": 0,
      "envType": 0,
      "eqConfig": null,
      "gainDb": 0,
      "gender": 1,
      "id": 3940834,
      "mixMode": null,
      "name": "杨冲",
      "nicknames": "",
      "numChapter": null,
      "numChar": null,
      "numCue": null,
      "presence": false,
      "robotDurationFactorSilence": 100,
      "robotGenMode": 0,
      "robotSpeedFactor": 100,
      "robotStyle": "",
      "robotStyleStandard": null,
      "selectedMixMode": 1,
      "type": null
    },
    {
      "ableToMerge": true,
      "ageType": 2,
      "autoBind": false,
      "autoMatch": true,
      "autoMixMode": 0,
      "color": "#AC88FF",
      "createdTime": 1737880145799,
      "cvHumanId": null,
      "cvHumanName": "",
      "cvHumanTask": true,
      "cvHumanTaskNum": null,
      "cvRobotId": null,
      "cvRobotName": "",
      "cvRobotTask": true,
      "cvRobotTaskNum": null,
      "debutChapterId": "",
      "debutChapterName": "",
      "debutChapterSeqNum": null,
      "description": "",
      "envMode": 0,
      "envType": 0,
      "eqConfig": null,
      "gainDb": 0,
      "gender": null,
      "id": 3940835,
      "mixMode": null,
      "name": "随从",
      "nicknames": "",
      "numChapter": null,
      "numChar": null,
      "numCue": null,
      "presence": false,
      "robotDurationFactorSilence": 100,
      "robotGenMode": 0,
      "robotSpeedFactor": 100,
      "robotStyle": "",
      "robotStyleStandard": null,
      "selectedMixMode": 1,
      "type": null
    },
    {
      "ableToMerge": true,
      "ageType": null,
      "autoBind": false,
      "autoMatch": true,
      "autoMixMode": 0,
      "color": "#FFF1EE",
      "createdTime": 1737880151406,
      "cvHumanId": null,
      "cvHumanName": "",
      "cvHumanTask": true,
      "cvHumanTaskNum": null,
      "cvRobotId": null,
      "cvRobotName": "",
      "cvRobotTask": true,
      "cvRobotTaskNum": null,
      "debutChapterId": "",
      "debutChapterName": "",
      "debutChapterSeqNum": null,
      "description": "",
      "envMode": 0,
      "envType": 0,
      "eqConfig": null,
      "gainDb": 0,
      "gender": null,
      "id": 3940836,
      "mixMode": null,
      "name": "众人",
      "nicknames": "",
      "numChapter": null,
      "numChar": null,
      "numCue": null,
      "presence": false,
      "robotDurationFactorSilence": 100,
      "robotGenMode": 0,
      "robotSpeedFactor": 100,
      "robotStyle": "",
      "robotStyleStandard": null,
      "selectedMixMode": 1,
      "type": null
    },
    {
      "ableToMerge": true,
      "ageType": null,
      "autoBind": false,
      "autoMatch": true,
      "autoMixMode": 0,
      "color": "#8FA8FE",
      "createdTime": 1737880160815,
      "cvHumanId": null,
      "cvHumanName": "",
      "cvHumanTask": true,
      "cvHumanTaskNum": null,
      "cvRobotId": null,
      "cvRobotName": "",
      "cvRobotTask": true,
      "cvRobotTaskNum": null,
      "debutChapterId": "",
      "debutChapterName": "",
      "debutChapterSeqNum": null,
      "description": "",
      "envMode": 0,
      "envType": 0,
      "eqConfig": null,
      "gainDb": 0,
      "gender": 1,
      "id": 3940837,
      "mixMode": null,
      "name": "寒衣秀士",
      "nicknames": "",
      "numChapter": null,
      "numChar": null,
      "numCue": null,
      "presence": false,
      "robotDurationFactorSilence": 100,
      "robotGenMode": 0,
      "robotSpeedFactor": 100,
      "robotStyle": "",
      "robotStyleStandard": null,
      "selectedMixMode": 1,
      "type": null
    },
    {
      "ableToMerge": true,
      "ageType": 2,
      "autoBind": false,
      "autoMatch": true,
      "autoMixMode": 0,
      "color": "#B7DDE4",
      "createdTime": 1737880494557,
      "cvHumanId": null,
      "cvHumanName": "",
      "cvHumanTask": true,
      "cvHumanTaskNum": null,
      "cvRobotId": null,
      "cvRobotName": "",
      "cvRobotTask": true,
      "cvRobotTaskNum": null,
      "debutChapterId": "",
      "debutChapterName": "",
      "debutChapterSeqNum": null,
      "description": "",
      "envMode": 0,
      "envType": 0,
      "eqConfig": null,
      "gainDb": 0,
      "gender": 2,
      "id": 3940846,
      "mixMode": null,
      "name": "兰儿",
      "nicknames": "",
      "numChapter": null,
      "numChar": null,
      "numCue": null,
      "presence": false,
      "robotDurationFactorSilence": 100,
      "robotGenMode": 0,
      "robotSpeedFactor": 100,
      "robotStyle": "",
      "robotStyleStandard": null,
      "selectedMixMode": 1,
      "type": null
    },
    {
      "ableToMerge": true,
      "ageType": null,
      "autoBind": false,
      "autoMatch": true,
      "autoMixMode": 0,
      "color": "#F3B6B6",
      "createdTime": 1737880496740,
      "cvHumanId": null,
      "cvHumanName": "",
      "cvHumanTask": true,
      "cvHumanTaskNum": null,
      "cvRobotId": null,
      "cvRobotName": "",
      "cvRobotTask": true,
      "cvRobotTaskNum": null,
      "debutChapterId": "",
      "debutChapterName": "",
      "debutChapterSeqNum": null,
      "description": "",
      "envMode": 0,
      "envType": 0,
      "eqConfig": null,
      "gainDb": 0,
      "gender": 2,
      "id": 3940847,
      "mixMode": null,
      "name": "老婆婆",
      "nicknames": "",
      "numChapter": null,
      "numChar": null,
      "numCue": null,
      "presence": false,
      "robotDurationFactorSilence": 100,
      "robotGenMode": 0,
      "robotSpeedFactor": 100,
      "robotStyle": "",
      "robotStyleStandard": null,
      "selectedMixMode": 1,
      "type": null
    },
    {
      "ableToMerge": true,
      "ageType": 2,
      "autoBind": false,
      "autoMatch": true,
      "autoMixMode": 0,
      "color": "#79EF6B",
      "createdTime": 1737880503822,
      "cvHumanId": null,
      "cvHumanName": "",
      "cvHumanTask": true,
      "cvHumanTaskNum": null,
      "cvRobotId": null,
      "cvRobotName": "",
      "cvRobotTask": true,
      "cvRobotTaskNum": null,
      "debutChapterId": "",
      "debutChapterName": "",
      "debutChapterSeqNum": null,
      "description": "",
      "envMode": 0,
      "envType": 0,
      "eqConfig": null,
      "gainDb": 0,
      "gender": 1,
      "id": 3940849,
      "mixMode": null,
      "name": "杨军",
      "nicknames": "",
      "numChapter": null,
      "numChar": null,
      "numCue": null,
      "presence": false,
      "robotDurationFactorSilence": 100,
      "robotGenMode": 0,
      "robotSpeedFactor": 100,
      "robotStyle": "",
      "robotStyleStandard": null,
      "selectedMixMode": 1,
      "type": null
    },
    {
      "ableToMerge": true,
      "ageType": 2,
      "autoBind": false,
      "autoMatch": true,
      "autoMixMode": 0,
      "color": "#A9DCFF",
      "createdTime": 1737880505994,
      "cvHumanId": null,
      "cvHumanName": "",
      "cvHumanTask": true,
      "cvHumanTaskNum": null,
      "cvRobotId": null,
      "cvRobotName": "",
      "cvRobotTask": true,
      "cvRobotTaskNum": null,
      "debutChapterId": "",
      "debutChapterName": "",
      "debutChapterSeqNum": null,
      "description": "",
      "envMode": 0,
      "envType": 0,
      "eqConfig": null,
      "gainDb": 0,
      "gender": 1,
      "id": 3940850,
      "mixMode": null,
      "name": "黑衣男子",
      "nicknames": "",
      "numChapter": null,
      "numChar": null,
      "numCue": null,
      "presence": false,
      "robotDurationFactorSilence": 100,
      "robotGenMode": 0,
      "robotSpeedFactor": 100,
      "robotStyle": "",
      "robotStyleStandard": null,
      "selectedMixMode": 1,
      "type": null
    },
    {
      "ableToMerge": true,
      "ageType": null,
      "autoBind": false,
      "autoMatch": true,
      "autoMixMode": 0,
      "color": "#FFE617",
      "createdTime": 1737880506002,
      "cvHumanId": null,
      "cvHumanName": "",
      "cvHumanTask": true,
      "cvHumanTaskNum": null,
      "cvRobotId": null,
      "cvRobotName": "",
      "cvRobotTask": true,
      "cvRobotTaskNum": null,
      "debutChapterId": "",
      "debutChapterName": "",
      "debutChapterSeqNum": null,
      "description": "",
      "envMode": 0,
      "envType": 0,
      "eqConfig": null,
      "gainDb": 0,
      "gender": 1,
      "id": 3940851,
      "mixMode": null,
      "name": "兰花无敌",
      "nicknames": "",
      "numChapter": null,
      "numChar": null,
      "numCue": null,
      "presence": false,
      "robotDurationFactorSilence": 100,
      "robotGenMode": 0,
      "robotSpeedFactor": 100,
      "robotStyle": "",
      "robotStyleStandard": null,
      "selectedMixMode": 1,
      "type": null
    },
    {
      "ableToMerge": true,
      "ageType": 2,
      "autoBind": false,
      "autoMatch": true,
      "autoMixMode": 0,
      "color": "#ff87a7",
      "createdTime": 1737880506006,
      "cvHumanId": null,
      "cvHumanName": "",
      "cvHumanTask": true,
      "cvHumanTaskNum": null,
      "cvRobotId": null,
      "cvRobotName": "",
      "cvRobotTask": true,
      "cvRobotTaskNum": null,
      "debutChapterId": "",
      "debutChapterName": "",
      "debutChapterSeqNum": null,
      "description": "",
      "envMode": 0,
      "envType": 0,
      "eqConfig": null,
      "gainDb": 0,
      "gender": 1,
      "id": 3940852,
      "mixMode": null,
      "name": "男子",
      "nicknames": "",
      "numChapter": null,
      "numChar": null,
      "numCue": null,
      "presence": false,
      "robotDurationFactorSilence": 100,
      "robotGenMode": 0,
      "robotSpeedFactor": 100,
      "robotStyle": "",
      "robotStyleStandard": null,
      "selectedMixMode": 1,
      "type": null
    },
    {
      "ableToMerge": true,
      "ageType": null,
      "autoBind": false,
      "autoMatch": true,
      "autoMixMode": 0,
      "color": "#B4EDAD",
      "createdTime": 1737880506009,
      "cvHumanId": null,
      "cvHumanName": "",
      "cvHumanTask": true,
      "cvHumanTaskNum": null,
      "cvRobotId": null,
      "cvRobotName": "",
      "cvRobotTask": true,
      "cvRobotTaskNum": null,
      "debutChapterId": "",
      "debutChapterName": "",
      "debutChapterSeqNum": null,
      "description": "",
      "envMode": 0,
      "envType": 0,
      "eqConfig": null,
      "gainDb": 0,
      "gender": 1,
      "id": 3940853,
      "mixMode": null,
      "name": "大人",
      "nicknames": "",
      "numChapter": null,
      "numChar": null,
      "numCue": null,
      "presence": false,
      "robotDurationFactorSilence": 100,
      "robotGenMode": 0,
      "robotSpeedFactor": 100,
      "robotStyle": "",
      "robotStyleStandard": null,
      "selectedMixMode": 1,
      "type": null
    },
    {
      "ableToMerge": true,
      "ageType": null,
      "autoBind": false,
      "autoMatch": true,
      "autoMixMode": 0,
      "color": "#FDA58E",
      "createdTime": 1737880508484,
      "cvHumanId": null,
      "cvHumanName": "",
      "cvHumanTask": true,
      "cvHumanTaskNum": null,
      "cvRobotId": null,
      "cvRobotName": "",
      "cvRobotTask": true,
      "cvRobotTaskNum": null,
      "debutChapterId": "",
      "debutChapterName": "",
      "debutChapterSeqNum": null,
      "description": "",
      "envMode": 0,
      "envType": 0,
      "eqConfig": null,
      "gainDb": 0,
      "gender": 1,
      "id": 3940854,
      "mixMode": null,
      "name": "张大人",
      "nicknames": "",
      "numChapter": null,
      "numChar": null,
      "numCue": null,
      "presence": false,
      "robotDurationFactorSilence": 100,
      "robotGenMode": 0,
      "robotSpeedFactor": 100,
      "robotStyle": "",
      "robotStyleStandard": null,
      "selectedMixMode": 1,
      "type": null
    },
    {
      "ableToMerge": true,
      "ageType": 2,
      "autoBind": false,
      "autoMatch": true,
      "autoMixMode": 0,
      "color": "#90c7ed",
      "createdTime": 1737880508487,
      "cvHumanId": null,
      "cvHumanName": "",
      "cvHumanTask": true,
      "cvHumanTaskNum": null,
      "cvRobotId": null,
      "cvRobotName": "",
      "cvRobotTask": true,
      "cvRobotTaskNum": null,
      "debutChapterId": "",
      "debutChapterName": "",
      "debutChapterSeqNum": null,
      "description": "",
      "envMode": 0,
      "envType": 0,
      "eqConfig": null,
      "gainDb": 0,
      "gender": 1,
      "id": 3940855,
      "mixMode": null,
      "name": "张路",
      "nicknames": "",
      "numChapter": null,
      "numChar": null,
      "numCue": null,
      "presence": false,
      "robotDurationFactorSilence": 100,
      "robotGenMode": 0,
      "robotSpeedFactor": 100,
      "robotStyle": "",
      "robotStyleStandard": null,
      "selectedMixMode": 1,
      "type": null
    },
    {
      "ableToMerge": true,
      "ageType": null,
      "autoBind": false,
      "autoMatch": true,
      "autoMixMode": 0,
      "color": "#D5FE84",
      "createdTime": 1737880510878,
      "cvHumanId": null,
      "cvHumanName": "",
      "cvHumanTask": true,
      "cvHumanTaskNum": null,
      "cvRobotId": null,
      "cvRobotName": "",
      "cvRobotTask": true,
      "cvRobotTaskNum": null,
      "debutChapterId": "",
      "debutChapterName": "",
      "debutChapterSeqNum": null,
      "description": "",
      "envMode": 0,
      "envType": 0,
      "eqConfig": null,
      "gainDb": 0,
      "gender": 1,
      "id": 3940856,
      "mixMode": null,
      "name": "义父",
      "nicknames": "",
      "numChapter": null,
      "numChar": null,
      "numCue": null,
      "presence": false,
      "robotDurationFactorSilence": 100,
      "robotGenMode": 0,
      "robotSpeedFactor": 100,
      "robotStyle": "",
      "robotStyleStandard": null,
      "selectedMixMode": 1,
      "type": null
    },
    {
      "ableToMerge": true,
      "ageType": 2,
      "autoBind": false,
      "autoMatch": true,
      "autoMixMode": 0,
      "color": "#FF8686",
      "createdTime": 1737880510886,
      "cvHumanId": null,
      "cvHumanName": "",
      "cvHumanTask": true,
      "cvHumanTaskNum": null,
      "cvRobotId": null,
      "cvRobotName": "",
      "cvRobotTask": true,
      "cvRobotTaskNum": null,
      "debutChapterId": "",
      "debutChapterName": "",
      "debutChapterSeqNum": null,
      "description": "",
      "envMode": 0,
      "envType": 0,
      "eqConfig": null,
      "gainDb": 0,
      "gender": 2,
      "id": 3940857,
      "mixMode": null,
      "name": "青儿",
      "nicknames": "",
      "numChapter": null,
      "numChar": null,
      "numCue": null,
      "presence": false,
      "robotDurationFactorSilence": 100,
      "robotGenMode": 0,
      "robotSpeedFactor": 100,
      "robotStyle": "",
      "robotStyleStandard": null,
      "selectedMixMode": 1,
      "type": null
    },
    {
      "ableToMerge": true,
      "ageType": 2,
      "autoBind": false,
      "autoMatch": true,
      "autoMixMode": 0,
      "color": "#00B4FF",
      "createdTime": 1737880514020,
      "cvHumanId": null,
      "cvHumanName": "",
      "cvHumanTask": true,
      "cvHumanTaskNum": null,
      "cvRobotId": null,
      "cvRobotName": "",
      "cvRobotTask": true,
      "cvRobotTaskNum": null,
      "debutChapterId": "",
      "debutChapterName": "",
      "debutChapterSeqNum": null,
      "description": "",
      "envMode": 0,
      "envType": 0,
      "eqConfig": null,
      "gainDb": 0,
      "gender": 2,
      "id": 3940859,
      "mixMode": null,
      "name": "怡姐",
      "nicknames": "",
      "numChapter": null,
      "numChar": null,
      "numCue": null,
      "presence": false,
      "robotDurationFactorSilence": 100,
      "robotGenMode": 0,
      "robotSpeedFactor": 100,
      "robotStyle": "",
      "robotStyleStandard": null,
      "selectedMixMode": 1,
      "type": null
    },
    {
      "ableToMerge": true,
      "ageType": null,
      "autoBind": false,
      "autoMatch": true,
      "autoMixMode": 0,
      "color": "#D9B3F9",
      "createdTime": 1737880514025,
      "cvHumanId": null,
      "cvHumanName": "",
      "cvHumanTask": true,
      "cvHumanTaskNum": null,
      "cvRobotId": null,
      "cvRobotName": "",
      "cvRobotTask": true,
      "cvRobotTaskNum": null,
      "debutChapterId": "",
      "debutChapterName": "",
      "debutChapterSeqNum": null,
      "description": "",
      "envMode": 0,
      "envType": 0,
      "eqConfig": null,
      "gainDb": 0,
      "gender": 1,
      "id": 3940860,
      "mixMode": null,
      "name": "另一军官",
      "nicknames": "",
      "numChapter": null,
      "numChar": null,
      "numCue": null,
      "presence": false,
      "robotDurationFactorSilence": 100,
      "robotGenMode": 0,
      "robotSpeedFactor": 100,
      "robotStyle": "",
      "robotStyleStandard": null,
      "selectedMixMode": 1,
      "type": null
    },
    {
      "ableToMerge": true,
      "ageType": null,
      "autoBind": false,
      "autoMatch": true,
      "autoMixMode": 0,
      "color": "#FFDDA7",
      "createdTime": 1737880514029,
      "cvHumanId": null,
      "cvHumanName": "",
      "cvHumanTask": true,
      "cvHumanTaskNum": null,
      "cvRobotId": null,
      "cvRobotName": "",
      "cvRobotTask": true,
      "cvRobotTaskNum": null,
      "debutChapterId": "",
      "debutChapterName": "",
      "debutChapterSeqNum": null,
      "description": "",
      "envMode": 0,
      "envType": 0,
      "eqConfig": null,
      "gainDb": 0,
      "gender": 2,
      "id": 3940861,
      "mixMode": null,
      "name": "分中",
      "nicknames": "",
      "numChapter": null,
      "numChar": null,
      "numCue": null,
      "presence": false,
      "robotDurationFactorSilence": 100,
      "robotGenMode": 0,
      "robotSpeedFactor": 100,
      "robotStyle": "",
      "robotStyleStandard": null,
      "selectedMixMode": 1,
      "type": null
    },
    {
      "ableToMerge": true,
      "ageType": null,
      "autoBind": false,
      "autoMatch": true,
      "autoMixMode": 0,
      "color": "#E0F6F2",
      "createdTime": 1737880515440,
      "cvHumanId": null,
      "cvHumanName": "",
      "cvHumanTask": true,
      "cvHumanTaskNum": null,
      "cvRobotId": null,
      "cvRobotName": "",
      "cvRobotTask": true,
      "cvRobotTaskNum": null,
      "debutChapterId": "",
      "debutChapterName": "",
      "debutChapterSeqNum": null,
      "description": "",
      "envMode": 0,
      "envType": 0,
      "eqConfig": null,
      "gainDb": 0,
      "gender": 1,
      "id": 3940862,
      "mixMode": null,
      "name": "刑吏",
      "nicknames": "",
      "numChapter": null,
      "numChar": null,
      "numCue": null,
      "presence": false,
      "robotDurationFactorSilence": 100,
      "robotGenMode": 0,
      "robotSpeedFactor": 100,
      "robotStyle": "",
      "robotStyleStandard": null,
      "selectedMixMode": 1,
      "type": null
    },
    {
      "ableToMerge": true,
      "ageType": null,
      "autoBind": false,
      "autoMatch": true,
      "autoMixMode": 0,
      "color": "#F3F4A3",
      "createdTime": 1737880515444,
      "cvHumanId": null,
      "cvHumanName": "",
      "cvHumanTask": true,
      "cvHumanTaskNum": null,
      "cvRobotId": null,
      "cvRobotName": "",
      "cvRobotTask": true,
      "cvRobotTaskNum": null,
      "debutChapterId": "",
      "debutChapterName": "",
      "debutChapterSeqNum": null,
      "description": "",
      "envMode": 0,
      "envType": 0,
      "eqConfig": null,
      "gainDb": 0,
      "gender": 1,
      "id": 3940863,
      "mixMode": null,
      "name": "金兄",
      "nicknames": "",
      "numChapter": null,
      "numChar": null,
      "numCue": null,
      "presence": false,
      "robotDurationFactorSilence": 100,
      "robotGenMode": 0,
      "robotSpeedFactor": 100,
      "robotStyle": "",
      "robotStyleStandard": null,
      "selectedMixMode": 1,
      "type": null
    },
    {
      "ableToMerge": true,
      "ageType": null,
      "autoBind": false,
      "autoMatch": true,
      "autoMixMode": 0,
      "color": "#88D7EB",
      "createdTime": 1737880519112,
      "cvHumanId": null,
      "cvHumanName": "",
      "cvHumanTask": true,
      "cvHumanTaskNum": null,
      "cvRobotId": null,
      "cvRobotName": "",
      "cvRobotTask": true,
      "cvRobotTaskNum": null,
      "debutChapterId": "",
      "debutChapterName": "",
      "debutChapterSeqNum": null,
      "description": "",
      "envMode": 0,
      "envType": 0,
      "eqConfig": null,
      "gainDb": 0,
      "gender": 1,
      "id": 3940864,
      "mixMode": null,
      "name": "张路正",
      "nicknames": "",
      "numChapter": null,
      "numChar": null,
      "numCue": null,
      "presence": false,
      "robotDurationFactorSilence": 100,
      "robotGenMode": 0,
      "robotSpeedFactor": 100,
      "robotStyle": "",
      "robotStyleStandard": null,
      "selectedMixMode": 1,
      "type": null
    },
    {
      "ableToMerge": true,
      "ageType": null,
      "autoBind": false,
      "autoMatch": true,
      "autoMixMode": 0,
      "color": "#F1E0FF",
      "createdTime": 1737880519116,
      "cvHumanId": null,
      "cvHumanName": "",
      "cvHumanTask": true,
      "cvHumanTaskNum": null,
      "cvRobotId": null,
      "cvRobotName": "",
      "cvRobotTask": true,
      "cvRobotTaskNum": null,
      "debutChapterId": "",
      "debutChapterName": "",
      "debutChapterSeqNum": null,
      "description": "",
      "envMode": 0,
      "envType": 0,
      "eqConfig": null,
      "gainDb": 0,
      "gender": 1,
      "id": 3940865,
      "mixMode": null,
      "name": "张路虎",
      "nicknames": "",
      "numChapter": null,
      "numChar": null,
      "numCue": null,
      "presence": false,
      "robotDurationFactorSilence": 100,
      "robotGenMode": 0,
      "robotSpeedFactor": 100,
      "robotStyle": "",
      "robotStyleStandard": null,
      "selectedMixMode": 1,
      "type": null
    },
    {
      "ableToMerge": true,
      "ageType": null,
      "autoBind": false,
      "autoMatch": true,
      "autoMixMode": 0,
      "color": "#E3F3C3",
      "createdTime": 1737880520578,
      "cvHumanId": null,
      "cvHumanName": "",
      "cvHumanTask": true,
      "cvHumanTaskNum": null,
      "cvRobotId": null,
      "cvRobotName": "",
      "cvRobotTask": true,
      "cvRobotTaskNum": null,
      "debutChapterId": "",
      "debutChapterName": "",
      "debutChapterSeqNum": null,
      "description": "",
      "envMode": 0,
      "envType": 0,
      "eqConfig": null,
      "gainDb": 0,
      "gender": 1,
      "id": 3940866,
      "mixMode": null,
      "name": "张来",
      "nicknames": "",
      "numChapter": null,
      "numChar": null,
      "numCue": null,
      "presence": false,
      "robotDurationFactorSilence": 100,
      "robotGenMode": 0,
      "robotSpeedFactor": 100,
      "robotStyle": "",
      "robotStyleStandard": null,
      "selectedMixMode": 1,
      "type": null
    },
    {
      "ableToMerge": true,
      "ageType": 2,
      "autoBind": false,
      "autoMatch": true,
      "autoMixMode": 0,
      "color": "#F19AC9",
      "createdTime": 1737880529832,
      "cvHumanId": null,
      "cvHumanName": "",
      "cvHumanTask": true,
      "cvHumanTaskNum": null,
      "cvRobotId": null,
      "cvRobotName": "",
      "cvRobotTask": true,
      "cvRobotTaskNum": null,
      "debutChapterId": "",
      "debutChapterName": "",
      "debutChapterSeqNum": null,
      "description": "",
      "envMode": 0,
      "envType": 0,
      "eqConfig": null,
      "gainDb": 0,
      "gender": 2,
      "id": 3940867,
      "mixMode": null,
      "name": "俏婢",
      "nicknames": "",
      "numChapter": null,
      "numChar": null,
      "numCue": null,
      "presence": false,
      "robotDurationFactorSilence": 100,
      "robotGenMode": 0,
      "robotSpeedFactor": 100,
      "robotStyle": "",
      "robotStyleStandard": null,
      "selectedMixMode": 1,
      "type": null
    },
    {
      "ableToMerge": true,
      "ageType": 2,
      "autoBind": false,
      "autoMatch": true,
      "autoMixMode": 0,
      "color": "#FCB038",
      "createdTime": 1737880529837,
      "cvHumanId": null,
      "cvHumanName": "",
      "cvHumanTask": true,
      "cvHumanTaskNum": null,
      "cvRobotId": null,
      "cvRobotName": "",
      "cvRobotTask": true,
      "cvRobotTaskNum": null,
      "debutChapterId": "",
      "debutChapterName": "",
      "debutChapterSeqNum": null,
      "description": "",
      "envMode": 0,
      "envType": 0,
      "eqConfig": null,
      "gainDb": 0,
      "gender": null,
      "id": 3940868,
      "mixMode": null,
      "name": "美婢",
      "nicknames": "",
      "numChapter": null,
      "numChar": null,
      "numCue": null,
      "presence": false,
      "robotDurationFactorSilence": 100,
      "robotGenMode": 0,
      "robotSpeedFactor": 100,
      "robotStyle": "",
      "robotStyleStandard": null,
      "selectedMixMode": 1,
      "type": null
    }
  ],
  "msg": "成功!"
}


==================================================
=== 请求信息 ===
时间: 2025-07-24 07:24:29
方法: GET
URL: https://www.gstudios.com.cn/story_v2/api/content/character/list/chapter?chapterId=6206642&roleType=chapter&skipStat=true
备注标题: 获取章节角色列表
备注内容: 
Headers: {
  "host": "www.gstudios.com.cn",
  "connection": "keep-alive",
  "sec-ch-ua-platform": "Windows",
  "x-requested-with": "XMLHttpRequest",
  "user-agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********",
  "accept": "application/json, text/plain, */*",
  "sec-ch-ua": "\"Microsoft Edge\";v=\"135\", \"Not-A.Brand\";v=\"8\", \"Chromium\";v=\"135\"",
  "sec-ch-ua-mobile": "?0",
  "sec-fetch-site": "same-origin",
  "sec-fetch-mode": "cors",
  "sec-fetch-dest": "empty",
  "accept-encoding": "gzip, deflate, br, zstd",
  "accept-language": "zh-CN,zh;q=0.9,en;q=0.8,en-GB;q=0.7,en-US;q=0.6",
  "referer": "https://www.gstudios.com.cn/",
  "authorization": "Bearer 3dfb89119562456cb8818120139f6ae1"
}
Cookie: 
=== 响应信息 ===
状态码: 200
响应头: {'Server': 'CLOUD ELB 1.0.0', 'Date': 'Wed, 23 Jul 2025 23:24:28 GMT', 'Content-Type': 'application/json;charset=UTF-8', 'Transfer-Encoding': 'chunked', 'Connection': 'close', 'Content-Encoding': 'gzip'}

=== 响应内容 ===
{
  "code": 1,
  "data": [
    {
      "ableToMerge": false,
      "ageType": null,
      "autoBind": true,
      "autoMatch": true,
      "autoMixMode": 0,
      "color": "#CFD5E9",
      "createdTime": 1737879987820,
      "cvHumanId": null,
      "cvHumanName": "",
      "cvHumanTask": true,
      "cvHumanTaskNum": null,
      "cvRobotId": 568,
      "cvRobotName": "曹先森MK-III",
      "cvRobotTask": true,
      "cvRobotTaskNum": null,
      "debutChapterId": "",
      "debutChapterName": "",
      "debutChapterSeqNum": null,
      "description": "",
      "envMode": 0,
      "envType": 0,
      "eqConfig": {
        "bp1": {
          "f": 200,
          "g": 0,
          "on": false,
          "q": 2
        },
        "bp2": {
          "f": 800,
          "g": 0,
          "on": false,
          "q": 2
        },
        "bp3": {
          "f": 3200,
          "g": 0,
          "on": false,
          "q": 2
        },
        "hs": {
          "f": 18000,
          "g": 0,
          "on": false,
          "s": 1
        },
        "ls": {
          "f": 40,
          "g": 0,
          "on": false,
          "s": 1
        }
      },
      "gainDb": 0,
      "gender": null,
      "id": 3940779,
      "mixMode": null,
      "name": "旁白",
      "nicknames": "",
      "numChapter": null,
      "numChar": null,
      "numCue": null,
      "presence": false,
      "robotDurationFactorSilence": 100,
      "robotGenMode": 0,
      "robotSpeedFactor": 100,
      "robotStyle": "",
      "robotStyleStandard": null,
      "selectedMixMode": 0,
      "type": 0
    },
    {
      "ableToMerge": true,
      "ageType": 2,
      "autoBind": false,
      "autoMatch": true,
      "autoMixMode": 0,
      "color": "#B0F7FF",
      "createdTime": 1737880040231,
      "cvHumanId": null,
      "cvHumanName": "",
      "cvHumanTask": true,
      "cvHumanTaskNum": null,
      "cvRobotId": null,
      "cvRobotName": "",
      "cvRobotTask": true,
      "cvRobotTaskNum": null,
      "debutChapterId": "",
      "debutChapterName": "",
      "debutChapterSeqNum": null,
      "description": "",
      "envMode": 0,
      "envType": 0,
      "eqConfig": {
        "bp1": {
          "f": 200,
          "g": 0,
          "on": false,
          "q": 2
        },
        "bp2": {
          "f": 800,
          "g": 0,
          "on": false,
          "q": 2
        },
        "bp3": {
          "f": 3200,
          "g": 0,
          "on": false,
          "q": 2
        },
        "hs": {
          "f": 18000,
          "g": 0,
          "on": false,
          "s": 1
        },
        "ls": {
          "f": 40,
          "g": 0,
          "on": false,
          "s": 1
        }
      },
      "gainDb": 0,
      "gender": 1,
      "id": 3940780,
      "mixMode": null,
      "name": "叶锋",
      "nicknames": "",
      "numChapter": null,
      "numChar": null,
      "numCue": null,
      "presence": false,
      "robotDurationFactorSilence": 100,
      "robotGenMode": 0,
      "robotSpeedFactor": 100,
      "robotStyle": "",
      "robotStyleStandard": null,
      "selectedMixMode": 1,
      "type": 1
    },
    {
      "ableToMerge": true,
      "ageType": 2,
      "autoBind": false,
      "autoMatch": true,
      "autoMixMode": 0,
      "color": "#8FA8FE",
      "createdTime": 1737880040235,
      "cvHumanId": null,
      "cvHumanName": "",
      "cvHumanTask": true,
      "cvHumanTaskNum": null,
      "cvRobotId": null,
      "cvRobotName": "",
      "cvRobotTask": true,
      "cvRobotTaskNum": null,
      "debutChapterId": "",
      "debutChapterName": "",
      "debutChapterSeqNum": null,
      "description": "",
      "envMode": 0,
      "envType": 0,
      "eqConfig": null,
      "gainDb": 0,
      "gender": 2,
      "id": 3940781,
      "mixMode": null,
      "name": "刘烟",
      "nicknames": "",
      "numChapter": null,
      "numChar": null,
      "numCue": null,
      "presence": false,
      "robotDurationFactorSilence": 100,
      "robotGenMode": 0,
      "robotSpeedFactor": 100,
      "robotStyle": "",
      "robotStyleStandard": null,
      "selectedMixMode": 1,
      "type": null
    }
  ],
  "msg": "成功!"
}


==================================================
=== 请求信息 ===
时间: 2025-07-24 07:24:32
方法: GET
URL: https://www.gstudios.com.cn/story_v2/api/content/character/list/chapter?chapterId=6206642&roleType=chapter&skipStat=true
备注标题: 获取章节角色列表
备注内容: 
Headers: {
  "host": "www.gstudios.com.cn",
  "connection": "keep-alive",
  "sec-ch-ua-platform": "Windows",
  "x-requested-with": "XMLHttpRequest",
  "user-agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********",
  "accept": "application/json, text/plain, */*",
  "sec-ch-ua": "\"Microsoft Edge\";v=\"135\", \"Not-A.Brand\";v=\"8\", \"Chromium\";v=\"135\"",
  "sec-ch-ua-mobile": "?0",
  "sec-fetch-site": "same-origin",
  "sec-fetch-mode": "cors",
  "sec-fetch-dest": "empty",
  "accept-encoding": "gzip, deflate, br, zstd",
  "accept-language": "zh-CN,zh;q=0.9,en;q=0.8,en-GB;q=0.7,en-US;q=0.6",
  "referer": "https://www.gstudios.com.cn/",
  "authorization": "Bearer 3dfb89119562456cb8818120139f6ae1"
}
Cookie: 
=== 响应信息 ===
状态码: 200
响应头: {'Server': 'CLOUD ELB 1.0.0', 'Date': 'Wed, 23 Jul 2025 23:24:32 GMT', 'Content-Type': 'application/json;charset=UTF-8', 'Transfer-Encoding': 'chunked', 'Connection': 'close', 'Content-Encoding': 'gzip'}

=== 响应内容 ===
{
  "code": 1,
  "data": [
    {
      "ableToMerge": false,
      "ageType": null,
      "autoBind": true,
      "autoMatch": true,
      "autoMixMode": 0,
      "color": "#CFD5E9",
      "createdTime": 1737879987820,
      "cvHumanId": null,
      "cvHumanName": "",
      "cvHumanTask": true,
      "cvHumanTaskNum": null,
      "cvRobotId": 568,
      "cvRobotName": "曹先森MK-III",
      "cvRobotTask": true,
      "cvRobotTaskNum": null,
      "debutChapterId": "",
      "debutChapterName": "",
      "debutChapterSeqNum": null,
      "description": "",
      "envMode": 0,
      "envType": 0,
      "eqConfig": {
        "bp1": {
          "f": 200,
          "g": 0,
          "on": false,
          "q": 2
        },
        "bp2": {
          "f": 800,
          "g": 0,
          "on": false,
          "q": 2
        },
        "bp3": {
          "f": 3200,
          "g": 0,
          "on": false,
          "q": 2
        },
        "hs": {
          "f": 18000,
          "g": 0,
          "on": false,
          "s": 1
        },
        "ls": {
          "f": 40,
          "g": 0,
          "on": false,
          "s": 1
        }
      },
      "gainDb": 0,
      "gender": null,
      "id": 3940779,
      "mixMode": null,
      "name": "旁白",
      "nicknames": "",
      "numChapter": null,
      "numChar": null,
      "numCue": null,
      "presence": false,
      "robotDurationFactorSilence": 100,
      "robotGenMode": 0,
      "robotSpeedFactor": 100,
      "robotStyle": "",
      "robotStyleStandard": null,
      "selectedMixMode": 0,
      "type": 0
    },
    {
      "ableToMerge": true,
      "ageType": 2,
      "autoBind": false,
      "autoMatch": true,
      "autoMixMode": 0,
      "color": "#B0F7FF",
      "createdTime": 1737880040231,
      "cvHumanId": null,
      "cvHumanName": "",
      "cvHumanTask": true,
      "cvHumanTaskNum": null,
      "cvRobotId": null,
      "cvRobotName": "",
      "cvRobotTask": true,
      "cvRobotTaskNum": null,
      "debutChapterId": "",
      "debutChapterName": "",
      "debutChapterSeqNum": null,
      "description": "",
      "envMode": 0,
      "envType": 0,
      "eqConfig": {
        "bp1": {
          "f": 200,
          "g": 0,
          "on": false,
          "q": 2
        },
        "bp2": {
          "f": 800,
          "g": 0,
          "on": false,
          "q": 2
        },
        "bp3": {
          "f": 3200,
          "g": 0,
          "on": false,
          "q": 2
        },
        "hs": {
          "f": 18000,
          "g": 0,
          "on": false,
          "s": 1
        },
        "ls": {
          "f": 40,
          "g": 0,
          "on": false,
          "s": 1
        }
      },
      "gainDb": 0,
      "gender": 1,
      "id": 3940780,
      "mixMode": null,
      "name": "叶锋",
      "nicknames": "",
      "numChapter": null,
      "numChar": null,
      "numCue": null,
      "presence": false,
      "robotDurationFactorSilence": 100,
      "robotGenMode": 0,
      "robotSpeedFactor": 100,
      "robotStyle": "",
      "robotStyleStandard": null,
      "selectedMixMode": 1,
      "type": 1
    },
    {
      "ableToMerge": true,
      "ageType": 2,
      "autoBind": false,
      "autoMatch": true,
      "autoMixMode": 0,
      "color": "#8FA8FE",
      "createdTime": 1737880040235,
      "cvHumanId": null,
      "cvHumanName": "",
      "cvHumanTask": true,
      "cvHumanTaskNum": null,
      "cvRobotId": null,
      "cvRobotName": "",
      "cvRobotTask": true,
      "cvRobotTaskNum": null,
      "debutChapterId": "",
      "debutChapterName": "",
      "debutChapterSeqNum": null,
      "description": "",
      "envMode": 0,
      "envType": 0,
      "eqConfig": null,
      "gainDb": 0,
      "gender": 2,
      "id": 3940781,
      "mixMode": null,
      "name": "刘烟",
      "nicknames": "",
      "numChapter": null,
      "numChar": null,
      "numCue": null,
      "presence": false,
      "robotDurationFactorSilence": 100,
      "robotGenMode": 0,
      "robotSpeedFactor": 100,
      "robotStyle": "",
      "robotStyleStandard": null,
      "selectedMixMode": 1,
      "type": null
    }
  ],
  "msg": "成功!"
}


==================================================
=== 请求信息 ===
时间: 2025-07-24 07:28:40
方法: GET
URL: https://www.gstudios.com.cn/story_v2/api/record/task/state/chapter/cues?chapterId=6206642
备注标题: 获取章节片段录音任务状态列表
备注内容: 
Headers: {
  "host": "www.gstudios.com.cn",
  "connection": "keep-alive",
  "sec-ch-ua-platform": "Windows",
  "x-requested-with": "XMLHttpRequest",
  "user-agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********",
  "accept": "application/json, text/plain, */*",
  "sec-ch-ua": "\"Microsoft Edge\";v=\"135\", \"Not-A.Brand\";v=\"8\", \"Chromium\";v=\"135\"",
  "sec-ch-ua-mobile": "?0",
  "sec-fetch-site": "same-origin",
  "sec-fetch-mode": "cors",
  "sec-fetch-dest": "empty",
  "accept-encoding": "gzip, deflate, br, zstd",
  "accept-language": "zh-CN,zh;q=0.9,en;q=0.8,en-GB;q=0.7,en-US;q=0.6",
  "referer": "https://www.gstudios.com.cn/",
  "authorization": "Bearer 3dfb89119562456cb8818120139f6ae1"
}
Cookie: 
=== 响应信息 ===
状态码: 200
响应头: {'Server': 'CLOUD ELB 1.0.0', 'Date': 'Wed, 23 Jul 2025 23:28:39 GMT', 'Content-Type': 'application/json;charset=UTF-8', 'Transfer-Encoding': 'chunked', 'Connection': 'close', 'Content-Encoding': 'gzip'}

=== 响应内容 ===
{
  "code": 1,
  "data": {
    "infoHuman": [
      {
        "beenFinished": false,
        "characterId": 3940781,
        "cueId": 510769319,
        "cvId": null,
        "numCharCharged": null,
        "taskId": null,
        "taskState": null
      },
      {
        "beenFinished": false,
        "characterId": 3940779,
        "cueId": 510769292,
        "cvId": null,
        "numCharCharged": null,
        "taskId": null,
        "taskState": null
      },
      {
        "beenFinished": false,
        "characterId": 3940779,
        "cueId": 510769293,
        "cvId": null,
        "numCharCharged": null,
        "taskId": null,
        "taskState": null
      },
      {
        "beenFinished": false,
        "characterId": 3940781,
        "cueId": 510769294,
        "cvId": null,
        "numCharCharged": null,
        "taskId": null,
        "taskState": null
      },
      {
        "beenFinished": false,
        "characterId": 3940780,
        "cueId": 510769296,
        "cvId": null,
        "numCharCharged": null,
        "taskId": null,
        "taskState": null
      },
      {
        "beenFinished": false,
        "characterId": 3940779,
        "cueId": 510769297,
        "cvId": null,
        "numCharCharged": null,
        "taskId": null,
        "taskState": null
      },
      {
        "beenFinished": false,
        "characterId": 3940779,
        "cueId": 510769300,
        "cvId": null,
        "numCharCharged": null,
        "taskId": null,
        "taskState": null
      },
      {
        "beenFinished": false,
        "characterId": 3940779,
        "cueId": 510769301,
        "cvId": null,
        "numCharCharged": null,
        "taskId": null,
        "taskState": null
      },
      {
        "beenFinished": false,
        "characterId": 3940779,
        "cueId": 510769302,
        "cvId": null,
        "numCharCharged": null,
        "taskId": null,
        "taskState": null
      },
      {
        "beenFinished": false,
        "characterId": 3940779,
        "cueId": 510769303,
        "cvId": null,
        "numCharCharged": null,
        "taskId": null,
        "taskState": null
      },
      {
        "beenFinished": false,
        "characterId": 3940779,
        "cueId": 510769304,
        "cvId": null,
        "numCharCharged": null,
        "taskId": null,
        "taskState": null
      },
      {
        "beenFinished": false,
        "characterId": 3940779,
        "cueId": 510769305,
        "cvId": null,
        "numCharCharged": null,
        "taskId": null,
        "taskState": null
      },
      {
        "beenFinished": false,
        "characterId": 3940779,
        "cueId": 510769306,
        "cvId": null,
        "numCharCharged": null,
        "taskId": null,
        "taskState": null
      },
      {
        "beenFinished": false,
        "characterId": 3940779,
        "cueId": 510769307,
        "cvId": null,
        "numCharCharged": null,
        "taskId": null,
        "taskState": null
      },
      {
        "beenFinished": false,
        "characterId": 3940779,
        "cueId": 510769308,
        "cvId": null,
        "numCharCharged": null,
        "taskId": null,
        "taskState": null
      },
      {
        "beenFinished": false,
        "characterId": 3940779,
        "cueId": 510769309,
        "cvId": null,
        "numCharCharged": null,
        "taskId": null,
        "taskState": null
      },
      {
        "beenFinished": false,
        "characterId": 3940779,
        "cueId": 510769310,
        "cvId": null,
        "numCharCharged": null,
        "taskId": null,
        "taskState": null
      },
      {
        "beenFinished": false,
        "characterId": 3940779,
        "cueId": 510769311,
        "cvId": null,
        "numCharCharged": null,
        "taskId": null,
        "taskState": null
      },
      {
        "beenFinished": false,
        "characterId": 3940779,
        "cueId": 510769312,
        "cvId": null,
        "numCharCharged": null,
        "taskId": null,
        "taskState": null
      },
      {
        "beenFinished": false,
        "characterId": 3940779,
        "cueId": 510769313,
        "cvId": null,
        "numCharCharged": null,
        "taskId": null,
        "taskState": null
      },
      {
        "beenFinished": false,
        "characterId": 3940779,
        "cueId": 510769314,
        "cvId": null,
        "numCharCharged": null,
        "taskId": null,
        "taskState": null
      },
      {
        "beenFinished": false,
        "characterId": 3940781,
        "cueId": 510769315,
        "cvId": null,
        "numCharCharged": null,
        "taskId": null,
        "taskState": null
      },
      {
        "beenFinished": false,
        "characterId": 3940779,
        "cueId": 510769316,
        "cvId": null,
        "numCharCharged": null,
        "taskId": null,
        "taskState": null
      },
      {
        "beenFinished": false,
        "characterId": 3940781,
        "cueId": 510769317,
        "cvId": null,
        "numCharCharged": null,
        "taskId": null,
        "taskState": null
      },
      {
        "beenFinished": false,
        "characterId": 3940779,
        "cueId": 510769318,
        "cvId": null,
        "numCharCharged": null,
        "taskId": null,
        "taskState": null
      },
      {
        "beenFinished": false,
        "characterId": 3940780,
        "cueId": 510769320,
        "cvId": null,
        "numCharCharged": null,
        "taskId": null,
        "taskState": null
      },
      {
        "beenFinished": false,
        "characterId": 3940780,
        "cueId": 510769321,
        "cvId": null,
        "numCharCharged": null,
        "taskId": null,
        "taskState": null
      },
      {
        "beenFinished": false,
        "characterId": 3940779,
        "cueId": 510769322,
        "cvId": null,
        "numCharCharged": null,
        "taskId": null,
        "taskState": null
      },
      {
        "beenFinished": false,
        "characterId": 3940779,
        "cueId": 510769323,
        "cvId": null,
        "numCharCharged": null,
        "taskId": null,
        "taskState": null
      },
      {
        "beenFinished": false,
        "characterId": 3940779,
        "cueId": 510769291,
        "cvId": null,
        "numCharCharged": null,
        "taskId": null,
        "taskState": null
      }
    ],
    "infoRobot": [
      {
        "beenFinished": false,
        "characterId": 3940781,
        "cueId": 510769319,
        "cvId": null,
        "numCharCharged": null,
        "taskId": null,
        "taskState": null
      },
      {
        "beenFinished": false,
        "characterId": 3940779,
        "cueId": 510769292,
        "cvId": null,
        "numCharCharged": null,
        "taskId": null,
        "taskState": null
      },
      {
        "beenFinished": false,
        "characterId": 3940779,
        "cueId": 510769293,
        "cvId": null,
        "numCharCharged": null,
        "taskId": null,
        "taskState": null
      },
      {
        "beenFinished": false,
        "characterId": 3940781,
        "cueId": 510769294,
        "cvId": null,
        "numCharCharged": null,
        "taskId": null,
        "taskState": null
      },
      {
        "beenFinished": false,
        "characterId": 3940780,
        "cueId": 510769296,
        "cvId": null,
        "numCharCharged": null,
        "taskId": null,
        "taskState": null
      },
      {
        "beenFinished": false,
        "characterId": 3940779,
        "cueId": 510769297,
        "cvId": null,
        "numCharCharged": null,
        "taskId": null,
        "taskState": null
      },
      {
        "beenFinished": false,
        "characterId": 3940779,
        "cueId": 510769300,
        "cvId": null,
        "numCharCharged": null,
        "taskId": null,
        "taskState": null
      },
      {
        "beenFinished": false,
        "characterId": 3940779,
        "cueId": 510769301,
        "cvId": null,
        "numCharCharged": null,
        "taskId": null,
        "taskState": null
      },
      {
        "beenFinished": false,
        "characterId": 3940779,
        "cueId": 510769302,
        "cvId": null,
        "numCharCharged": null,
        "taskId": null,
        "taskState": null
      },
      {
        "beenFinished": false,
        "characterId": 3940779,
        "cueId": 510769303,
        "cvId": null,
        "numCharCharged": null,
        "taskId": null,
        "taskState": null
      },
      {
        "beenFinished": false,
        "characterId": 3940779,
        "cueId": 510769304,
        "cvId": null,
        "numCharCharged": null,
        "taskId": null,
        "taskState": null
      },
      {
        "beenFinished": false,
        "characterId": 3940779,
        "cueId": 510769305,
        "cvId": null,
        "numCharCharged": null,
        "taskId": null,
        "taskState": null
      },
      {
        "beenFinished": false,
        "characterId": 3940779,
        "cueId": 510769306,
        "cvId": null,
        "numCharCharged": null,
        "taskId": null,
        "taskState": null
      },
      {
        "beenFinished": false,
        "characterId": 3940779,
        "cueId": 510769307,
        "cvId": null,
        "numCharCharged": null,
        "taskId": null,
        "taskState": null
      },
      {
        "beenFinished": false,
        "characterId": 3940779,
        "cueId": 510769308,
        "cvId": null,
        "numCharCharged": null,
        "taskId": null,
        "taskState": null
      },
      {
        "beenFinished": false,
        "characterId": 3940779,
        "cueId": 510769309,
        "cvId": null,
        "numCharCharged": null,
        "taskId": null,
        "taskState": null
      },
      {
        "beenFinished": false,
        "characterId": 3940779,
        "cueId": 510769310,
        "cvId": null,
        "numCharCharged": null,
        "taskId": null,
        "taskState": null
      },
      {
        "beenFinished": false,
        "characterId": 3940779,
        "cueId": 510769311,
        "cvId": null,
        "numCharCharged": null,
        "taskId": null,
        "taskState": null
      },
      {
        "beenFinished": false,
        "characterId": 3940779,
        "cueId": 510769312,
        "cvId": null,
        "numCharCharged": null,
        "taskId": null,
        "taskState": null
      },
      {
        "beenFinished": false,
        "characterId": 3940779,
        "cueId": 510769313,
        "cvId": null,
        "numCharCharged": null,
        "taskId": null,
        "taskState": null
      },
      {
        "beenFinished": false,
        "characterId": 3940779,
        "cueId": 510769314,
        "cvId": null,
        "numCharCharged": null,
        "taskId": null,
        "taskState": null
      },
      {
        "beenFinished": false,
        "characterId": 3940781,
        "cueId": 510769315,
        "cvId": null,
        "numCharCharged": null,
        "taskId": null,
        "taskState": null
      },
      {
        "beenFinished": false,
        "characterId": 3940779,
        "cueId": 510769316,
        "cvId": null,
        "numCharCharged": null,
        "taskId": null,
        "taskState": null
      },
      {
        "beenFinished": false,
        "characterId": 3940781,
        "cueId": 510769317,
        "cvId": null,
        "numCharCharged": null,
        "taskId": null,
        "taskState": null
      },
      {
        "beenFinished": false,
        "characterId": 3940779,
        "cueId": 510769318,
        "cvId": null,
        "numCharCharged": null,
        "taskId": null,
        "taskState": null
      },
      {
        "beenFinished": false,
        "characterId": 3940780,
        "cueId": 510769320,
        "cvId": null,
        "numCharCharged": null,
        "taskId": null,
        "taskState": null
      },
      {
        "beenFinished": false,
        "characterId": 3940780,
        "cueId": 510769321,
        "cvId": null,
        "numCharCharged": null,
        "taskId": null,
        "taskState": null
      },
      {
        "beenFinished": false,
        "characterId": 3940779,
        "cueId": 510769322,
        "cvId": null,
        "numCharCharged": null,
        "taskId": null,
        "taskState": null
      },
      {
        "beenFinished": false,
        "characterId": 3940779,
        "cueId": 510769323,
        "cvId": null,
        "numCharCharged": null,
        "taskId": null,
        "taskState": null
      },
      {
        "beenFinished": false,
        "characterId": 3940779,
        "cueId": 510769291,
        "cvId": 568,
        "numCharCharged": 0,
        "taskId": 201194018,
        "taskState": 4
      }
    ]
  },
  "msg": "成功!"
}

