#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
书籍搜索模块

实现书籍搜索界面，支持模糊匹配和精确匹配，显示搜索结果和选择机制。

作者：Augment Agent
版本：1.0.0
"""

import tkinter as tk
from tkinter import ttk, messagebox
import threading
from typing import List, Dict, Any, Optional

from utils.logger import LoggerMixin
from utils.field_utils import (
    get_book_field, normalize_book_data, find_book_by_id,
    format_book_display_info, get_book_display_values
)
from utils.table_styles import setup_striped_table, refresh_table_stripes


class BookSearchFrame(ttk.Frame, LoggerMixin):
    """书籍搜索框架"""
    
    def __init__(self, parent, main_window):
        """
        初始化书籍搜索框架
        
        Args:
            parent: 父窗口
            main_window: 主窗口实例
        """
        super().__init__(parent)
        self.main_window = main_window
        self.api_client = None
        self.books_data = []
        
        self._create_widgets()
        self._setup_layout()
        self._bind_events()

        # 延迟自动加载书籍列表
        self.after(1000, self._auto_load_books)

        self.logger.info("书籍搜索模块初始化完成")

    def _create_context_menu(self):
        """创建右键上下文菜单"""
        self.context_menu = tk.Menu(self, tearoff=0)
        self.context_menu.add_command(label="查看详情", command=self._view_book_detail)

    def _auto_load_books(self):
        """自动加载书籍列表"""
        try:
            # 检查API客户端是否可用
            if self._check_api_client():
                # 清空搜索框，执行空搜索（相当于获取所有书籍）
                self.search_var.set("")
                self._search_books()
                self.logger.info("自动加载书籍列表")
            else:
                # 如果API客户端不可用，在状态栏显示提示信息
                self.main_window.update_search_result_status(
                    "请先在设置菜单中配置API Token，然后点击搜索按钮获取书籍列表"
                )
        except Exception as e:
            self.logger.debug(f"自动加载书籍列表失败: {e}")
            # 不显示错误，因为这是自动操作

    def _create_widgets(self):
        """创建界面组件"""
        # 搜索区域
        search_frame = ttk.LabelFrame(self, text="书籍搜索", padding=10)

        # 第一行：搜索输入、控件和状态筛选
        # 书籍名称标签
        ttk.Label(search_frame, text="书籍名称:").grid(row=0, column=0, sticky=tk.W, padx=(0, 5))

        # 搜索输入框
        self.search_var = tk.StringVar()
        self.search_entry = ttk.Entry(search_frame, textvariable=self.search_var, width=25)
        self.search_entry.grid(row=0, column=1, padx=(0, 5))

        # 精确匹配复选框
        self.exact_match_var = tk.BooleanVar()
        self.exact_match_check = ttk.Checkbutton(
            search_frame,
            text="精确匹配",
            variable=self.exact_match_var
        )
        self.exact_match_check.grid(row=0, column=2, padx=(5, 5))

        # 搜索按钮
        self.search_button = ttk.Button(search_frame, text="搜索", command=self._search_books)
        self.search_button.grid(row=0, column=3, padx=(5, 10))

        # 状态筛选标签
        ttk.Label(search_frame, text="状态:").grid(row=0, column=4, sticky=tk.W, padx=(5, 5))

        # 状态筛选单选按钮组（移动到第一行）
        # 默认选中"制作中"
        self.status_filter_var = tk.StringVar(value="working")
        status_frame = ttk.Frame(search_frame)
        status_frame.grid(row=0, column=5, sticky=tk.W, padx=(0, 0))

        # 调整顺序：制作中 → 完成 → 全部
        self.status_working_radio = ttk.Radiobutton(
            status_frame,
            text="制作中",
            variable=self.status_filter_var,
            value="working",
            command=self._on_status_filter_changed
        )
        self.status_working_radio.pack(side=tk.LEFT, padx=(0, 8))

        self.status_finished_radio = ttk.Radiobutton(
            status_frame,
            text="完成",
            variable=self.status_filter_var,
            value="finished",
            command=self._on_status_filter_changed
        )
        self.status_finished_radio.pack(side=tk.LEFT, padx=(0, 8))

        self.status_all_radio = ttk.Radiobutton(
            status_frame,
            text="全部",
            variable=self.status_filter_var,
            value="all",
            command=self._on_status_filter_changed
        )
        self.status_all_radio.pack(side=tk.LEFT)

        # 移除搜索结果统计信息标签，改为在状态栏显示

        # 结果区域
        results_frame = ttk.LabelFrame(self, text="搜索结果", padding=10)
        
        # 创建Treeview显示搜索结果（移除书籍ID列）
        columns = ("book_name", "description", "status", "created_time", "updated_time")
        self.results_tree = ttk.Treeview(results_frame, columns=columns, show="headings", height=15)

        # 设置列标题
        self.results_tree.heading("book_name", text="书籍名称")
        self.results_tree.heading("description", text="描述")
        self.results_tree.heading("status", text="制作状态")
        self.results_tree.heading("created_time", text="创建时间")
        self.results_tree.heading("updated_time", text="编辑时间")

        # 设置列宽（重新调整以充分利用界面空间）
        self.results_tree.column("book_name", width=220)  # 增加书籍名称列宽
        self.results_tree.column("description", width=250)  # 增加描述列宽
        self.results_tree.column("status", width=100)  # 增加状态列宽
        self.results_tree.column("created_time", width=150)  # 增加创建时间列宽
        self.results_tree.column("updated_time", width=150)  # 增加编辑时间列宽
        
        # 添加滚动条
        self.scrollbar_y = ttk.Scrollbar(results_frame, orient=tk.VERTICAL, command=self.results_tree.yview)
        self.scrollbar_x = ttk.Scrollbar(results_frame, orient=tk.HORIZONTAL, command=self.results_tree.xview)
        self.results_tree.configure(yscrollcommand=self.scrollbar_y.set, xscrollcommand=self.scrollbar_x.set)

        # 应用条纹化样式美化表格
        setup_striped_table(self.results_tree, auto_update=True)
        
        # 移除操作按钮区域和选中书籍信息显示区域（功能将通过右键菜单和工具栏提供）
        
        # 创建右键上下文菜单
        self._create_context_menu()

        # 保存组件引用
        self.search_frame = search_frame
        self.results_frame = results_frame
    
    def _setup_layout(self):
        """设置布局"""
        self.search_frame.pack(fill=tk.X, pady=(0, 10))
        self.results_frame.pack(fill=tk.BOTH, expand=True, pady=(0, 10))
        
        # 结果树形视图布局
        self.results_tree.grid(row=0, column=0, sticky="nsew")
        self.scrollbar_y.grid(row=0, column=1, sticky="ns")
        self.scrollbar_x.grid(row=1, column=0, sticky="ew")
        
        self.results_frame.grid_rowconfigure(0, weight=1)
        self.results_frame.grid_columnconfigure(0, weight=1)
        
        # 移除按钮和选中信息布局
    
    def _bind_events(self):
        """绑定事件"""
        # 搜索框回车事件
        self.search_entry.bind("<Return>", lambda e: self._search_books())
        
        # 树形视图选择事件
        self.results_tree.bind("<<TreeviewSelect>>", self._on_tree_select)
        
        # 双击选择书籍
        self.results_tree.bind("<Double-1>", lambda e: self._select_book())

        # 右键菜单
        self.results_tree.bind("<Button-3>", self._show_context_menu)
    
    def _on_tree_select(self, event):
        """树形视图选择事件处理"""
        # 按钮已移除，此方法保留以备将来扩展
        pass

    def _show_context_menu(self, event):
        """显示右键上下文菜单"""
        # 获取点击位置的项目
        item = self.results_tree.identify_row(event.y)
        if item:
            # 选中该项目
            self.results_tree.selection_set(item)
            # 显示右键菜单
            try:
                self.context_menu.tk_popup(event.x_root, event.y_root)
            finally:
                self.context_menu.grab_release()
    
    def _search_books(self):
        """搜索书籍"""
        if not self._check_api_client():
            return

        query = self.search_var.get().strip()
        exact_match = self.exact_match_var.get()
        status_filter = self.status_filter_var.get()

        # 更新状态信息
        if query:
            self.main_window.update_status("正在搜索书籍...")
        else:
            self.main_window.update_status("正在获取书籍列表...")

        self.search_button.config(state=tk.DISABLED)

        # 在后台线程中执行搜索
        def search_thread():
            try:
                # 获取状态筛选参数
                finished_param = None
                if status_filter == "finished":
                    finished_param = True
                elif status_filter == "working":
                    finished_param = False
                # status_filter == "all" 时，finished_param 保持 None

                if exact_match and query:
                    # 精确匹配：使用API搜索，然后进行精确筛选
                    books = self._search_books_with_status(query, finished_param)
                    filtered_books = [
                        book for book in books
                        if (get_book_field(book, 'name') or '').lower() == query.lower()
                    ]
                else:
                    # 模糊匹配或空搜索：直接使用API搜索
                    # 空搜索相当于获取所有书籍（根据状态筛选）
                    filtered_books = self._search_books_with_status(query, finished_param)

                # 在主线程中更新UI
                self.after(0, lambda: self._update_search_results(filtered_books))

            except Exception as e:
                self.logger.error(f"搜索书籍失败: {e}")
                self.after(0, lambda: self._handle_search_error(str(e)))

        threading.Thread(target=search_thread, daemon=True).start()
    


    def _search_books_with_status(self, query: str, finished: Optional[bool]) -> List[Dict[str, Any]]:
        """
        使用状态筛选搜索书籍

        Args:
            query: 搜索关键词
            finished: 完成状态筛选 (None=全部, True=已完成, False=制作中)

        Returns:
            List[Dict]: 书籍列表
        """
        try:
            from api.models import BookListEditorParams

            # 构建查询参数
            params = BookListEditorParams(
                page_size=100,  # 增加页面大小以获取更多结果
                page_no=1,
                name=query if query else None,
                finished=finished
            )

            # 调用API
            response = self.api_client.get_book_list_editor(params)

            # 调试日志
            self.logger.debug(f"API响应: code={response.code}, msg={response.msg}, data_type={type(response.data)}")

            if response.code == 1 and response.data:  # ErrorCodes.SUCCESS = 1
                book_list = response.data.get('list', [])
                self.logger.debug(f"获取到书籍列表，数量: {len(book_list)}")
                return book_list
            else:
                self.logger.warning(f"API返回失败: code={response.code}, msg={response.msg}")
                return []

        except Exception as e:
            self.logger.error(f"搜索书籍失败: {e}")
            raise

    def _on_status_filter_changed(self):
        """状态筛选变更处理"""
        # 无论是否有搜索内容，都重新搜索
        # 空搜索相当于获取所有书籍（根据状态筛选）
        self._search_books()

    def _update_search_results(self, books: List[Dict[str, Any]]):
        """更新搜索结果"""
        # 清空现有结果
        for item in self.results_tree.get_children():
            self.results_tree.delete(item)

        # 标准化书籍数据并添加新结果
        self.books_data = []
        for book in books:
            # 标准化书籍数据，确保兼容性
            normalized_book = normalize_book_data(book)
            self.books_data.append(normalized_book)

            # 获取显示值（移除ID列：名称、描述、状态、创建时间、编辑时间）
            book_id, book_name, description, status, created_time, updated_time = get_book_display_values(normalized_book)

            self.results_tree.insert("", tk.END, values=(
                book_name, description, status, created_time, updated_time
            ))

            # 在调试模式下记录书籍字段
            if hasattr(self.api_client, 'debug_mode') and self.api_client.debug_mode:
                self.logger.debug(f"原始书籍字段: {list(book.keys())}")
                self.logger.debug(f"标准化后字段: {list(normalized_book.keys())}")
                self.logger.debug(f"显示数据: ID={book_id}, 名称={book_name}, 描述={description}, 状态={status}")
                self.logger.debug(f"时间信息: 创建={created_time}, 编辑={updated_time}")

        # 更新状态栏显示搜索结果统计信息
        count = len(books)
        query = self.search_var.get().strip()
        status_filter = self.status_filter_var.get()

        if query:
            if status_filter == "all":
                info_text = f"搜索 \"{query}\" 找到 {count} 本书籍"
            elif status_filter == "working":
                info_text = f"搜索 \"{query}\" 找到 {count} 本制作中的书籍"
            elif status_filter == "finished":
                info_text = f"搜索 \"{query}\" 找到 {count} 本完成的书籍"
        else:
            if status_filter == "all":
                info_text = f"共找到 {count} 本书籍"
            elif status_filter == "working":
                info_text = f"共找到 {count} 本制作中的书籍"
            elif status_filter == "finished":
                info_text = f"共找到 {count} 本完成的书籍"

        # 将搜索结果信息显示在状态栏
        self.main_window.update_search_result_status(info_text)

        # 刷新表格条纹化效果
        refresh_table_stripes(self.results_tree)

        # 恢复按钮状态
        self.search_button.config(state=tk.NORMAL)

        self.logger.info(f"搜索完成，找到 {count} 本书籍")
    
    def _handle_search_error(self, error_message: str):
        """处理搜索错误"""
        # 显示详细的错误信息和建议
        error_dialog = tk.Toplevel(self)
        error_dialog.title("搜索失败")
        error_dialog.geometry("600x400")
        error_dialog.transient(self)
        error_dialog.grab_set()

        # 创建文本框显示错误详情
        text_frame = ttk.Frame(error_dialog)
        text_frame.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)

        error_text = tk.Text(text_frame, wrap=tk.WORD)
        scrollbar = ttk.Scrollbar(text_frame, orient=tk.VERTICAL, command=error_text.yview)
        error_text.configure(yscrollcommand=scrollbar.set)

        error_text.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        scrollbar.pack(side=tk.RIGHT, fill=tk.Y)

        # 填充错误信息
        error_content = f"搜索书籍时发生错误\n"
        error_content += "=" * 50 + "\n\n"
        error_content += f"错误信息: {error_message}\n\n"

        # 添加可能的解决建议
        error_content += "可能的解决方案:\n"
        error_content += "1. 检查网络连接是否正常\n"
        error_content += "2. 验证API Token是否正确设置\n"
        error_content += "3. 在设置菜单中使用'测试API连接'进行诊断\n"
        error_content += "4. 尝试增加超时时间设置\n"
        error_content += "5. 检查防火墙或代理设置\n\n"

        error_content += "如果问题持续存在，请:\n"
        error_content += "- 启用调试模式查看详细日志\n"
        error_content += "- 联系技术支持并提供日志文件\n"

        error_text.insert(1.0, error_content)
        error_text.config(state=tk.DISABLED)

        # 按钮框架
        button_frame = ttk.Frame(error_dialog)
        button_frame.pack(fill=tk.X, padx=10, pady=(0, 10))

        ttk.Button(button_frame, text="打开设置菜单", command=lambda: [error_dialog.destroy(), self._show_settings_hint()]).pack(side=tk.LEFT, padx=(0, 5))
        ttk.Button(button_frame, text="关闭", command=error_dialog.destroy).pack(side=tk.RIGHT)

        self.main_window.update_status("搜索失败")

        # 恢复按钮状态
        self.search_button.config(state=tk.NORMAL)

    def _show_settings_hint(self):
        """显示设置提示"""
        messagebox.showinfo(
            "API连接测试",
            "API连接测试功能已移动到主菜单。\n\n"
            "请使用以下方式进行API连接测试：\n"
            "1. 点击菜单栏的'设置'\n"
            "2. 选择'测试API连接'\n\n"
            "您也可以使用'网络诊断'功能进行更详细的网络检查。"
        )
    
    def _select_book(self):
        """选择书籍"""
        selection = self.results_tree.selection()
        if not selection:
            messagebox.showwarning("提示", "请先选择一本书籍")
            return

        # 获取选中项的索引
        selected_item = selection[0]
        try:
            # 获取选中项在树形视图中的索引
            all_items = self.results_tree.get_children()
            selected_index = all_items.index(selected_item)

            # 通过索引从books_data获取完整的书籍信息
            if 0 <= selected_index < len(self.books_data):
                selected_book = self.books_data[selected_index]

                # 更新工作流状态
                self.main_window.update_workflow_state('selected_book', selected_book)

                # 切换到下一步
                self.main_window.next_step()

                book_name = get_book_field(selected_book, 'name')
                self.logger.info(f"已选择书籍: {book_name}")
            else:
                messagebox.showerror("错误", "选中的书籍索引超出范围")
                self.logger.error(f"书籍索引超出范围: {selected_index}")

        except (ValueError, IndexError) as e:
            messagebox.showerror("错误", "获取选中书籍信息失败")
            self.logger.error(f"获取选中书籍信息失败: {e}")

    def _view_book_detail(self):
        """查看书籍详情"""
        selection = self.results_tree.selection()
        if not selection:
            return

        # 获取选中项的索引
        selected_item = selection[0]
        try:
            # 获取选中项在树形视图中的索引
            all_items = self.results_tree.get_children()
            selected_index = all_items.index(selected_item)

            # 通过索引从books_data获取完整的书籍信息
            if 0 <= selected_index < len(self.books_data):
                selected_book = self.books_data[selected_index]
                book_id = get_book_field(selected_book, 'id')
                book_name = get_book_field(selected_book, 'name')

                # 这里可以实现查看书籍详情的功能
                messagebox.showinfo("书籍详情", f"书籍ID: {book_id}\n书籍名称: {book_name}\n\n详情功能待实现...")
            else:
                messagebox.showerror("错误", "选中的书籍索引超出范围")
                self.logger.error(f"查看详情时书籍索引超出范围: {selected_index}")

        except (ValueError, IndexError) as e:
            messagebox.showerror("错误", "获取选中书籍信息失败")
            self.logger.error(f"查看详情时获取书籍信息失败: {e}")
    
    # _display_selected_book 方法已移除，书籍信息现在显示在工具栏中
    
    def _check_api_client(self) -> bool:
        """检查API客户端是否可用"""
        if self.api_client is None:
            # 尝试创建API客户端
            try:
                from api.client import GStudioAPIClient
                
                api_config = self.main_window.config.get_api_config()
                from api.models import APIVersion

                # 检查是否启用调试模式
                debug_mode = self.main_window.config.get('logging.level', 'INFO').upper() == 'DEBUG'

                self.api_client = GStudioAPIClient(
                    base_url=api_config.get('base_url'),
                    version=APIVersion.V2,
                    timeout=api_config.get('timeout', 30),
                    max_retries=api_config.get('max_retries', 3),
                    retry_delay=api_config.get('retry_delay', 1.0),
                    debug_mode=debug_mode,
                    verify_ssl=self.main_window.config.is_ssl_verification_enabled(),
                    request_interval=self.main_window.config.get('api.request_interval', 0.1)
                )
                
                # 设置token
                token = api_config.get('token')
                if token:
                    self.api_client.set_token(token)
                    self.main_window.set_connection_status(True)
                else:
                    messagebox.showwarning("提示", "请先在菜单中设置API Token")
                    return False
                
            except Exception as e:
                self.logger.error(f"创建API客户端失败: {e}")

                # 检查是否是SSL问题
                if "SSL" in str(e) and "CERTIFICATE_VERIFY_FAILED" in str(e):
                    # 显示SSL问题对话框
                    self.main_window._show_ssl_problem_dialog(str(e))
                else:
                    messagebox.showerror("错误", f"创建API客户端失败:\n{e}")
                return False
        
        return True
