# TTS API 更新说明

## 🎯 更新概述

基于新的API调用记录文件 `request_log_20250722_171914.txt`，我们成功添加了对**TTS（文本转语音）试听API**的完整支持。

## 🚀 新增功能

### 1. TTS试听API支持
- **端点**: `POST /story_v2/api/record/tts/trial`
- **功能**: 生成试听音频文件（MP3格式）
- **特色**: 支持语速调节、静音控制、随机种子等高级参数

### 2. 新增数据类型
```python
# TTS生成模式
class GenMode(IntEnum):
    DEFAULT = 0

# TTS请求参数
@dataclass
class TTSTrialParams:
    bookId: str
    cvRobotId: int
    ssml: str
    genMode: GenMode = GenMode.DEFAULT
    speedFactor: int = 100
    durationFactorSilence: int = 100
    seed: Optional[int] = None
```

### 3. 重组API端点
```python
class Record:
    """录音相关接口"""
    TTS_TRIAL = "/record/tts/trial"
    CHAPTER_CV_INFO = "/record/task/batch/cancel/chapter/cv/info"
    CHAPTER_CV_CANCEL = "/record/task/batch/cancel/chapter/cv"
```

## 📖 快速使用

### 基本用法
```python
from gstudio_api import APIEndpoints, TTSTrialParams, GenMode

# 创建TTS参数
params = TTSTrialParams(
    bookId="39726",
    cvRobotId=568,
    ssml="你好，这是一个测试。"
)

# 获取API端点
url = APIEndpoints.get_url(APIEndpoints.Record.TTS_TRIAL)
# 结果: https://www.gstudios.com.cn/story_v2/api/record/tts/trial
```

### 完整示例
```python
# 与实际API调用记录一致的参数
params = TTSTrialParams(
    bookId="39726",
    cvRobotId=568,
    ssml="在一道斥责声中，林止陌迷迷糊糊的抬起头，一道模糊的窈窕身影，慢慢的在他眼前变得清晰起来。",
    genMode=GenMode.DEFAULT,
    speedFactor=100,
    durationFactorSilence=100,
    seed=None
)
```

## ⚠️ 重要说明

### 特殊响应格式
TTS API返回**二进制音频数据**（MP3格式），而不是标准的JSON响应：
- Content-Type: `audio/mpeg`
- 响应体: 二进制MP3数据
- 需要特殊的响应处理方式

### 使用注意事项
1. 确保有有效的认证令牌
2. 响应数据需要保存为文件
3. 文件扩展名建议使用 `.mp3`
4. 大文件可能需要流式下载

## 📁 相关文件

### 核心代码
- `simple-gstudio-api/gstudio_api.py` - 主要API库
- `docs/api_endpoints.md` - API文档

### 示例和测试
- `tts_usage_example.py` - 完整使用示例
- `test_tts_api.py` - 自动化测试脚本

### 文档
- `TTS_API_更新总结.md` - 详细更新说明
- `更新验证清单.md` - 验证清单
- `README_TTS_更新.md` - 本文件

## ✅ 验证状态

所有功能已通过完整测试：
- ✅ 参数创建和验证
- ✅ API端点URL生成
- ✅ 版本兼容性
- ✅ 与实际API调用记录的一致性

## 🔧 运行测试

```bash
# 运行自动化测试
python test_tts_api.py

# 运行使用示例
python tts_usage_example.py
```

## 📞 技术支持

如有问题，请参考：
1. `docs/api_endpoints.md` - 完整API文档
2. `tts_usage_example.py` - 实际使用示例
3. `TTS_API_更新总结.md` - 详细技术说明

---

**更新完成时间**: 2025-07-23  
**更新状态**: ✅ 完成并验证  
**兼容性**: 向后兼容，无破坏性变更
