"""
GStudio API支持库
简单的API端点定义和数据结构支持
"""
from enum import Enum, IntEnum
from typing import Optional, Any, Dict, List
from dataclasses import dataclass
from datetime import datetime

class APIVersion(str, Enum):
    """API版本"""
    V1 = "story"
    V2 = "story_v2"

class EditionState(IntEnum):
    """编辑状态"""
    NOT_PUBLISHED = 0     # 未发布
    EDITING = 1           # 编辑中
    SUBMITTED = 2         # 已提交
    CANCELLED = 3         # 已取消

class ExecutionState(IntEnum):
    """录音状态"""
    NOT_PUBLISHED = 0     # 未发布
    RECORDING_OLD = 1     # (录音中) - 旧状态，保留兼容性
    RETURNING_OLD = 2     # (返音中) - 旧状态，保留兼容性
    COMPLETED_OLD = 3     # (已完成) - 旧状态，保留兼容性
    RECORDING = 4         # 录音中
    RETURNING = 5         # 返音中
    COMPLETED = 6         # 已完成

class AuditionState(IntEnum):
    """审听状态"""
    NOT_PUBLISHED = 0     # 未发布
    PUBLISHING = 1        # 发布中
    PENDING_AUDITION = 2  # 待审听
    SUBMITTED = 3         # 已提交
    CANCELLED = 4         # 已取消
    EXCEPTION = 5         # 异常

class RangeMode(IntEnum):
    """范围模式"""
    BY_SEQ_NUM = 0       # 按序号范围
    BY_CHAPTER_ID = 1    # 按章节ID范围

class GenMode(IntEnum):
    """TTS生成模式"""
    DEFAULT = 0          # 默认模式

class APIEndpoints:
    """API端点定义"""
    BASE_URL = "https://www.gstudios.com.cn"
    
    @staticmethod
    def get_url(path: str, version: APIVersion = APIVersion.V2) -> str:
        """生成完整的API URL"""
        return f"{APIEndpoints.BASE_URL}/{version.value}/api{path}"

    class Auth:
        """认证相关接口"""
        LOGIN = "/auth/login"
        USER_INFO = "/user/info"
        REFRESH_TOKEN = "/auth/refresh"
        PORTAL = "/user/portal"

    class Book:
        """书籍相关接口"""
        LIST = "/content/book/list"
        LIST_EDITOR = "/content/book/list/editor"
        DETAIL = "/content/book/{book_id}"
        PARTNER_LIST = "/content/book/partner/list"
        
    class Chapter:
        """章节相关接口"""
        LIST = "/content/chapter/list"
        SEQ_RANGE = "/content/chapter/list/seq/range"
        CUES_LIST_EDITOR = "/content/chapter/cues/list/editor"

    class Character:
        """角色相关接口"""
        LIST_BOOK = "/content/character/list/book"
        LIST_CHAPTER = "/content/character/list/chapter"

    class Record:
        """录音相关接口"""
        TTS_TRIAL = "/record/tts/trial"
        CHAPTER_CV_INFO = "/record/task/batch/cancel/chapter/cv/info"
        CHAPTER_CV_CANCEL = "/record/task/batch/cancel/chapter/cv"
        TASK_STATE_CHAPTER_CUES = "/record/task/state/chapter/cues"

    class Material:
        """录音材料相关接口"""
        VOICE_COUNT_CHAPTER_CUES = "/material/voice/count/chapter/cues"
        VOICE_LIST_CUE = "/material/voice/list/cue"
        VOICE_DOWNLOAD = "/material/voice/download"
        VOICE_UPLOAD = "/material/voice/upload"

    class Invite:
        """邀请相关接口"""
        CREATE = "/invite/create"
        REDEEM = "/invite/redeem"

@dataclass
class APIResponse:
    """API响应基础结构"""
    code: int
    msg: str
    data: Optional[Any] = None

@dataclass
class PageParams:
    """分页参数"""
    page_no: int
    page_size: int
    total: Optional[int] = None

@dataclass
class ChapterListParams:
    """章节列表查询参数"""
    book_id: str
    page_size: int
    page_no: int
    cv_human_id: Optional[str] = None
    cv_human_relation_type: Optional[int] = None
    content: Optional[str] = None
    query_text: Optional[str] = None
    edition_state: Optional[EditionState] = None
    execution_state: Optional[ExecutionState] = None
    audition_state: Optional[AuditionState] = None
    character_id: Optional[str] = None
    sort_item: Optional[str] = None
    sort_asc: Optional[bool] = None

@dataclass
class ChapterInfo:
    """章节信息"""
    chapter_id: str
    chapter_name: str
    seq_num: int
    edition_state: EditionState
    execution_state: ExecutionState
    audition_state: AuditionState
    content: Optional[str] = None

@dataclass
class ChapterCVInfo:
    """章节CV任务信息"""
    chapter_id: str
    cv_task_id: str
    status: ExecutionState
    execution_state: ExecutionState
    audition_state: AuditionState

@dataclass
class ChapterRangeParams:
    """章节范围查询参数"""
    book_id: str
    seq_num_begin: int
    seq_num_end: int
    range_mode: RangeMode = RangeMode.BY_SEQ_NUM

@dataclass
class TTSTrialParams:
    """TTS试听请求参数"""
    bookId: str
    cvRobotId: int
    ssml: str
    genMode: GenMode = GenMode.DEFAULT
    speedFactor: int = 100
    durationFactorSilence: int = 100
    seed: Optional[int] = None

@dataclass
class VoiceCountChapterCuesParams:
    """章节内容统计查询参数"""
    chapterId: int

@dataclass
class VoiceListCueParams:
    """章节内容片录音列表查询参数"""
    cueId: int
    characterId: int

@dataclass
class ChapterCuesEditorParams:
    """章节编辑器内容列表查询参数"""
    chapterId: int

@dataclass
class VoiceDownloadParams:
    """录音文件下载参数"""
    id: int  # materialId

@dataclass
class VoiceUploadParams:
    """音频文件上传参数"""
    file: Any  # 文件对象或文件路径
    cueId: int
    filename: Optional[str] = None  # 可选的文件名

@dataclass
class VoiceUploadResponse:
    """音频上传响应数据"""
    content: str  # 识别的文本内容
    durationEffectiveMs: int  # 有效时长（毫秒）
    durationTotalMs: int  # 总时长（毫秒）
    leftBlankHeadMs: int  # 头部空白时长（毫秒）
    leftBlankTailMs: int  # 尾部空白时长（毫秒）
    materialId: int  # 生成的材料ID
    snrDb: float  # 信噪比（分贝）

@dataclass
class CharacterListBookParams:
    """获取书籍角色列表参数"""
    book_id: int
    role_type: str = "book"  # 角色类型，默认为book
    skip_stat: bool = True  # 是否跳过统计信息

@dataclass
class CharacterListChapterParams:
    """获取章节角色列表参数"""
    chapter_id: int
    role_type: str = "chapter"  # 角色类型，默认为chapter
    skip_stat: bool = True  # 是否跳过统计信息

@dataclass
class EQConfig:
    """均衡器配置"""
    bp1: Dict[str, Union[int, bool]]  # 带通滤波器1
    bp2: Dict[str, Union[int, bool]]  # 带通滤波器2
    bp3: Dict[str, Union[int, bool]]  # 带通滤波器3
    hs: Dict[str, Union[int, bool]]   # 高频滤波器
    ls: Dict[str, Union[int, bool]]   # 低频滤波器

@dataclass
class CharacterInfo:
    """角色信息"""
    id: int
    name: str
    type: Optional[int]
    gender: Optional[int]
    age_type: Optional[int]
    color: str
    description: str
    nicknames: str
    able_to_merge: bool
    auto_bind: bool
    auto_match: bool
    auto_mix_mode: int
    selected_mix_mode: int
    mix_mode: Optional[int]
    env_mode: int
    env_type: int
    gain_db: int
    presence: bool
    created_time: int
    cv_human_id: Optional[int]
    cv_human_name: str
    cv_human_task: bool
    cv_human_task_num: Optional[int]
    cv_robot_id: Optional[int]
    cv_robot_name: str
    cv_robot_task: bool
    cv_robot_task_num: Optional[int]
    robot_duration_factor_silence: int
    robot_gen_mode: int
    robot_speed_factor: int
    robot_style: str
    robot_style_standard: Optional[str]
    debut_chapter_id: str
    debut_chapter_name: str
    debut_chapter_seq_num: Optional[int]
    num_chapter: Optional[int]
    num_char: Optional[int]
    num_cue: Optional[int]
    eq_config: Optional[EQConfig]

@dataclass
class RecordTaskStateParams:
    """获取章节片段录音任务状态参数"""
    chapter_id: int

@dataclass
class RecordTaskInfo:
    """录音任务信息"""
    been_finished: bool
    character_id: int
    cue_id: int
    cv_id: Optional[int]
    num_char_charged: Optional[int]
    task_id: Optional[int]
    task_state: Optional[int]

@dataclass
class RecordTaskStateResponse:
    """章节片段录音任务状态响应"""
    info_human: List[RecordTaskInfo]
    info_robot: List[RecordTaskInfo]

@dataclass
class BookListEditorParams:
    """书籍列表编辑器查询参数"""
    page_size: int
    page_no: int
    name: Optional[str] = None
    remark: Optional[str] = None
    finished: Optional[bool] = None
    total: Optional[int] = None
    sort_item: Optional[str] = None
    sort_asc: Optional[bool] = None

@dataclass
class BookListEditorResponse:
    """书籍列表编辑器响应数据"""
    list: List[Dict[str, Any]]
    total: int

@dataclass
class BookEditorInfo:
    """书籍编辑器信息（完整字段）"""
    id: int
    name: str
    description: str
    finished: bool
    remark: str
    audition_rate_limit: int
    audition_show_cv: bool
    community_visible: bool
    created_time: int
    updated_time: int
    pinned: bool
    readonly: bool
    team_name: str
    use_master_comp: bool
    use_voice_comp: bool
    use_voice_denoise: bool
    user_can_audit: bool
    user_can_delete: bool
    user_can_edit: bool
    vad_sensitivity: int

@dataclass
class BookInfo:
    """书籍信息"""
    book_id: str
    book_name: str
    price: Optional[float] = None
    description: Optional[str] = None
    
@dataclass
class InviteInfo:
    """邀请信息"""
    book_name: str
    price: float
    redeem_code: str
    relation_type: int

@dataclass
class UserInfo:
    """用户信息"""
    user_id: str
    user_name: str
    user_type: int
    user_role: int
    group_name: Optional[str] = None
    
class DefaultHeaders:
    """默认请求头"""
    @staticmethod
    def get_headers(token: Optional[str] = None) -> Dict[str, str]:
        headers = {
            "host": "www.gstudios.com.cn",
            "connection": "keep-alive",
            "sec-ch-ua-platform": "Windows",
            "x-requested-with": "XMLHttpRequest",
            "user-agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36",
            "accept": "application/json, text/plain, */*",
            "sec-fetch-site": "same-origin",
            "sec-fetch-mode": "cors",
            "sec-fetch-dest": "empty",
            "accept-encoding": "gzip, deflate, br, zstd",
            "accept-language": "zh-CN,zh;q=0.9,en;q=0.8"
        }
        if token:
            headers["authorization"] = f"Bearer {token}"
        return headers

class ErrorCodes:
    """错误码定义"""
    SUCCESS = 1
    UNAUTHORIZED = 401
    FORBIDDEN = 403
    NOT_FOUND = 404
    SERVER_ERROR = 500

# 使用示例:
"""
from gstudio_api import (
    APIEndpoints,
    APIVersion,
    DefaultHeaders,
    ChapterListParams,
    ChapterRangeParams,
    TTSTrialParams,
    VoiceCountChapterCuesParams,
    VoiceListCueParams,
    ChapterCuesEditorParams,
    VoiceDownloadParams,
    VoiceUploadParams,
    VoiceUploadResponse,
    BookListEditorParams,
    BookListEditorResponse,
    BookEditorInfo,
    EditionState,
    ExecutionState,
    AuditionState,
    RangeMode,
    GenMode,
    APIResponse,
    PageParams
)

# 示例1: 获取章节列表
chapter_params = ChapterListParams(
    book_id="12345",
    page_size=50,
    page_no=1,
    edition_state=EditionState.EDITING,
    execution_state=ExecutionState.NOT_PUBLISHED,
    audition_state=AuditionState.NOT_PUBLISHED,
    sort_asc=True
)

chapter_list_url = APIEndpoints.get_url(APIEndpoints.Chapter.LIST)
headers = DefaultHeaders.get_headers(token="your_token_here")

# 示例2: 批量获取章节CV信息
range_params = ChapterRangeParams(
    book_id="12345",
    seq_num_begin=1,
    seq_num_end=20,
    range_mode=RangeMode.BY_SEQ_NUM
)

cv_info_url = APIEndpoints.get_url(APIEndpoints.Record.CHAPTER_CV_INFO)

# 示例3: TTS试听音频生成
tts_params = TTSTrialParams(
    bookId="39726",
    cvRobotId=568,
    ssml="在一道斥责声中，林止陌迷迷糊糊的抬起头，一道模糊的窈窕身影，慢慢的在他眼前变得清晰起来。",
    genMode=GenMode.DEFAULT,
    speedFactor=100,
    durationFactorSilence=100,
    seed=None
)

tts_trial_url = APIEndpoints.get_url(APIEndpoints.Record.TTS_TRIAL)
# 注意：此API返回二进制音频数据(MP3格式)，而非JSON响应

# 示例4: 录音材料相关API
# 4.1 获取章节内容统计
voice_count_params = VoiceCountChapterCuesParams(chapterId=8607068)
voice_count_url = APIEndpoints.get_url(APIEndpoints.Material.VOICE_COUNT_CHAPTER_CUES)

# 4.2 获取章节内容片录音列表
voice_list_params = VoiceListCueParams(cueId=691699346, characterId=5530515)
voice_list_url = APIEndpoints.get_url(APIEndpoints.Material.VOICE_LIST_CUE)

# 4.3 章节编辑器内容列表
chapter_cues_params = ChapterCuesEditorParams(chapterId=8607068)
chapter_cues_url = APIEndpoints.get_url(APIEndpoints.Chapter.CUES_LIST_EDITOR)

# 4.4 下载录音文件
voice_download_params = VoiceDownloadParams(id=305346872)
voice_download_url = APIEndpoints.get_url(APIEndpoints.Material.VOICE_DOWNLOAD)
# 注意：此API返回二进制音频文件(application/octet-stream)，而非JSON响应

# 4.5 上传音频文件
voice_upload_params = VoiceUploadParams(
    file="path/to/audio.mp3",  # 文件路径或文件对象
    cueId=691699346,
    filename="曹先森MK-III.mp3"
)
voice_upload_url = APIEndpoints.get_url(APIEndpoints.Material.VOICE_UPLOAD)
# 注意：此API使用multipart/form-data格式上传文件，返回音频分析结果

# 示例5: 处理分页响应
page_params = PageParams(
    page_no=1,
    page_size=50,
    total=100
)

response = APIResponse(
    code=1,
    msg="success",
    data={
        "list": [
            {
                "chapter_id": "ch001",
                "chapter_name": "第一章",
                "seq_num": 1,
                "edition_state": EditionState.SUBMITTED,
                "execution_state": ExecutionState.RECORDING,
                "audition_state": AuditionState.PENDING_AUDITION
            }
        ],
        "pageNo": page_params.page_no,
        "pageSize": page_params.page_size,
        "total": page_params.total
    }
)

# 示例6: 用户认证流程
portal_url = APIEndpoints.get_url(APIEndpoints.Auth.PORTAL)
auth_response = APIResponse(
    code=1,
    msg="success",
    data={
        "token": "eyJhbGciOiJIUzI1NiIs...",
        "refreshToken": "eyJhbGciOiJIUzI1NiIs...",
        "expiresIn": 7200,
        "userInfo": {
            "userId": "u123",
            "userName": "测试用户",
            "userType": 1,
            "userRole": 2
        }
    }
)

# 示例7: 书籍列表编辑器查询
book_editor_params = BookListEditorParams(
    page_size=50,
    page_no=1,
    name="测试书籍",
    finished=False,
    sort_item="bookName",
    sort_asc=True
)

book_list_editor_url = APIEndpoints.get_url(APIEndpoints.Book.LIST_EDITOR)

# 示例响应处理
book_editor_response = APIResponse(
    code=1,
    msg="成功!",
    data=BookListEditorResponse(
        list=[
            {
                "id": 33964,
                "name": "战国明月",
                "description": "",
                "finished": False,
                "remark": "小南瓜画本",
                "auditionRateLimit": 3,
                "auditionShowCv": False,
                "communityVisible": True,
                "createdTime": 1743654555189,
                "updatedTime": 1743654555254,
                "pinned": True,
                "readonly": False,
                "teamName": "",
                "useMasterComp": True,
                "useVoiceComp": True,
                "useVoiceDenoise": True,
                "userCanAudit": False,
                "userCanDelete": True,
                "userCanEdit": True,
                "vadSensitivity": 0
            }
        ],
        total=10
    )
)

# 示例8: 合作成员列表查询
partner_params = {
    "bookId": "12345",
    "subjectType": "cv"
}
partner_list_url = APIEndpoints.get_url(APIEndpoints.Book.PARTNER_LIST)
partner_response = APIResponse(
    code=1,
    msg="success",
    data={
        "list": [
            {
                "subjectId": 1001,
                "subjectName": "配音演员A",
                "subjectType": 3,
                "status": 1
            }
        ]
    }
)

# 示例9: 错误处理示例
error_response = APIResponse(
    code=ErrorCodes.UNAUTHORIZED,
    msg="Token已过期",
    data=None
)

# 示例10: 不同API版本调用
v1_book_list = APIEndpoints.get_url(
    APIEndpoints.Book.LIST,
    version=APIVersion.V1
)
v2_book_list = APIEndpoints.get_url(
    APIEndpoints.Book.LIST,
    version=APIVersion.V2
)

# 示例11: 完整的请求头构建
headers_with_token = DefaultHeaders.get_headers(
    token="d22e0aabbc6441dea7c113d07d0a4ed9"
)
headers_without_token = DefaultHeaders.get_headers()
"""




