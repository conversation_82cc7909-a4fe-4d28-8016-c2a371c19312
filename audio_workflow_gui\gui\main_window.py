#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
主窗口GUI模块

实现应用程序的主窗口界面，包含多步骤工作流和状态管理。

作者：Augment Agent
版本：1.0.0
"""

import tkinter as tk
from tkinter import ttk, messagebox, filedialog
import threading
from typing import Optional, Dict, Any

from config.settings import AppConfig
from utils.logger import LoggerMixin
from utils.file_manager import FileManager
from gui.book_search import BookSearchFrame
from gui.chapter_manager import ChapterManagerFrame
from gui.paragraph_processor import ParagraphProcessorFrame
from gui.audio_generator import AudioGeneratorFrame
from gui.audio_uploader import AudioUploaderFrame


class MainWindow(LoggerMixin):
    """主窗口类"""
    
    def __init__(self, root: tk.Tk, config: AppConfig):
        """
        初始化主窗口
        
        Args:
            root: Tkinter根窗口
            config: 应用程序配置
        """
        self.root = root
        self.config = config
        self.file_manager = FileManager(config.get('paths.audio_files', 'audio_files'))
        
        # 工作流状态
        self.workflow_state = {
            'selected_book': None,
            'selected_chapter': None,
            'filtered_paragraphs': [],
            'generated_audios': [],
            'upload_results': []
        }

        # 窗体置顶状态
        self.always_on_top = False
        
        self._setup_window()
        self._create_widgets()
        self._setup_layout()
        self._bind_events()

        # 初始化SSL状态
        self.update_ssl_status()

        # 启动时检查SSL问题
        self.check_ssl_on_startup()

        # 在所有组件创建完成后，执行最终的窗口居中
        # 使用增强的居中方法以获得更好的多显示器支持
        self.root.after(500, self._center_window_enhanced)

        self.logger.info("主窗口初始化完成")

    def _center_window(self):
        """将窗口居中显示在屏幕中央"""
        def do_center():
            """执行窗口居中操作"""
            try:
                # 强制更新窗口以获取准确的尺寸
                self.root.update_idletasks()

                # 获取窗口实际尺寸（而不是请求尺寸）
                window_width = self.root.winfo_width()
                window_height = self.root.winfo_height()

                # 如果窗口尺寸为0或过小，使用配置中的默认尺寸
                if window_width <= 1 or window_height <= 1:
                    gui_config = self.config.get_gui_config()
                    window_width = gui_config.get('window_width', 1200)
                    window_height = gui_config.get('window_height', 920)
                    # 设置窗口尺寸
                    self.root.geometry(f"{window_width}x{window_height}")
                    # 再次更新以获取设置后的尺寸
                    self.root.update_idletasks()
                    window_width = self.root.winfo_width() or window_width
                    window_height = self.root.winfo_height() or window_height

                # 获取屏幕尺寸
                screen_width = self.root.winfo_screenwidth()
                screen_height = self.root.winfo_screenheight()

                # 获取可用屏幕区域（考虑任务栏等系统UI元素）
                # 在Windows系统中，任务栏通常占用底部40-50像素
                taskbar_height = 50  # 为任务栏预留空间
                available_height = screen_height - taskbar_height

                # 计算居中位置
                x = (screen_width - window_width) // 2
                y = (available_height - window_height) // 2

                # 边界检查，确保窗口不会超出屏幕可用区域
                x = max(0, min(x, screen_width - window_width))
                y = max(0, min(y, available_height - window_height))

                # 确保窗口不会被放置在屏幕外
                if x < 0:
                    x = 0
                if y < 0:
                    y = 0

                # 设置窗口位置和尺寸
                self.root.geometry(f"{window_width}x{window_height}+{x}+{y}")

                self.logger.debug(f"窗口居中显示: 屏幕尺寸({screen_width}x{screen_height}), "
                                f"可用高度({available_height}), 窗口尺寸({window_width}x{window_height}), "
                                f"位置({x}, {y})")

            except Exception as e:
                self.logger.error(f"窗口居中失败: {e}")
                # 如果居中失败，至少确保窗口可见
                try:
                    gui_config = self.config.get_gui_config()
                    default_width = gui_config.get('window_width', 1200)
                    default_height = gui_config.get('window_height', 920)
                    # 使用安全的默认位置
                    self.root.geometry(f"{default_width}x{default_height}+100+100")
                    self.logger.info(f"使用默认位置显示窗口: {default_width}x{default_height}+100+100")
                except Exception as fallback_error:
                    self.logger.error(f"设置默认窗口位置也失败: {fallback_error}")

        # 延迟执行居中操作，确保窗口完全创建和渲染
        # 使用100ms延迟给窗口足够时间完成初始化
        self.root.after(100, do_center)

    def _center_window_enhanced(self):
        """增强的窗口居中方法，支持多显示器环境"""
        def do_enhanced_center():
            """执行增强的窗口居中操作"""
            try:
                # 强制更新窗口
                self.root.update_idletasks()

                # 获取窗口实际尺寸
                window_width = self.root.winfo_width()
                window_height = self.root.winfo_height()

                # 如果尺寸无效，使用配置默认值
                if window_width <= 1 or window_height <= 1:
                    gui_config = self.config.get_gui_config()
                    window_width = gui_config.get('window_width', 1200)
                    window_height = gui_config.get('window_height', 920)

                # 获取屏幕信息
                screen_width = self.root.winfo_screenwidth()
                screen_height = self.root.winfo_screenheight()

                # 尝试获取鼠标位置来确定当前显示器（多显示器支持）
                try:
                    mouse_x = self.root.winfo_pointerx()
                    mouse_y = self.root.winfo_pointery()

                    # 如果鼠标在屏幕范围内，使用鼠标所在显示器
                    if 0 <= mouse_x <= screen_width and 0 <= mouse_y <= screen_height:
                        # 简单的多显示器处理：假设主显示器
                        target_screen_width = screen_width
                        target_screen_height = screen_height
                        screen_offset_x = 0
                        screen_offset_y = 0
                    else:
                        # 使用主显示器
                        target_screen_width = screen_width
                        target_screen_height = screen_height
                        screen_offset_x = 0
                        screen_offset_y = 0
                except:
                    # 如果获取鼠标位置失败，使用主显示器
                    target_screen_width = screen_width
                    target_screen_height = screen_height
                    screen_offset_x = 0
                    screen_offset_y = 0

                # 考虑系统UI元素（任务栏、菜单栏等）
                taskbar_height = 50
                title_bar_height = 30
                available_width = target_screen_width
                available_height = target_screen_height - taskbar_height - title_bar_height

                # 计算居中位置
                x = screen_offset_x + (available_width - window_width) // 2
                y = screen_offset_y + (available_height - window_height) // 2

                # 边界检查和调整
                min_x = screen_offset_x
                max_x = screen_offset_x + target_screen_width - window_width
                min_y = screen_offset_y
                max_y = screen_offset_y + target_screen_height - window_height - taskbar_height

                x = max(min_x, min(x, max_x))
                y = max(min_y, min(y, max_y))

                # 设置窗口位置
                self.root.geometry(f"{window_width}x{window_height}+{x}+{y}")

                self.logger.debug(f"增强窗口居中: 目标屏幕({target_screen_width}x{target_screen_height}), "
                                f"窗口({window_width}x{window_height}), 最终位置({x}, {y})")

            except Exception as e:
                self.logger.error(f"增强窗口居中失败: {e}")
                # 回退到基本居中方法
                self._center_window()

        # 延迟执行，确保所有组件都已创建
        self.root.after(200, do_enhanced_center)
    
    def _setup_window(self):
        """设置窗口属性"""
        gui_config = self.config.get_gui_config()
        
        self.root.title("GStudio 音频工作流处理器 v1.0.0")
        # 精确计算窗体高度以确保状态栏完整显示
        # 考虑工具栏、内容区域、状态栏的总高度需求
        default_height = gui_config.get('window_height', 920)  # 从850增加到920
        self.root.geometry(f"{gui_config.get('window_width', 1200)}x{default_height}")
        
        # 设置窗口图标（如果有的话）
        try:
            # self.root.iconbitmap('icon.ico')
            pass
        except:
            pass
        
        # 设置窗口最小尺寸
        self.root.minsize(800, 600)

        # 优化窗口居中显示逻辑
        self._center_window()
    
    def _create_widgets(self):
        """创建界面组件"""
        # 创建主框架
        self.main_frame = ttk.Frame(self.root)
        
        # 创建菜单栏
        self._create_menu()
        
        # 创建工具栏
        self._create_toolbar()
        
        # 创建状态栏
        self._create_status_bar()
        
        # 创建主要内容区域
        self._create_content_area()
    
    def _create_menu(self):
        """创建菜单栏"""
        menubar = tk.Menu(self.root)
        self.root.config(menu=menubar)
        
        # 文件菜单
        file_menu = tk.Menu(menubar, tearoff=0)
        menubar.add_cascade(label="文件", menu=file_menu)
        file_menu.add_command(label="导出日志", command=self._export_logs)
        file_menu.add_separator()
        file_menu.add_command(label="退出", command=self.root.quit)

        # 设置菜单
        settings_menu = tk.Menu(menubar, tearoff=0)
        menubar.add_cascade(label="设置", menu=settings_menu)
        # API相关设置
        settings_menu.add_command(label="设置API Token", command=self._set_api_token)
        settings_menu.add_command(label="测试API连接", command=self._test_api_connection)
        settings_menu.add_separator()
        # SSL和网络设置
        settings_menu.add_command(label="SSL设置", command=self._open_ssl_settings)
        settings_menu.add_command(label="网络诊断", command=self._run_network_diagnosis)
        settings_menu.add_separator()
        # 应用程序设置
        settings_menu.add_command(label="应用程序设置", command=self._open_app_settings)
        
        # 工具菜单
        tools_menu = tk.Menu(menubar, tearoff=0)
        menubar.add_cascade(label="工具", menu=tools_menu)
        tools_menu.add_command(label="清理临时文件", command=self._cleanup_temp_files)
        tools_menu.add_command(label="存储统计", command=self._show_storage_stats)
        tools_menu.add_separator()
        tools_menu.add_command(label="调试模式设置", command=self._debug_mode_settings)
        
        # 帮助菜单
        help_menu = tk.Menu(menubar, tearoff=0)
        menubar.add_cascade(label="帮助", menu=help_menu)
        help_menu.add_command(label="使用说明", command=self._show_help)
        help_menu.add_command(label="关于", command=self._show_about)
    
    def _create_toolbar(self):
        """创建工具栏"""
        self.toolbar = ttk.Frame(self.main_frame)
        
        # 移除API连接状态和SSL状态指示器（将移动到状态栏）
        
        # 当前步骤指示器
        self.step_label = ttk.Label(self.toolbar, text="当前步骤: 1/5 - 书籍搜索")
        self.step_label.pack(side=tk.LEFT, padx=5)

        # 已选择书籍信息显示
        self.selected_book_label = ttk.Label(self.toolbar, text="", foreground="blue")
        self.selected_book_label.pack(side=tk.LEFT, padx=(10, 5))

        # 已选择章节信息显示
        self.selected_chapter_label = ttk.Label(self.toolbar, text="", foreground="green")
        self.selected_chapter_label.pack(side=tk.LEFT, padx=(5, 5))

        # 待处理段落信息显示
        self.paragraph_info_label = ttk.Label(self.toolbar, text="", foreground="purple")
        self.paragraph_info_label.pack(side=tk.LEFT, padx=(5, 5))
        
        # 窗体置顶按钮（放在右侧区域）
        self.always_on_top_button = ttk.Button(
            self.toolbar,
            text="📌",  # 使用图钉图标
            width=3,
            command=self._toggle_always_on_top
        )
        self.always_on_top_button.pack(side=tk.RIGHT, padx=5)
    
    def _create_status_bar(self):
        """创建状态栏"""
        self.status_bar = ttk.Frame(self.main_frame)

        # 状态文本
        self.status_var = tk.StringVar()
        self.status_var.set("就绪")
        self.status_label = ttk.Label(self.status_bar, textvariable=self.status_var)
        self.status_label.pack(side=tk.LEFT, padx=5)

        # 时间标签
        self.time_label = ttk.Label(self.status_bar, text="")
        self.time_label.pack(side=tk.RIGHT, padx=5)

        # SSL状态指示器（在时间标签左侧）
        self.ssl_label = ttk.Label(self.status_bar, text="SSL: 启用", foreground="green")
        self.ssl_label.pack(side=tk.RIGHT, padx=5)

        # API连接状态指示器（在SSL状态左侧）
        self.connection_label = ttk.Label(self.status_bar, text="API状态: 未连接", foreground="red")
        self.connection_label.pack(side=tk.RIGHT, padx=5)

        # 更新时间显示
        self._update_time()
    
    def _create_content_area(self):
        """创建主要内容区域"""
        # 创建笔记本控件（标签页）
        self.notebook = ttk.Notebook(self.main_frame)
        
        # 创建各个功能模块的框架
        self.book_search_frame = BookSearchFrame(self.notebook, self)
        self.chapter_manager_frame = ChapterManagerFrame(self.notebook, self)
        self.paragraph_processor_frame = ParagraphProcessorFrame(self.notebook, self)
        self.audio_generator_frame = AudioGeneratorFrame(self.notebook, self)
        self.audio_uploader_frame = AudioUploaderFrame(self.notebook, self)
        
        # 添加标签页
        self.notebook.add(self.book_search_frame, text="1. 书籍搜索")
        self.notebook.add(self.chapter_manager_frame, text="2. 章节管理")
        self.notebook.add(self.paragraph_processor_frame, text="3. 段落处理")
        self.notebook.add(self.audio_generator_frame, text="4. 音频生成")
        self.notebook.add(self.audio_uploader_frame, text="5. 音频上传")
        
        # 绑定标签页切换事件
        self.notebook.bind("<<NotebookTabChanged>>", self._on_tab_changed)
    
    def _setup_layout(self):
        """设置布局"""
        self.main_frame.pack(fill=tk.BOTH, expand=True, padx=5, pady=5)
        self.toolbar.pack(fill=tk.X, pady=(0, 5))
        self.notebook.pack(fill=tk.BOTH, expand=True, pady=(0, 5))
        self.status_bar.pack(fill=tk.X)
    
    def _bind_events(self):
        """绑定事件"""
        # 窗口关闭事件
        self.root.protocol("WM_DELETE_WINDOW", self._on_closing)
        
        # 键盘快捷键
        self.root.bind("<Control-q>", lambda e: self.root.quit())
        self.root.bind("<F1>", lambda e: self._show_help())
    
    def _on_tab_changed(self, event):
        """标签页切换事件处理"""
        selected_tab = self.notebook.index(self.notebook.select())
        step_names = [
            "书籍搜索", "章节管理", "段落处理", "音频生成", "音频上传"
        ]
        
        if 0 <= selected_tab < len(step_names):
            self.step_label.config(text=f"当前步骤: {selected_tab + 1}/5 - {step_names[selected_tab]}")
    
    def _update_time(self):
        """更新时间显示"""
        import datetime
        current_time = datetime.datetime.now().strftime("%Y-%m-%d %H:%M:%S")
        self.time_label.config(text=current_time)
        self.root.after(1000, self._update_time)
    
    def _on_closing(self):
        """窗口关闭事件处理"""
        if messagebox.askokcancel("退出", "确定要退出应用程序吗？"):
            self.logger.info("应用程序正在关闭")
            self.root.destroy()
    
    # 菜单事件处理方法
    def _set_api_token(self):
        """设置API Token"""
        from tkinter import simpledialog
        
        current_token = self.config.get('api.token', '')
        token = simpledialog.askstring(
            "设置API Token",
            "请输入API Token:",
            initialvalue=current_token,
            show='*'
        )
        
        if token:
            self.config.set('api.token', token)
            self.config.save_config()
            messagebox.showinfo("成功", "API Token已设置")
            self.logger.info("API Token已更新")
    
    def _export_logs(self):
        """导出日志"""
        filename = filedialog.asksaveasfilename(
            title="导出日志",
            defaultextension=".txt",
            filetypes=[("文本文件", "*.txt"), ("所有文件", "*.*")]
        )
        
        if filename:
            try:
                # 这里可以实现日志导出逻辑
                messagebox.showinfo("成功", f"日志已导出到: {filename}")
            except Exception as e:
                messagebox.showerror("错误", f"导出日志失败: {e}")
    
    def _cleanup_temp_files(self):
        """清理临时文件"""
        try:
            count = self.file_manager.cleanup_temp_files()
            messagebox.showinfo("完成", f"已清理 {count} 个临时文件")
        except Exception as e:
            messagebox.showerror("错误", f"清理临时文件失败: {e}")
    
    def _show_storage_stats(self):
        """显示存储统计"""
        try:
            stats = self.file_manager.get_storage_stats()

            message = "存储统计信息:\n\n"
            for category, info in stats.items():
                message += f"{category}:\n"
                message += f"  文件数量: {info['file_count']}\n"
                message += f"  总大小: {info['total_size_mb']} MB\n\n"

            messagebox.showinfo("存储统计", message)
        except Exception as e:
            messagebox.showerror("错误", f"获取存储统计失败: {e}")

    def _debug_mode_settings(self):
        """调试模式设置"""
        # 创建调试设置窗口
        debug_window = tk.Toplevel(self.root)
        debug_window.title("调试模式设置")
        debug_window.geometry("500x400")
        debug_window.transient(self.root)
        debug_window.grab_set()

        # 主框架
        main_frame = ttk.Frame(debug_window)
        main_frame.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)

        # 当前设置显示
        current_frame = ttk.LabelFrame(main_frame, text="当前设置", padding=10)
        current_frame.pack(fill=tk.X, pady=(0, 10))

        current_level = self.config.get('logging.level', 'INFO')
        ttk.Label(current_frame, text=f"当前日志级别: {current_level}").pack(anchor=tk.W)

        debug_enabled = current_level.upper() == 'DEBUG'
        status_text = "已启用" if debug_enabled else "已禁用"
        ttk.Label(current_frame, text=f"调试模式: {status_text}").pack(anchor=tk.W)

        # 日志级别设置
        level_frame = ttk.LabelFrame(main_frame, text="日志级别设置", padding=10)
        level_frame.pack(fill=tk.X, pady=(0, 10))

        level_var = tk.StringVar(value=current_level)

        levels = [
            ("DEBUG", "调试模式 - 显示详细的调试信息"),
            ("INFO", "信息模式 - 显示一般信息"),
            ("WARNING", "警告模式 - 只显示警告和错误"),
            ("ERROR", "错误模式 - 只显示错误信息")
        ]

        for level, description in levels:
            frame = ttk.Frame(level_frame)
            frame.pack(fill=tk.X, pady=2)

            ttk.Radiobutton(
                frame,
                text=level,
                variable=level_var,
                value=level
            ).pack(side=tk.LEFT)

            ttk.Label(frame, text=description, foreground="gray").pack(side=tk.LEFT, padx=(10, 0))

        # 调试功能说明
        info_frame = ttk.LabelFrame(main_frame, text="调试模式功能", padding=10)
        info_frame.pack(fill=tk.BOTH, expand=True, pady=(0, 10))

        info_text = tk.Text(info_frame, wrap=tk.WORD, height=8)
        info_scrollbar = ttk.Scrollbar(info_frame, orient=tk.VERTICAL, command=info_text.yview)
        info_text.configure(yscrollcommand=info_scrollbar.set)

        info_text.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        info_scrollbar.pack(side=tk.RIGHT, fill=tk.Y)

        debug_info = """启用调试模式后，应用程序将提供以下详细信息：

HTTP请求详细信息：
• 完整的请求URL、方法、参数
• 请求头信息（API Token会被部分隐藏）
• 请求体内容和文件信息
• 网络连接诊断结果

HTTP响应详细信息：
• 响应状态码、响应头
• 响应体内容（前200字符）
• 响应时间统计

错误诊断信息：
• 详细的错误堆栈信息
• 当前配置参数
• 网络连接状态
• 重试过程详情

注意：调试模式会产生大量日志输出，建议只在需要诊断问题时启用。"""

        info_text.insert(1.0, debug_info)
        info_text.config(state=tk.DISABLED)

        # 按钮框架
        button_frame = ttk.Frame(debug_window)
        button_frame.pack(fill=tk.X, padx=10, pady=(0, 10))

        def apply_settings():
            new_level = level_var.get()
            self.config.set('logging.level', new_level)
            self.config.save_config()

            # 更新日志级别
            import logging
            logging.getLogger().setLevel(getattr(logging, new_level.upper()))

            messagebox.showinfo("设置已保存", f"日志级别已设置为: {new_level}\n\n重启应用程序后完全生效")
            debug_window.destroy()

        ttk.Button(button_frame, text="应用设置", command=apply_settings).pack(side=tk.LEFT)
        ttk.Button(button_frame, text="取消", command=debug_window.destroy).pack(side=tk.RIGHT)
    
    def _show_help(self):
        """显示帮助信息"""
        help_text = """
GStudio 音频工作流处理器使用说明

1. 书籍搜索: 搜索并选择要处理的书籍
2. 章节管理: 选择要处理的章节
3. 段落处理: 筛选旁白+机器人段落
4. 音频生成: 批量生成TTS音频
5. 音频上传: 上传音频到对应段落

快捷键:
- Ctrl+Q: 退出应用程序
- F1: 显示帮助

注意事项:
- 使用前请先设置API Token
- 确保网络连接正常
- 大批量处理时请耐心等待
        """
        
        messagebox.showinfo("使用说明", help_text)
    
    def _show_about(self):
        """显示关于信息"""
        about_text = """
GStudio 音频工作流处理器
版本: 1.0.0
作者: Augment Agent

这是一个用于自动化处理书籍音频的GUI应用程序，
支持书籍搜索、章节管理、段落筛选、音频生成和上传等功能。
        """
        
        messagebox.showinfo("关于", about_text)
    
    # 公共方法
    def update_status(self, message: str):
        """更新状态栏信息"""
        self.status_var.set(message)
        self.root.update_idletasks()

    def update_search_result_status(self, message: str):
        """更新搜索结果状态信息到状态栏"""
        self.status_var.set(message)
        self.root.update_idletasks()
    
    def update_progress(self, value: float, message: str = ""):
        """更新进度（进度条已移除，仅保留兼容性）"""
        # 进度条组件已移除，此方法保留以维持向后兼容性
        # 如果有消息，更新状态栏
        if message:
            self.update_status(message)
    
    def set_connection_status(self, connected: bool):
        """设置API连接状态"""
        if connected:
            self.connection_label.config(text="API状态: 已连接", foreground="green")
        else:
            self.connection_label.config(text="API状态: 未连接", foreground="red")
    
    def get_workflow_state(self) -> Dict[str, Any]:
        """获取工作流状态"""
        return self.workflow_state.copy()
    
    def update_workflow_state(self, key: str, value: Any):
        """更新工作流状态"""
        self.workflow_state[key] = value
        self.logger.debug(f"工作流状态更新: {key} = {value}")

        # 如果更新的是选中的书籍，同时更新工具栏显示
        if key == 'selected_book':
            self._update_selected_book_display(value)
        # 如果更新的是选中的章节，同时更新工具栏显示
        elif key == 'selected_chapter':
            self._update_selected_chapter_display(value)
    
    def next_step(self):
        """切换到下一步"""
        current_tab = self.notebook.index(self.notebook.select())
        if current_tab < self.notebook.index("end") - 1:
            self.notebook.select(current_tab + 1)

    def get_selected_character_info(self) -> Optional[Dict[str, Any]]:
        """获取段落处理器中当前选择的角色信息"""
        try:
            return self.paragraph_processor_frame.get_selected_character_info()
        except Exception as e:
            self.logger.error(f"获取选中角色信息失败: {e}")
            return None

    # SSL相关方法
    def update_ssl_status(self):
        """更新SSL状态显示"""
        if self.config.is_ssl_verification_enabled():
            self.ssl_label.config(text="SSL: 启用", foreground="green")
        else:
            self.ssl_label.config(text="SSL: 禁用", foreground="orange")

    def _open_ssl_settings(self):
        """打开SSL设置对话框"""
        try:
            from gui.ssl_settings_dialog import SSLSettingsDialog

            def on_settings_changed():
                self.update_ssl_status()
                self._refresh_api_clients()

            ssl_dialog = SSLSettingsDialog(self.root, self.config, on_settings_changed)
            ssl_dialog.wait_window()

        except Exception as e:
            messagebox.showerror("错误", f"无法打开SSL设置: {e}")
            self.logger.error(f"打开SSL设置失败: {e}")

    def _run_network_diagnosis(self):
        """运行网络诊断"""
        try:
            from utils.ssl_fix import SSLFixer

            def diagnose():
                try:
                    ssl_fixer = SSLFixer()
                    base_url = self.config.get('api.base_url', 'https://www.gstudios.com.cn')
                    hostname = base_url.replace('https://', '').replace('http://', '').split('/')[0]

                    diagnosis = ssl_fixer.diagnose_ssl_issue(hostname)

                    # 在主线程中显示结果
                    self.root.after(0, self._show_diagnosis_result, diagnosis)

                except Exception as e:
                    self.root.after(0, lambda: messagebox.showerror("诊断失败", f"网络诊断失败: {e}"))

            # 显示进度
            self.update_status("正在运行网络诊断...")
            threading.Thread(target=diagnose, daemon=True).start()

        except Exception as e:
            messagebox.showerror("错误", f"无法运行网络诊断: {e}")
            self.logger.error(f"网络诊断失败: {e}")

    def _show_diagnosis_result(self, diagnosis):
        """显示诊断结果"""
        result_text = f"网络诊断结果:\n\n"
        result_text += f"目标主机: {diagnosis['hostname']}:{diagnosis['port']}\n"
        result_text += f"SSL支持: {'是' if diagnosis['ssl_support'] else '否'}\n"
        result_text += f"证书有效: {'是' if diagnosis['certificate_valid'] else '否'}\n\n"

        if diagnosis.get('errors'):
            result_text += "发现的问题:\n"
            for error in diagnosis['errors']:
                result_text += f"• {error}\n"
            result_text += "\n"

        if diagnosis.get('suggestions'):
            result_text += "建议:\n"
            for suggestion in diagnosis['suggestions']:
                result_text += f"• {suggestion}\n"

        messagebox.showinfo("网络诊断结果", result_text)
        self.update_status("网络诊断完成")

    def _open_app_settings(self):
        """打开应用程序设置（占位符）"""
        messagebox.showinfo("设置", "应用程序设置功能正在开发中...")

    def _refresh_api_clients(self):
        """刷新所有API客户端的SSL设置"""
        try:
            # 通知所有模块刷新API客户端
            if hasattr(self, 'book_search_frame'):
                self.book_search_frame._check_api_client()

            self.logger.info("API客户端SSL设置已刷新")

        except Exception as e:
            self.logger.error(f"刷新API客户端失败: {e}")

    def check_ssl_on_startup(self):
        """启动时检查SSL问题"""
        if not self.config.is_ssl_auto_fix_enabled():
            return

        def check_ssl():
            try:
                from utils.ssl_fix import SSLFixer
                from api.client import GStudioAPIClient

                # 测试SSL连接
                ssl_fixer = SSLFixer()
                base_url = self.config.get('api.base_url', 'https://www.gstudios.com.cn')
                hostname = base_url.replace('https://', '').replace('http://', '').split('/')[0]

                diagnosis = ssl_fixer.diagnose_ssl_issue(hostname)

                if not diagnosis['certificate_valid']:
                    # 尝试API调用以确认问题
                    try:
                        client = GStudioAPIClient(
                            verify_ssl=True,
                            debug_mode=False,
                            request_interval=self.config.get('api.request_interval', 0.1)
                        )
                        token = self.config.get('api.token')
                        if token:
                            client.set_token(token)
                            client.search_books("", page_size=1)
                    except Exception as api_error:
                        if "SSL" in str(api_error):
                            # 在主线程中显示SSL问题对话框
                            self.root.after(0, self._show_ssl_problem_dialog, str(api_error))

            except Exception as e:
                self.logger.debug(f"SSL启动检查失败: {e}")

        # 延迟执行，确保界面完全加载
        self.root.after(2000, lambda: threading.Thread(target=check_ssl, daemon=True).start())

    def _show_ssl_problem_dialog(self, error_message):
        """显示SSL问题对话框"""
        try:
            from gui.ssl_problem_dialog import SSLProblemDialog

            def on_solution_applied(solution_type):
                self.update_ssl_status()
                self._refresh_api_clients()
                self.update_status(f"SSL问题已解决（{solution_type}）")

            ssl_problem_dialog = SSLProblemDialog(
                self.root,
                self.config,
                error_message,
                on_solution_applied
            )
            ssl_problem_dialog.wait_window()

        except Exception as e:
            self.logger.error(f"显示SSL问题对话框失败: {e}")
            # 回退到简单的消息框
            if messagebox.askyesno(
                "SSL连接问题",
                f"检测到SSL证书验证问题:\n{error_message}\n\n"
                "是否要禁用SSL验证以解决此问题？\n"
                "（这会降低安全性，仅适用于测试环境）",
                icon="warning"
            ):
                self.config.disable_ssl_verification()
                self.update_ssl_status()
                self._refresh_api_clients()

    def _test_api_connection(self):
        """测试API连接（从菜单调用）"""
        try:
            from api.client import GStudioAPIClient

            # 检查API Token
            token = self.config.get('api.token')
            if not token:
                messagebox.showwarning(
                    "API Token未设置",
                    "请先在设置菜单中设置API Token，然后再测试连接。"
                )
                return

            # 显示进度
            self.update_status("正在测试API连接...")

            def test_connection():
                try:
                    # 创建API客户端
                    client = GStudioAPIClient(
                        verify_ssl=self.config.is_ssl_verification_enabled(),
                        debug_mode=False,
                        request_interval=self.config.get('api.request_interval', 0.1)
                    )
                    client.set_token(token)

                    # 测试API调用
                    books = client.search_books("", page_size=1)

                    # 在主线程中显示结果
                    self.root.after(0, self._show_api_test_success, len(books))

                except Exception as e:
                    # 在主线程中显示错误
                    self.root.after(0, self._show_api_test_error, str(e))

            # 在后台线程中执行测试
            import threading
            threading.Thread(target=test_connection, daemon=True).start()

        except Exception as e:
            messagebox.showerror("测试失败", f"无法启动API连接测试: {e}")
            self.update_status("API连接测试失败")
            self.logger.error(f"API连接测试启动失败: {e}")

    def _show_api_test_success(self, book_count):
        """显示API测试成功结果"""
        ssl_status = "启用" if self.config.is_ssl_verification_enabled() else "禁用"

        messagebox.showinfo(
            "连接测试成功",
            f"API连接测试成功！\n\n"
            f"连接状态: 正常\n"
            f"SSL验证: {ssl_status}\n"
            f"测试结果: 成功获取到书籍数据\n"
            f"API响应: 正常"
        )

        self.update_status("API连接测试成功")
        self.logger.info("API连接测试成功")

    def _show_api_test_error(self, error_message):
        """显示API测试错误结果"""
        # 分析错误类型并提供建议
        suggestions = []

        if "SSL" in error_message:
            suggestions.append("• 尝试在SSL设置中禁用SSL验证")
            suggestions.append("• 运行网络诊断检查SSL证书问题")

        if "token" in error_message.lower() or "unauthorized" in error_message.lower():
            suggestions.append("• 检查API Token是否正确设置")
            suggestions.append("• 确认API Token是否有效且未过期")

        if "timeout" in error_message.lower() or "connection" in error_message.lower():
            suggestions.append("• 检查网络连接是否正常")
            suggestions.append("• 检查防火墙或代理设置")

        if not suggestions:
            suggestions.append("• 运行网络诊断获取详细信息")
            suggestions.append("• 检查API服务器状态")

        suggestion_text = "\n".join(suggestions)

        result = messagebox.askyesno(
            "连接测试失败",
            f"API连接测试失败:\n\n"
            f"错误信息: {error_message}\n\n"
            f"建议解决方案:\n{suggestion_text}\n\n"
            f"是否要打开网络诊断？",
            icon="error"
        )

        if result:
            self._run_network_diagnosis()

        self.update_status("API连接测试失败")
        self.logger.error(f"API连接测试失败: {error_message}")

    def _toggle_always_on_top(self):
        """切换窗体置顶状态"""
        self.always_on_top = not self.always_on_top
        self.root.attributes('-topmost', self.always_on_top)

        # 更新按钮的视觉状态指示
        if self.always_on_top:
            # 启用置顶时：使用实心图钉，设置不同的样式
            self.always_on_top_button.config(text="📍")  # 实心图钉
            # 尝试设置按钮背景色（某些主题可能不支持）
            try:
                self.always_on_top_button.config(style="Pressed.TButton")
            except:
                pass
        else:
            # 禁用置顶时：使用空心图钉
            self.always_on_top_button.config(text="📌")  # 空心图钉
            try:
                self.always_on_top_button.config(style="TButton")
            except:
                pass

        # 更新状态栏信息
        status_text = "窗体置顶已启用" if self.always_on_top else "窗体置顶已禁用"
        self.update_status(status_text)

        self.logger.info(f"窗体置顶状态: {'启用' if self.always_on_top else '禁用'}")

    def _update_selected_book_display(self, book: Optional[Dict[str, Any]]):
        """更新工具栏中已选择书籍的显示"""
        if book:
            # 获取书籍名称，使用兼容的字段获取方法
            from utils.field_utils import get_book_field
            book_name = get_book_field(book, 'name') or '未知书籍'

            # 限制显示长度，避免工具栏过于拥挤
            if len(book_name) > 20:
                book_name = book_name[:17] + "..."

            display_text = f"书籍：《{book_name}》"
            self.selected_book_label.config(text=display_text)
        else:
            # 没有选择书籍时不显示
            self.selected_book_label.config(text="")

    def _update_selected_chapter_display(self, chapter: Optional[Dict[str, Any]]):
        """更新工具栏中已选择章节的显示"""
        if chapter:
            # 获取章节名称
            chapter_name = chapter.get('chapterName', '未知章节')

            # 限制显示长度，避免工具栏过于拥挤
            if len(chapter_name) > 15:
                chapter_name = chapter_name[:12] + "..."

            display_text = f"章节：《{chapter_name}》"
            self.selected_chapter_label.config(text=display_text)
        else:
            # 没有选择章节时不显示
            self.selected_chapter_label.config(text="")

    def update_paragraph_info_display(self, paragraph_count: int, total_chars: int):
        """更新工具栏中待处理段落信息的显示"""
        if paragraph_count > 0:
            # 计算预估处理时间（假设每分钟处理500字符）
            estimated_minutes = max(1, total_chars // 500)

            # 限制显示长度，避免工具栏过于拥挤
            display_text = f"段落：{paragraph_count} 字符数：{total_chars} 预计时间：{estimated_minutes}分钟"
            if len(display_text) > 40:
                display_text = f"段落：{paragraph_count} 字符：{total_chars} 时间：{estimated_minutes}分"

            self.paragraph_info_label.config(text=display_text)
        else:
            # 没有筛选结果时不显示
            self.paragraph_info_label.config(text="")
