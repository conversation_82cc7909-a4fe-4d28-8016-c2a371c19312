#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
API端点测试脚本

测试API端点是否可访问，不需要有效的token。

作者：Augment Agent
版本：1.0.0
"""

import sys
import os
import requests

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from api.models import APIEndpoints, DefaultHeaders


def test_endpoint_accessibility(url: str, endpoint_name: str) -> bool:
    """
    测试端点是否可访问
    
    Args:
        url: 端点URL
        endpoint_name: 端点名称
        
    Returns:
        bool: 可访问返回True
    """
    try:
        # 使用HEAD请求测试端点是否存在
        headers = DefaultHeaders.get_headers()
        response = requests.head(url, headers=headers, timeout=10)
        
        # 404表示端点不存在，其他状态码（如401未授权）表示端点存在
        if response.status_code == 404:
            print(f"✗ {endpoint_name}: 端点不存在 (404)")
            return False
        else:
            print(f"✓ {endpoint_name}: 端点存在 (状态码: {response.status_code})")
            return True
            
    except requests.exceptions.RequestException as e:
        print(f"✗ {endpoint_name}: 网络错误 - {e}")
        return False


def main():
    """主测试函数"""
    print("GStudio API端点可访问性测试")
    print("=" * 50)
    print("注意: 此测试不需要有效的API Token")
    print("我们只测试端点是否存在，不测试实际功能")
    print()
    
    # 测试的端点列表
    endpoints_to_test = [
        (APIEndpoints.Book.LIST, "书籍列表"),
        (APIEndpoints.Chapter.LIST, "章节列表"),
        (APIEndpoints.Chapter.CUES_LIST_EDITOR, "章节内容编辑器"),
        (APIEndpoints.Record.TTS_TRIAL, "TTS试听"),
        (APIEndpoints.Material.VOICE_UPLOAD, "音频上传"),
        (APIEndpoints.Auth.PORTAL, "用户门户"),
    ]
    
    passed = 0
    total = len(endpoints_to_test)
    
    for endpoint_path, endpoint_name in endpoints_to_test:
        full_url = APIEndpoints.get_url(endpoint_path)
        print(f"测试: {endpoint_name}")
        print(f"URL: {full_url}")
        
        if test_endpoint_accessibility(full_url, endpoint_name):
            passed += 1
        
        print()
    
    print("=" * 50)
    print(f"测试结果: {passed}/{total} 个端点可访问")
    
    if passed == total:
        print("✓ 所有API端点都可访问")
        print("现在您可以设置有效的API Token来使用应用程序")
    elif passed > 0:
        print("⚠ 部分API端点可访问")
        print("请检查不可访问的端点是否正确")
    else:
        print("✗ 所有API端点都不可访问")
        print("请检查:")
        print("1. 网络连接是否正常")
        print("2. API服务器是否在线")
        print("3. API端点URL是否正确")
    
    return passed == total


if __name__ == "__main__":
    success = main()
    input("\n按回车键退出...")
    sys.exit(0 if success else 1)
